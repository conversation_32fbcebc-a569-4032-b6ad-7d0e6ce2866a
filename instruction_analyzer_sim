#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_0000024e68931100 .scope module, "instruction_analyzer" "instruction_analyzer" 2 2;
 .timescale 0 0;
v0000024e6892ef20_0 .var "ace_op1", 6 0;
v0000024e688e9e10_0 .var "ace_op2", 6 0;
v0000024e6892e6e0_0 .var "instruction", 63 0;
v0000024e68941290_0 .var "lsuop", 1 0;
v0000024e688e7fe0_0 .var "memuop", 5 0;
v0000024e68903380_0 .var "offseten", 0 0;
v0000024e68942470_0 .var "rmten", 0 0;
v0000024e68942510_0 .var "rs1", 4 0;
v0000024e6892c100_0 .var "rs3", 4 0;
v0000024e6892c1a0_0 .var "td", 7 0;
v0000024e6892c240_0 .var "tuop1", 2 0;
v0000024e688ead90_0 .var "tuop2", 2 0;
v0000024e688eae30_0 .var "word1", 31 0;
v0000024e688eaed0_0 .var "word2", 31 0;
    .scope S_0000024e68931100;
T_0 ;
    %pushi/vec4 2147717243, 0, 32;
    %concati/vec4 2181063803, 0, 32;
    %store/vec4 v0000024e6892e6e0_0, 0, 64;
    %load/vec4 v0000024e6892e6e0_0;
    %parti/s 32, 0, 2;
    %store/vec4 v0000024e688eae30_0, 0, 32;
    %load/vec4 v0000024e6892e6e0_0;
    %parti/s 32, 32, 7;
    %store/vec4 v0000024e688eaed0_0, 0, 32;
    %vpi_call 2 20 "$display", "=== Instruction Analysis: 0x%016x ===", v0000024e6892e6e0_0 {0 0 0};
    %vpi_call 2 21 "$display", "Word1 (bits 31:0):  0x%08x", v0000024e688eae30_0 {0 0 0};
    %vpi_call 2 22 "$display", "Word2 (bits 63:32): 0x%08x", v0000024e688eaed0_0 {0 0 0};
    %load/vec4 v0000024e688eae30_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000024e6892ef20_0, 0, 7;
    %load/vec4 v0000024e688eae30_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000024e6892c240_0, 0, 3;
    %load/vec4 v0000024e688eae30_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0000024e68941290_0, 0, 2;
    %load/vec4 v0000024e688eae30_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0000024e688e7fe0_0, 0, 6;
    %load/vec4 v0000024e688eae30_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0000024e6892c100_0, 0, 5;
    %vpi_call 2 31 "$display", "\012=== First Word Analysis ===" {0 0 0};
    %vpi_call 2 32 "$display", "ACE_OP[6:0]:   %b (%d)", v0000024e6892ef20_0, v0000024e6892ef20_0 {0 0 0};
    %vpi_call 2 33 "$display", "TUOP[14:12]:   %b (%d)", v0000024e6892c240_0, v0000024e6892c240_0 {0 0 0};
    %vpi_call 2 34 "$display", "LSUOP[11:10]:  %b (%d)", v0000024e68941290_0, v0000024e68941290_0 {0 0 0};
    %vpi_call 2 35 "$display", "MEMUOP[30:25]: %b (%d)", v0000024e688e7fe0_0, v0000024e688e7fe0_0 {0 0 0};
    %vpi_call 2 36 "$display", "RS3[24:20]:    %b (%d)", v0000024e6892c100_0, v0000024e6892c100_0 {0 0 0};
    %load/vec4 v0000024e688eaed0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000024e688e9e10_0, 0, 7;
    %load/vec4 v0000024e688eaed0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000024e688ead90_0, 0, 3;
    %load/vec4 v0000024e688eaed0_0;
    %parti/s 8, 23, 6;
    %store/vec4 v0000024e6892c1a0_0, 0, 8;
    %load/vec4 v0000024e688eaed0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0000024e68942510_0, 0, 5;
    %load/vec4 v0000024e688eaed0_0;
    %parti/s 1, 15, 5;
    %store/vec4 v0000024e68903380_0, 0, 1;
    %load/vec4 v0000024e688eaed0_0;
    %parti/s 1, 20, 6;
    %store/vec4 v0000024e68942470_0, 0, 1;
    %vpi_call 2 46 "$display", "\012=== Second Word Analysis ===" {0 0 0};
    %vpi_call 2 47 "$display", "ACE_OP[6:0]:   %b (%d)", v0000024e688e9e10_0, v0000024e688e9e10_0 {0 0 0};
    %vpi_call 2 48 "$display", "TUOP[14:12]:   %b (%d)", v0000024e688ead90_0, v0000024e688ead90_0 {0 0 0};
    %vpi_call 2 49 "$display", "TD[30:23]:     %b (%d)", v0000024e6892c1a0_0, v0000024e6892c1a0_0 {0 0 0};
    %vpi_call 2 50 "$display", "RS1[19:15]:    %b (%d)", v0000024e68942510_0, v0000024e68942510_0 {0 0 0};
    %vpi_call 2 51 "$display", "OFFSETEN[15]:  %b (%d)", v0000024e68903380_0, v0000024e68903380_0 {0 0 0};
    %vpi_call 2 52 "$display", "RMTEN[20]:     %b (%d)", v0000024e68942470_0, v0000024e68942470_0 {0 0 0};
    %vpi_call 2 55 "$display", "\012=== Expected: tld.trr.mx58.share T0, (t2), zero ===" {0 0 0};
    %vpi_call 2 56 "$display", "- Should be a tld (load) instruction" {0 0 0};
    %vpi_call 2 57 "$display", "- Should be trr format (tile-register-register)" {0 0 0};
    %vpi_call 2 58 "$display", "- Should have mx58 modifier" {0 0 0};
    %vpi_call 2 59 "$display", "- Should be share memory space" {0 0 0};
    %vpi_call 2 60 "$display", "- TD should be T0 (0)" {0 0 0};
    %vpi_call 2 61 "$display", "- Should involve t2 register" {0 0 0};
    %vpi_call 2 62 "$display", "- Should involve zero register" {0 0 0};
    %vpi_call 2 65 "$display", "\012=== Current Decoding Analysis ===" {0 0 0};
    %load/vec4 v0000024e6892ef20_0;
    %cmpi/e 123, 0, 7;
    %jmp/0xz  T_0.0, 4;
    %vpi_call 2 67 "$display", "\342\234\223 ACE_OP matches tile instruction" {0 0 0};
    %load/vec4 v0000024e6892c240_0;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_0.2, 6;
    %vpi_call 2 92 "$display", "\342\234\227 TUOP1 = %b (not 110)", v0000024e6892c240_0 {0 0 0};
    %jmp T_0.4;
T_0.2 ;
    %vpi_call 2 71 "$display", "\342\234\223 TUOP1 = 110 (multi-lane memory instruction)" {0 0 0};
    %load/vec4 v0000024e688e7fe0_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 6;
    %cmp/u;
    %jmp/1 T_0.5, 6;
    %vpi_call 2 87 "$display", "\342\234\227 MEMUOP = %b (not block operations)", v0000024e688e7fe0_0 {0 0 0};
    %jmp T_0.7;
T_0.5 ;
    %vpi_call 2 75 "$display", "\342\234\223 MEMUOP = 000001 (block memory operations)" {0 0 0};
    %load/vec4 v0000024e688e9e10_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.10, 4;
    %load/vec4 v0000024e688ead90_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.10;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.8, 8;
    %vpi_call 2 78 "$display", "\342\234\223 Second word: ACE_OP=1111011, TUOP2=001" {0 0 0};
    %vpi_call 2 79 "$display", "\342\206\222 This should be tld.trr.blk.* instruction" {0 0 0};
    %jmp T_0.9;
T_0.8 ;
    %vpi_call 2 81 "$display", "\342\234\227 Second word format mismatch" {0 0 0};
    %vpi_call 2 82 "$display", "  Expected: ACE_OP=1111011, TUOP2=001" {0 0 0};
    %vpi_call 2 83 "$display", "  Actual: ACE_OP=%b, TUOP2=%b", v0000024e688e9e10_0, v0000024e688ead90_0 {0 0 0};
T_0.9 ;
    %jmp T_0.7;
T_0.7 ;
    %pop/vec4 1;
    %jmp T_0.4;
T_0.4 ;
    %pop/vec4 1;
    %jmp T_0.1;
T_0.0 ;
    %vpi_call 2 96 "$display", "\342\234\227 ACE_OP = %b (not tile instruction)", v0000024e6892ef20_0 {0 0 0};
T_0.1 ;
    %vpi_call 2 99 "$display", "\012=== Problem Identification ===" {0 0 0};
    %vpi_call 2 100 "$display", "The issue is likely in the instruction name extraction logic." {0 0 0};
    %vpi_call 2 101 "$display", "Current decoder returns generic 'tld_64bit' instead of specific format." {0 0 0};
    %vpi_call 2 102 "$display", "Need to enhance extract_instruction_name() for detailed tld.trr.* decoding." {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "instruction_analyzer.sv";
