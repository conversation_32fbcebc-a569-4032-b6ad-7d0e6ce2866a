#!/usr/bin/env python3
"""
重新分析 tld.trii.linear.u32.share 指令编码
基于找到的文档进行正确解析
"""

def analyze_correct_tld_share():
    # Test instruction: 0x802e107b0000607b (64-bit)
    instr_val = 0x802e107b0000607b
    
    print("=== 重新分析 tld.trii.linear.u32.share 指令 ===")
    print(f"指令编码: 0x{instr_val:016x}")
    print()
    
    # 按32位字节序分解 (64位指令)
    word1 = instr_val & 0xFFFFFFFF
    word2 = (instr_val >> 32) & 0xFFFFFFFF
    print(f"第一个32位字: 0x{word1:08x}")
    print(f"第二个32位字: 0x{word2:08x}")
    print()
    
    # 根据文档，这是一个多通道指令：
    # 第一个字: tuop=110 (第一通道)
    # 第二个字: tuop=001 (share memory)
    
    # 分析第一个字的关键字段
    ace_op = word1 & 0x7F
    tuop1 = (word1 >> 12) & 0x7
    memuop = (word1 >> 25) & 0x3F
    lsuop = (word1 >> 10) & 0x3
    offseten_bit = word1 & (1 << 31)  # bit 31, 但实际上offseten应该在第二个字
    
    print("=== 第一个字段分析 ===")
    print(f"ACE_OP[6:0]:   {ace_op:07b} (0x{ace_op:02x}) = {ace_op}")
    print(f"TUOP[14:12]:   {tuop1:03b} ({tuop1}) -> tuop_110 (多通道指令第一字)")
    print(f"MEMUOP[30:25]: {memuop:06b} ({memuop}) -> load操作")
    print(f"LSUOP[11:10]:  {lsuop:02b} ({lsuop}) -> linear操作")
    
    # 分析第二个字的关键字段
    ace_op2 = word2 & 0x7F
    tuop2 = (word2 >> 12) & 0x7
    offseten = (word2 >> 15) & 0x1
    rmten = (word2 >> 20) & 0x1
    tilesize = (word2 >> 21) & 0x7
    td = (word2 >> 24) & 0xFF
    
    print()
    print("=== 第二个字段分析 ===")
    print(f"ACE_OP[6:0]:   {ace_op2:07b} (0x{ace_op2:02x}) = {ace_op2}")
    print(f"TUOP[14:12]:   {tuop2:03b} ({tuop2}) -> tuop_001 (share memory)")
    print(f"OFFSETEN[15]:  {offseten} -> {'trir' if offseten else 'trii'}")
    print(f"RMTEN[20]:     {rmten} -> {'remote' if rmten else 'local'}")
    print(f"TILESIZE[23:21]: {tilesize:03b} ({tilesize})")
    print(f"TD[31:24]:     {td:08b} ({td})")
    
    print()
    print("=== 指令类型判断 ===")
    is_tile = (ace_op == 0x7B)
    is_dual_lane = (tuop1 == 6)  # tuop_110 indicates multi-lane
    is_load_op = (memuop == 0)
    is_linear = (lsuop == 0)
    is_trii = (offseten == 0)
    is_share = (tuop2 == 1)  # 第二字tuop=001表示share memory
    is_local = (rmten == 0)
    
    print(f"1. 是Tile指令 (ACE_OP == 0x7B): {is_tile}")
    print(f"2. 是多通道指令 (第一字TUOP == 6): {is_dual_lane}")
    print(f"3. 是load操作 (MEMUOP == 0): {is_load_op}")
    print(f"4. 是linear操作 (LSUOP == 0): {is_linear}")
    print(f"5. 是trii格式 (OFFSETEN == 0): {is_trii}")
    print(f"6. 是share memory (第二字TUOP == 1): {is_share}")
    print(f"7. 是local (RMTEN == 0): {is_local}")
    
    # 检查tilesize
    tilesize_name = {0: 'm1', 1: 'm2', 2: 'm4', 3: 'm8', 4: 'mf8', 5: 'mf4', 6: 'mf2'}
    tilesize_str = tilesize_name.get(tilesize, f'm{tilesize}')
    print(f"8. Tile size: {tilesize_str}")
    
    print()
    print("=== 预期结果 ===")
    if all([is_tile, is_dual_lane, is_load_op, is_linear, is_trii, is_share, is_local]):
        expected_name = f"tld.trii.linear.u32.share.{tilesize_str}"
        print(f"✓ 应该识别为: {expected_name}")
        print(f"✓ 操作数: T{td}, (t3), 0, 0")
    else:
        print("✗ 不匹配预期的指令模式")
        
    print()
    print("=== 当前问题分析 ===")
    print("问题1: 当前解码器中tuop_110的处理逻辑只考虑了block memory操作")
    print("问题2: 没有识别tuop_110 + 第二字tuop_001的share memory linear指令")
    print("问题3: extract_instruction_name函数需要增加对这种组合的支持")
    print()
    print("=== 修复建议 ===")
    print("1. 在tuop_110处理中，检查第二字的tuop值")
    print("2. 当tuop_second == 001时，识别为share memory指令")
    print("3. 根据offseten, rmten等字段确定具体的指令变体")

if __name__ == "__main__":
    analyze_correct_tld_share()
