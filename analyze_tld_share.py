#!/usr/bin/env python3
"""
分析 tld.trii.linear.u32.share.m4 指令编码
"""

def analyze_tld_share():
    # Test instruction: 0x802e107b0000607b (64-bit)
    instr_val = 0x802e107b0000607b
    
    print("=== 分析 tld.trii.linear.u32.share.m4 指令 ===")
    print(f"指令编码: 0x{instr_val:016x}")
    print()
    
    # 按32位字节序分解 (64位指令)
    word1 = instr_val & 0xFFFFFFFF
    word2 = (instr_val >> 32) & 0xFFFFFFFF
    print(f"第一个32位字: 0x{word1:08x}")
    print(f"第二个32位字: 0x{word2:08x}")
    print()
    
    # 分析第一个字的关键字段
    ace_op = word1 & 0x7F
    tuop = (word1 >> 12) & 0x7
    memuop = (word1 >> 25) & 0x3F
    lsuop = (word1 >> 10) & 0x3
    
    print("=== 第一个字段分析 ===")
    print(f"ACE_OP[6:0]:   {ace_op:07b} (0x{ace_op:02x}) = {ace_op}")
    print(f"TUOP[14:12]:   {tuop:03b} ({tuop})")
    print(f"MEMUOP[30:25]: {memuop:06b} ({memuop})")
    print(f"LSUOP[11:10]:  {lsuop:02b} ({lsuop})")
    
    # 分析第二个字的关键字段
    tuop2 = (word2 >> 12) & 0x7
    offseten = (word2 >> 15) & 0x1
    rmten = (word2 >> 20) & 0x1
    
    print()
    print("=== 第二个字段分析 ===")
    print(f"第二字TUOP[14:12]: {tuop2:03b} ({tuop2})")
    print(f"OFFSETEN[15]:      {offseten}")
    print(f"RMTEN[20]:         {rmten}")
    
    print()
    print("=== 指令类型判断 ===")
    is_tile = (ace_op == 0x7B)
    is_tuop_001 = (tuop == 1)
    is_load_op = (memuop == 0)
    is_linear = (lsuop == 0)
    is_trii = (offseten == 0)
    is_share = (tuop2 == 1)  # 第二字tuop=001表示share memory
    
    print(f"1. 是Tile指令 (ACE_OP == 0x7B): {is_tile}")
    print(f"2. 是tuop_001: {is_tuop_001}")
    print(f"3. 是load操作 (MEMUOP == 0): {is_load_op}")
    print(f"4. 是linear操作 (LSUOP == 0): {is_linear}")
    print(f"5. 是trii格式 (OFFSETEN == 0): {is_trii}")
    print(f"6. 是share memory (第二字TUOP == 1): {is_share}")
    print(f"7. 是local (RMTEN == 0): {rmten == 0}")
    
    print()
    print("=== 预期结果 ===")
    if all([is_tile, is_tuop_001, is_load_op, is_linear, is_trii, is_share]):
        print("✓ 应该识别为: tld.trii.linear.u32.share")
        if rmten == 0:
            print("✓ 完整指令: tld.trii.linear.u32.share (local)")
        else:
            print("✓ 完整指令: tld.trii.linear.u32.share.remote")
    else:
        print("✗ 不匹配预期的指令模式")
        
    print()
    print("=== 当前问题 ===")
    print("当前被识别为: tld_64bit (通用64位指令)")
    print("应该识别为: tld.trii.linear.u32.share")
    print("问题: 在extract_instruction_name函数中tuop_001的处理逻辑不完整")

if __name__ == "__main__":
    analyze_tld_share()
