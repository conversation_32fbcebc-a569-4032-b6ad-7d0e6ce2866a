#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2009.vpi";
S_000001c40fa4a7f0 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_000001c40fa4ad80 .scope module, "verify_bits" "verify_bits" 3 1;
 .timescale 0 0;
v000001c40fad33e0_0 .var "instruction", 63 0;
v000001c40fa4af10_0 .var "word1", 31 0;
v000001c40faf8410_0 .var "word2", 31 0;
    .scope S_000001c40fa4ad80;
T_0 ;
    %pushi/vec4 2663645817, 0, 54;
    %concati/vec4 251, 0, 10;
    %store/vec4 v000001c40fad33e0_0, 0, 64;
    %load/vec4 v000001c40fad33e0_0;
    %parti/s 32, 0, 2;
    %store/vec4 v000001c40fa4af10_0, 0, 32;
    %load/vec4 v000001c40fad33e0_0;
    %parti/s 32, 32, 7;
    %store/vec4 v000001c40faf8410_0, 0, 32;
    %vpi_call/w 3 10 "$display", "Manual bit analysis:" {0 0 0};
    %vpi_call/w 3 11 "$display", "Word 1: 0x%08x = %032b", v000001c40fa4af10_0, v000001c40fa4af10_0 {0 0 0};
    %vpi_call/w 3 12 "$display", "Word 2: 0x%08x = %032b", v000001c40faf8410_0, v000001c40faf8410_0 {0 0 0};
    %vpi_call/w 3 13 "$display", "\000" {0 0 0};
    %vpi_call/w 3 16 "$display", "Word 1 bit fields:" {0 0 0};
    %vpi_call/w 3 17 "$display", "  [6:0]   ACE_OP: %07b = 0x%02x", &PV<v000001c40fa4af10_0, 0, 7>, &PV<v000001c40fa4af10_0, 0, 7> {0 0 0};
    %vpi_call/w 3 18 "$display", "  [11:10] vecuop1: %02b = %0d", &PV<v000001c40fa4af10_0, 10, 2>, &PV<v000001c40fa4af10_0, 10, 2> {0 0 0};
    %vpi_call/w 3 19 "$display", "  [14:12] tuop: %03b = %0d", &PV<v000001c40fa4af10_0, 12, 3>, &PV<v000001c40fa4af10_0, 12, 3> {0 0 0};
    %vpi_call/w 3 20 "$display", "  [8]     rsen: %b", &PV<v000001c40fa4af10_0, 8, 1> {0 0 0};
    %vpi_call/w 3 21 "$display", "  [9]     immen: %b", &PV<v000001c40fa4af10_0, 9, 1> {0 0 0};
    %vpi_call/w 3 22 "$display", "  [19:15] rs2: %05b = %0d", &PV<v000001c40fa4af10_0, 15, 5>, &PV<v000001c40fa4af10_0, 15, 5> {0 0 0};
    %vpi_call/w 3 23 "$display", "  [24:20] ???: %05b = %0d", &PV<v000001c40fa4af10_0, 20, 5>, &PV<v000001c40fa4af10_0, 20, 5> {0 0 0};
    %vpi_call/w 3 24 "$display", "  [27]    neg1: %b", &PV<v000001c40fa4af10_0, 27, 1> {0 0 0};
    %vpi_call/w 3 25 "$display", "  [28]    neg2: %b", &PV<v000001c40fa4af10_0, 28, 1> {0 0 0};
    %vpi_call/w 3 26 "$display", "\000" {0 0 0};
    %vpi_call/w 3 29 "$display", "Word 2 bit fields:" {0 0 0};
    %vpi_call/w 3 30 "$display", "  [6:0]   ACE_OP: %07b = 0x%02x", &PV<v000001c40faf8410_0, 0, 7>, &PV<v000001c40faf8410_0, 0, 7> {0 0 0};
    %vpi_call/w 3 31 "$display", "  [14:12] tuop: %03b = %0d", &PV<v000001c40faf8410_0, 12, 3>, &PV<v000001c40faf8410_0, 12, 3> {0 0 0};
    %vpi_call/w 3 32 "$display", "  [19:16] vecuop2[5:2]: %04b = %0d", &PV<v000001c40faf8410_0, 16, 4>, &PV<v000001c40faf8410_0, 16, 4> {0 0 0};
    %vpi_call/w 3 33 "$display", "\000" {0 0 0};
    %vpi_call/w 3 36 "$display", "Alternative bit field readings:" {0 0 0};
    %vpi_call/w 3 37 "$display", "  Instruction bits [8]: %b", &PV<v000001c40fad33e0_0, 8, 1> {0 0 0};
    %vpi_call/w 3 38 "$display", "  Instruction bits [9]: %b", &PV<v000001c40fad33e0_0, 9, 1> {0 0 0};
    %vpi_call/w 3 39 "$display", "  Instruction bits [28]: %b", &PV<v000001c40fad33e0_0, 28, 1> {0 0 0};
    %vpi_call/w 3 40 "$display", "  Instruction bits [11:10]: %02b", &PV<v000001c40fad33e0_0, 10, 2> {0 0 0};
    %vpi_call/w 3 41 "$display", "  Instruction bits [51:48]: %04b", &PV<v000001c40fad33e0_0, 48, 4> {0 0 0};
    %vpi_call/w 3 43 "$finish" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "-";
    "verify_bits.sv";
