#!/usr/bin/env python3

instruction = 0x82217b000564fb
print(f"Instruction: 0x{instruction:016x}")

# Split into 32-bit words  
first_word = instruction & 0xffffffff
second_word = (instruction >> 32) & 0xffffffff
print(f"First word:  0x{first_word:08x}")
print(f"Second word: 0x{second_word:08x}")

# Extract fields from first word
ace_op = first_word & 0x7f
tuop_first = (first_word >> 12) & 0x7
vecuop1 = (first_word >> 10) & 0x3
rsen = (first_word >> 9) & 0x1
immen = (first_word >> 8) & 0x1
rs2 = (first_word >> 20) & 0x1f
memuop = (first_word >> 25) & 0x3f

print("\nFirst word fields:")
print(f"ACE_OP: {ace_op:07b} ({ace_op:3d})")
print(f"tuop_first: {tuop_first:03b} ({tuop_first})")
print(f"vecuop1: {vecuop1:02b} ({vecuop1})")
print(f"rsen: {rsen}")
print(f"immen: {immen}")
print(f"rs2: {rs2:05b} ({rs2})")
print(f"memuop: {memuop:06b} ({memuop})")

# Extract fields from second word
ace_op2 = second_word & 0x7f
tuop_second = (second_word >> 12) & 0x7
vecuop2_52 = (second_word >> 16) & 0xf
td = (second_word >> 0) & 0xff
ts1 = (second_word >> 8) & 0xff

print("\nSecond word fields:")
print(f"ACE_OP: {ace_op2:07b} ({ace_op2:3d})")
print(f"tuop_second: {tuop_second:03b} ({tuop_second})")
print(f"vecuop2[5:2]: {vecuop2_52:04b} ({vecuop2_52})")
print(f"td: {td:08b} ({td})")
print(f"ts1: {ts1:08b} ({ts1})")

print("\nInstruction analysis:")
print(f"Expected: tmul.ttr.f32 T{td}, T{ts1}, x{rs2}")
print(f"This should be a tmul operation because vecuop2[5:2] = 1 (0001)")
print(f"tuop_first = {tuop_first} (110) indicates multi-lane memory or ALU")
print(f"tuop_second = {tuop_second} (010) indicates vector ALU operation")
print(f"vecuop1 = {vecuop1} (10) indicates 2-operand ALU")
print(f"rsen = {rsen} indicates scalar register source")
print(f"vecuop2[5:2] = {vecuop2_52} (0001) should indicate MUL operation")
