#!/usr/bin/env python3
"""
Analyze hex instruction 0x8629807b1000607b to understand why it's being decoded incorrectly
"""

def analyze_instruction(instruction_hex):
    print(f"Analyzing instruction: {instruction_hex}")
    
    # Convert hex to 64-bit integer
    instruction = int(instruction_hex, 16)
    
    # Extract 32-bit words (little-endian format)
    word1 = instruction & 0xFFFFFFFF  # Lower 32 bits
    word2 = (instruction >> 32) & 0xFFFFFFFF  # Upper 32 bits
    
    print(f"Word 1 (bits 31:0):  0x{word1:08x}")
    print(f"Word 2 (bits 63:32): 0x{word2:08x}")
    
    # Analyze first word (word1)
    print("\n=== First Word Analysis ===")
    ace_op = word1 & 0x7F  # bits [6:0]
    tuop = (word1 >> 12) & 0x7  # bits [14:12]
    lsuop = (word1 >> 10) & 0x3  # bits [11:10]
    memuop = (word1 >> 25) & 0x3F  # bits [30:25]
    rs3 = (word1 >> 20) & 0x1F  # bits [24:20]
    
    print(f"ACE_OP[6:0]:   0x{ace_op:02x} ({'TILE' if ace_op == 0x7b else 'NOT TILE'})")
    print(f"TUOP[14:12]:   {tuop} (0b{tuop:03b})")
    print(f"LSUOP[11:10]:  {lsuop} (0b{lsuop:02b})")
    print(f"MEMUOP[30:25]: {memuop} (0b{memuop:06b})")
    print(f"RS3[24:20]:    {rs3}")
    
    # Analyze second word (word2)
    print("\n=== Second Word Analysis ===")
    ace_op2 = word2 & 0x7F  # bits [6:0]
    tuop2 = (word2 >> 12) & 0x7  # bits [14:12]
    rs1 = (word2 >> 15) & 0x1F  # bits [19:15]
    td = (word2 >> 23) & 0xFF  # bits [30:23]
    offseten = (word2 >> 15) & 0x1  # bit [15]
    rmten = (word2 >> 20) & 0x1  # bit [20]
    
    print(f"ACE_OP[6:0]:   0x{ace_op2:02x} ({'TILE' if ace_op2 == 0x7b else 'NOT TILE'})")
    print(f"TUOP[14:12]:   {tuop2} (0b{tuop2:03b})")
    print(f"RS1[19:15]:    {rs1}")
    print(f"TD[30:23]:     {td}")
    print(f"OFFSETEN[15]:  {offseten}")
    print(f"RMTEN[20]:     {rmten}")
    
    # Analyze decoding path
    print("\n=== Decoding Path Analysis ===")
    if ace_op == 0x7b:
        print("✓ ACE_OP matches tile instruction")
        if tuop == 6:  # 0b110
            print("✓ TUOP = 110 (multi-lane memory instruction)")
            if memuop == 0:  # 0b000000
                print("✓ MEMUOP = 000000 (standard memory operations)")
                if tuop2 == 1:  # 0b001
                    print("✓ Second word TUOP = 001")
                    print("→ This should be tst.trii.linear.u32.global")
                    
                    # Decode the specific instruction
                    if lsuop == 0:  # 0b00
                        print("✓ LSUOP = 00 (linear operations)")
                        if not offseten and not rmten:
                            print("→ Expected: tst.trii.linear.u32.global")
                        elif not offseten and rmten:
                            print("→ Expected: tst.trii.linear.u32.global.remote")
                        elif offseten and not rmten:
                            print("→ Expected: tst.trir.linear.u32.global")
                        else:
                            print("→ Expected: tst.trir.linear.u32.global.remote")
                    elif lsuop == 1:  # 0b01
                        print("✓ LSUOP = 01 (stride operations)")
                        print("→ Expected: tst.trri.stride.u32.global")
                else:
                    print(f"✗ Second word TUOP = {tuop2} (not 001)")
            elif memuop == 1:  # 0b000001
                print("✓ MEMUOP = 000001 (block memory operations)")
                print("→ This should be tst.trr.blk.* instruction")
            else:
                print(f"✗ MEMUOP = {memuop:06b} (not standard or block)")
        else:
            print(f"✗ TUOP = {tuop} (not 110)")
    else:
        print(f"✗ ACE_OP = 0x{ace_op:02x} (not tile instruction)")
    
    return word1, word2, ace_op, tuop, lsuop, memuop, tuop2

if __name__ == "__main__":
    instruction_hex = "0x8629807b1000607b"
    analyze_instruction(instruction_hex)
