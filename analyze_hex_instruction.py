#!/usr/bin/env python3
"""
Analyze the specific hex instruction 0x82216b000564fb to understand why it's being
decoded as tcos.tt.f32 instead of tmul.ttr.f32
"""

def analyze_instruction(hex_str):
    """Analyze a 64-bit hex instruction"""
    # Remove 0x prefix if present
    if hex_str.startswith('0x'):
        hex_str = hex_str[2:]

    # Convert to integer
    instruction = int(hex_str, 16)

    print(f"Analyzing instruction: 0x{hex_str}")
    print(f"Binary: {instruction:064b}")
    print()

    # Split into two 32-bit words (little-endian)
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF

    print(f"Word 1 (bits 31:0):  0x{word1:08x} = {word1:032b}")
    print(f"Word 2 (bits 63:32): 0x{word2:08x} = {word2:032b}")
    print()

    # Extract key fields from word 1
    ace_op = word1 & 0x7F  # bits 6:0
    rsen = (word1 >> 8) & 0x1  # bit 8
    immen = (word1 >> 9) & 0x1  # bit 9
    vecuop1 = (word1 >> 10) & 0x3  # bits 11:10
    tuop = (word1 >> 12) & 0x7  # bits 14:12
    memuop = (word1 >> 25) & 0x3F  # bits 30:25

    print("Word 1 fields:")
    print(f"  ace_op (6:0):    {ace_op:07b} = 0x{ace_op:02x} = {ace_op}")
    print(f"  rsen (8):        {rsen}")
    print(f"  immen (9):       {immen}")
    print(f"  vecuop1 (11:10): {vecuop1:02b} = {vecuop1}")
    print(f"  tuop (14:12):    {tuop:03b} = {tuop}")
    print(f"  memuop (30:25):  {memuop:06b} = {memuop}")

    # Check if this is a tile instruction
    TILE_ACE_OP = 0x7B  # 1111011
    if ace_op == TILE_ACE_OP:
        print(f"  ✓ This is a tile instruction (ace_op = {TILE_ACE_OP:07b})")
    else:
        print(f"  ✗ Not a tile instruction (expected {TILE_ACE_OP:07b})")
        return

    print()

    # Extract fields from word 2
    ace_op2 = word2 & 0x7F  # bits 6:0
    tuop2 = (word2 >> 12) & 0x7  # bits 14:12
    vecuop2_52 = (word2 >> 16) & 0xF  # bits 19:16 in word 2 = vecuop2[5:2]
    vecuop2_10 = (word2 >> 16) & 0x3  # bits 17:16 in word 2 = vecuop2[1:0]

    print("Word 2 fields:")
    print(f"  ace_op2 (6:0):       {ace_op2:07b} = 0x{ace_op2:02x}")
    print(f"  tuop2 (14:12):       {tuop2:03b} = {tuop2}")
    print(f"  vecuop2[5:2] (19:16): {vecuop2_52:04b} = {vecuop2_52}")
    print(f"  vecuop2[1:0] (17:16): {vecuop2_10:02b} = {vecuop2_10}")
    print()

    # Analyze based on tuop
    if tuop == 0b010:  # tuop_010 - vector operations
        print("This is a tuop_010 (vector operations) instruction")
        print(f"  vecuop1: {vecuop1:02b} = {vecuop1}")
        print(f"  rsen: {rsen}")
        print(f"  immen: {immen}")
        print(f"  vecuop2[5:2]: {vecuop2_52:04b} = {vecuop2_52}")
        print()

        # Determine instruction type based on vecuop1
        if vecuop1 == 0b01:  # vecuop1 = 01 - SFU operations
            print("vecuop1 = 01 → SFU (Special Function Unit) operations")
            print(f"vecuop2[5:2] = {vecuop2_52} → ", end="")

            sfu_ops = {
                0: 'tsgmd',
                1: 'tsin',
                2: 'tcos',
                3: 'texp2',
                4: 'tlog2',
                6: 'trcp',
                7: 'tsqrt',
                8: 'trsqrt',
                9: 'ttanh'
            }

            if vecuop2_52 in sfu_ops:
                print(f"{sfu_ops[vecuop2_52]} operation")
                print(f"This explains why it's decoded as '{sfu_ops[vecuop2_52]}.tt.f32'")
            else:
                print("Unknown SFU operation")

        elif vecuop1 == 0b10:  # vecuop1 = 10 - 2-operand ALU operations
            print("vecuop1 = 10 → 2-operand ALU operations (ADD/MUL/MIN/MAX)")
            print(f"vecuop2[5:2] = {vecuop2_52} → ", end="")

            alu_ops = {
                0: 'tadd',
                1: 'tmul',
                8: 'tmin',
                9: 'tmax'
            }

            if vecuop2_52 in alu_ops:
                print(f"{alu_ops[vecuop2_52]} operation")

                # Determine operand types
                if rsen and not immen:
                    print(f"rsen=1, immen=0 → {alu_ops[vecuop2_52]}.ttr (tile-tile-register)")
                elif not rsen and not immen:
                    print(f"rsen=0, immen=0 → {alu_ops[vecuop2_52]}.ttt (tile-tile-tile)")
                elif not rsen and immen:
                    print(f"rsen=0, immen=1 → {alu_ops[vecuop2_52]}.tti (tile-tile-immediate)")

                print(f"Expected instruction: {alu_ops[vecuop2_52]}.ttr.f32")
            else:
                print("Unknown ALU operation")
        else:
            print(f"vecuop1 = {vecuop1} → Other operation type")
    elif tuop == 0b110:  # tuop_110 - multi-lane instructions
        print("This is a tuop_110 (multi-lane) instruction")
        print("Need to check second word tuop to determine exact type")
    else:
        print(f"tuop = {tuop} → Other instruction type")

    print()
    print("ISSUE ANALYSIS:")
    print("The decoder is incorrectly interpreting this instruction.")
    print()
    print("Current decoder logic:")
    print(f"1. tuop = {tuop} (110) → treated as tuop_110 (multi-lane)")
    print(f"2. vecuop1 = {vecuop1} (01) and memuop = {memuop} (000000)")
    print("3. This matches the special case in get_instruction_length() line 154:")
    print("   if (vecuop1 == 2'b01 && memuop_field == 6'b000000)")
    print("4. So it's treated as a 64-bit vector ALU instruction")
    print("5. But in extract_instruction_name(), it goes to tuop_110 case")
    print("6. In tuop_110, it checks for SFU operations and finds vecuop2[5:2] = 2")
    print("7. vecuop2[5:2] = 2 maps to 'tcos' in SFU operations")
    print()
    print("PROBLEM:")
    print("The instruction should be decoded as tuop_010 (vector operations), not tuop_110!")
    print("The tuop field extraction might be wrong, or there's a logic error.")
    print()
    print("Let's check if this is actually a tuop_010 instruction:")
    print("If tuop should be 010, then:")
    print("- vecuop1 = 01 → SFU operations")
    print("- vecuop2[5:2] = 2 → tcos operation")
    print("- This would correctly decode as tcos.tt.f32")
    print()
    print("But the expected instruction is tmul.ttr.f32, which means:")
    print("- tuop should be 010")
    print("- vecuop1 should be 10 (2-operand ALU)")
    print("- vecuop2[5:2] should be 1 (tmul)")
    print("- rsen should be 1, immen should be 0")
    print()
    print("CONCLUSION: There's a mismatch between the expected instruction encoding")
    print("and the actual hex value. Need to verify the correct encoding.")

if __name__ == "__main__":
    # Analyze the problematic instruction
    analyze_instruction("0x82216b000564fb")
