#!/usr/bin/env python3
"""
分析指令 0x027b1009e4fb (tadd.ttr.f32.neg2 T0,T0,s3)
"""

def analyze_tadd_ttr_instruction():
    # 指令编码: 0x027b1009e4fb (64位)
    instr_val = 0x027b1009e4fb
    
    print("=== 分析 tadd.ttr.f32.neg2 指令 ===")
    print(f"指令编码: 0x{instr_val:016x}")
    print()
    
    # 按32位字节序分解 (64位指令)
    word1 = instr_val & 0xFFFFFFFF      # 低32位: 0x1009e4fb
    word2 = (instr_val >> 32) & 0xFFFFFFFF  # 高32位: 0x027b
    
    # 注意：在64位指令中，实际的字节序可能不同
    # 让我们也试试反序
    print(f"按低-高分解:")
    print(f"  第一个32位字: 0x{word1:08x}")
    print(f"  第二个32位字: 0x{word2:08x}")
    print()
    
    # 分析第一个字的关键字段 (word1 = 0x1009e4fb)
    print("=== 第一个字段分析 (0x1009e4fb) ===")
    ace_op = word1 & 0x7F
    rsen_immen = (word1 >> 8) & 0x3
    vecuop1 = (word1 >> 10) & 0x3
    tuop = (word1 >> 12) & 0x7
    rs2 = (word1 >> 15) & 0x1F
    reuse1 = (word1 >> 25) & 0x1
    neg1 = (word1 >> 27) & 0x1
    neg2 = (word1 >> 28) & 0x1
    vecuop2_10 = (word1 >> 29) & 0x3
    
    print(f"ACE_OP[6:0]:     {ace_op:07b} (0x{ace_op:02x}) = {ace_op}")
    print(f"rsen+immen[9:8]: {rsen_immen:02b} ({rsen_immen})")
    print(f"VECUOP1[11:10]:  {vecuop1:02b} ({vecuop1})")
    print(f"TUOP[14:12]:     {tuop:03b} ({tuop})")
    print(f"rs2[19:15]:      {rs2:05b} ({rs2})")
    print(f"reuse1[25]:      {reuse1} ")
    print(f"neg1[27]:        {neg1}")
    print(f"neg2[28]:        {neg2}")
    print(f"vecuop2[1:0]:    {vecuop2_10:02b} ({vecuop2_10})")
    print()
    
    # 分析第二个字的关键字段 (word2 = 0x027b) - 实际上是16位
    print("=== 第二个字段分析 (0x027b - 只有低16位有效) ===")
    word2_16bit = word2 & 0xFFFF  # 取低16位
    ace_op2 = word2_16bit & 0x7F
    tuop2 = (word2_16bit >> 12) & 0x7
    vecuop2_52 = (word2_16bit >> 8) & 0xF
    ts1 = (word2_16bit >> 8) & 0xFF  # 这可能不对，需要参考编码文档
    td = (word2_16bit >> 0) & 0xFF   # 这也需要确认
    
    print(f"完整第二字: 0x{word2_16bit:04x}")
    print(f"ACE_OP[6:0]:     {ace_op2:07b} (0x{ace_op2:02x})")
    print(f"TUOP2[14:12]:    {tuop2:03b} ({tuop2})")
    print(f"vecuop2[5:2]:    {vecuop2_52:04b} ({vecuop2_52})")
    print()
    
    # 现在按照tadd.ttr的编码格式重新分析
    print("=== 按照tadd.ttr编码格式重新分析 ===")
    
    # 根据tadd.ttr.md文档中的位字段定义
    # 第一个字包含: ACE_op[6:0], rsen=1, vecuop1[11:10]=10, tuop[14:12]=110, rs2[19:15], neg1[27], neg2[28]
    # 第二个字包含: ACE_op[6:0], vecuop2[5:2]=0000, tuop[14:12]=010, Ts1[23:16], Td[31:24]
    
    is_tile = (ace_op == 0x7B)
    is_tadd_ttr = (
        is_tile and 
        tuop == 6 and  # tuop=110 in first word
        vecuop1 == 2 and  # vecuop1=10
        (rsen_immen & 1) == 1 and  # rsen=1 (bit 8)
        (rsen_immen & 2) == 0      # immen=0 (bit 9)
    )
    
    print(f"1. 是Tile指令 (ACE_OP == 0x7B): {is_tile}")
    print(f"2. TUOP == 6 (110): {tuop == 6}")
    print(f"3. VECUOP1 == 2 (10): {vecuop1 == 2}")
    print(f"4. rsen == 1: {(rsen_immen & 1) == 1}")
    print(f"5. immen == 0: {(rsen_immen & 2) == 0}")
    print(f"6. neg2 == 1: {neg2 == 1}")
    print()
    
    if is_tadd_ttr:
        print("✓ 应该识别为: tadd.ttr.f32" + (".neg2" if neg2 else ""))
        print(f"✓ 操作数: T{td}, T{ts1}, r{rs2}")
        if neg2:
            print("✓ 包含neg2修饰符")
    else:
        print("✗ 不匹配tadd.ttr模式")
    
    print()
    print("=== 问题分析 ===")
    print("当前问题可能出现在:")
    print("1. 指令长度判断 - 可能被错误判断为64位而非32位")
    print("2. extract_instruction_name函数中对tuop=110的处理逻辑")
    print("3. 对rsen/immen字段的检查")
    print("4. 可能缺少对tadd.ttr变体的支持")

if __name__ == "__main__":
    analyze_tadd_ttr_instruction()
