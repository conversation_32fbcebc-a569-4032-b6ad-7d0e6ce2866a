#!/bin/bash

# VCS Test Script for Tile Instruction Decoder
# Tests the fix for instruction 0x8003907b8200647b

echo "=== VCS Test Script for Tile Instruction Decoder ==="
echo "Testing fix for instruction 0x8003907b8200647b"
echo ""

# Clean up previous runs
echo "Cleaning up previous runs..."
rm -rf simv* csrc* *.log *.vpd *.fsdb DVEfiles/ ucli.key
echo ""

# Compile with VCS
echo "Compiling with VCS..."
vcs -sverilog \
    -timescale=1ns/1ps \
    +v2k \
    -debug_access+all \
    -kdb \
    -lca \
    -full64 \
    +lint=TFIPC-L \
    +warn=all \
    -o simv \
    tile_instruction_decoder.sv \
    test_field_based_fix.sv

# Check compilation result
if [ $? -eq 0 ]; then
    echo "✓ Compilation successful!"
    echo ""
else
    echo "✗ Compilation failed!"
    echo "Please check the error messages above."
    exit 1
fi

# Run simulation
echo "Running simulation..."
echo "----------------------------------------"
./simv +vcs+lic+wait
echo "----------------------------------------"

# Check simulation result
if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Simulation completed successfully!"
else
    echo ""
    echo "✗ Simulation failed!"
    exit 1
fi

echo ""
echo "=== VCS Test Complete ==="
echo ""
echo "Please check the output above for:"
echo "1. Whether the instruction was correctly identified as tld.trr.blk.mx48.share"
echo "2. Whether the field extraction produced td=0, rs1=7, rs2=0"
echo "3. Whether the final result is 'tld.trr.blk.mx48.share t0, (x7), x0'"
echo ""
echo "If all checks pass, the fix is working correctly!"
