#!/usr/bin/env python3
"""
Simulate the SystemVerilog logic to verify the fix for instruction 0x8003907b8200647b
"""

def extract_instruction_name(instruction_data, length):
    """Simulate the SystemVerilog extract_instruction_name function"""
    
    TILE_ACE_OP = 0x7b
    INSTR_64BIT = 1
    
    # Extract fields
    ace_op = instruction_data & 0x7F
    
    if ace_op != TILE_ACE_OP:
        return "unknown_non_tile"
    
    tuop = (instruction_data >> 12) & 0x7
    
    if tuop == 0:  # tuop_000
        # Memory operations
        memuop = (instruction_data >> 25) & 0x3F
        lsuop = (instruction_data >> 10) & 0x3
        # ... (other tuop_000 logic)
        return "tuop_000_operations"
        
    elif tuop == 1:  # tuop_001
        return "tmma.ttt"
        
    elif tuop == 6:  # tuop_110
        memuop = (instruction_data >> 25) & 0x3F
        lsuop = (instruction_data >> 10) & 0x3
        
        if length == INSTR_64BIT:  # Dual-lane instructions
            second_tuop = (instruction_data >> (32 + 12)) & 0x7
            
            if memuop == 1:  # 6'b000001 - Block memory operations
                if second_tuop == 1:  # 3'b001 - tuop_110 + tuop_001
                    if lsuop == 1:  # 2'b01
                        return "tld.trr.blk.mx48.share"
                    elif lsuop == 2:  # 2'b10
                        return "tld.trr.blk.mx6.share"
                    elif lsuop == 0:  # 2'b00
                        return "tld.trr.blk.share"
                    else:
                        return "unknown_blk_lsuop"
                else:
                    return "unknown_blk_second_tuop"
                    
            elif memuop == 0:  # 6'b000000 - Standard memory operations
                if second_tuop == 0:  # 3'b000 - tuop_110 + tuop_000
                    if lsuop == 0:  # 2'b00
                        return "tld.trii.linear.u32.global"
                    elif lsuop == 1:  # 2'b01
                        return "tld.trri.stride.u32.global"
                    elif lsuop == 3:  # 2'b11
                        return "tld.other.64bit"
                    else:
                        return "unknown_std_lsuop"
                else:
                    return "unknown_std_second_tuop"
            else:
                return "ace_low"  # Regular ACE operation
        else:
            return "non_64bit_tuop_110"
            
    elif tuop == 4:  # tuop_100
        # CSR operations
        rw = (instruction_data >> 30) & 0x3
        if rw == 0:
            return "tcsrw.i"
        elif rw == 1:
            return "tcsrr.r"
        elif rw == 2:
            return "tcsrw.r"
        else:
            return "unknown_csr"
            
    elif tuop == 7:  # tuop_111
        return "ace_mem_high"
        
    else:
        return "unknown_tile"

def format_operands(instruction_data, length, instr_name):
    """Simulate the SystemVerilog format_operands function"""
    
    INSTR_64BIT = 1
    
    if length == INSTR_64BIT:
        if instr_name.startswith("tld") and "trr" in instr_name:
            # Dual-lane block memory instructions
            td = (instruction_data >> (32 + 23)) & 0xFF  # Td field in second word bits [30:23]
            rs1 = (instruction_data >> (32 + 15)) & 0x1F  # rs1 field in second word bits [19:15]
            rs2 = (instruction_data >> 20) & 0x1F  # rs3 field in first word bits [24:20]
            return f"t{td}, (x{rs1}), x{rs2}"
        else:
            # Standard 64-bit layout
            td = (instruction_data >> 32) & 0xFF  # Td field in second word
            rs1 = (instruction_data >> (32 + 16)) & 0x1F  # rs1 field in second word
            return f"t{td}, (x{rs1})"
    
    return ""

def disassemble_instruction(instruction_data, length):
    """Simulate the SystemVerilog disassemble_instruction function"""
    
    instr_name = extract_instruction_name(instruction_data, length)
    operands = format_operands(instruction_data, length, instr_name)
    
    if operands:
        return f"{instr_name} {operands}"
    else:
        return instr_name

def test_instruction():
    """Test the specific instruction 0x8003907b8200647b"""
    
    instruction = 0x8003907b8200647b
    length = 1  # INSTR_64BIT
    
    print(f"Testing instruction: 0x{instruction:016x}")
    print(f"Length: {length} (64-bit)")
    print()
    
    # Extract fields for debugging
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF
    
    print(f"Word 1 (low):  0x{word1:08x}")
    print(f"Word 2 (high): 0x{word2:08x}")
    print()
    
    # Field analysis
    ace_op = word1 & 0x7F
    tuop = (word1 >> 12) & 0x7
    lsuop = (word1 >> 10) & 0x3
    memuop = (word1 >> 25) & 0x3F
    second_tuop = (word2 >> 12) & 0x7
    
    print("Field analysis:")
    print(f"  ace_op: 0x{ace_op:02x} ({'TILE' if ace_op == 0x7b else 'NOT_TILE'})")
    print(f"  tuop: {tuop}")
    print(f"  lsuop: {lsuop}")
    print(f"  memuop: {memuop}")
    print(f"  second_tuop: {second_tuop}")
    print()
    
    # Test disassembly
    result = disassemble_instruction(instruction, length)
    print(f"Disassembly result: {result}")
    print()
    
    # Check result
    expected = "tld.trr.blk.mx48.share"
    if result.startswith(expected):
        print("✓ SUCCESS: Instruction correctly identified!")
        print("The fix has resolved the unknown_tile issue.")
    else:
        print("✗ FAILURE: Instruction still not correctly identified.")
        print(f"Expected to start with: {expected}")
        print(f"Actual result: {result}")

if __name__ == "__main__":
    test_instruction()
