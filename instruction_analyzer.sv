// Instruction bit field analyzer
module instruction_analyzer;

    reg [63:0] instruction;
    reg [31:0] word1, word2;
    reg [6:0] ace_op1, ace_op2;
    reg [2:0] tuop1, tuop2;
    reg [5:0] memuop;
    reg [1:0] lsuop;
    reg [7:0] td;
    reg [4:0] rs1, rs2, rs3;
    reg offseten, rmten;
    
    initial begin
        // Analyze instruction 0x8003907b8200647b
        instruction = 64'h8003907b8200647b;
        word1 = instruction[31:0];   // Lower 32 bits (first word)
        word2 = instruction[63:32];  // Upper 32 bits (second word)
        
        $display("=== Instruction Analysis: 0x%016x ===", instruction);
        $display("Word1 (bits 31:0):  0x%08x", word1);
        $display("Word2 (bits 63:32): 0x%08x", word2);
        
        // Analyze first word
        ace_op1 = word1[6:0];
        tuop1 = word1[14:12];
        lsuop = word1[11:10];
        memuop = word1[30:25];
        rs3 = word1[24:20];
        
        $display("\n=== First Word Analysis ===");
        $display("ACE_OP[6:0]:   %b (%d)", ace_op1, ace_op1);
        $display("TUOP[14:12]:   %b (%d)", tuop1, tuop1);
        $display("LSUOP[11:10]:  %b (%d)", lsuop, lsuop);
        $display("MEMUOP[30:25]: %b (%d)", memuop, memuop);
        $display("RS3[24:20]:    %b (%d)", rs3, rs3);
        
        // Analyze second word
        ace_op2 = word2[6:0];
        tuop2 = word2[14:12];
        td = word2[30:23];
        rs1 = word2[19:15];
        offseten = word2[15];
        rmten = word2[20];
        
        $display("\n=== Second Word Analysis ===");
        $display("ACE_OP[6:0]:   %b (%d)", ace_op2, ace_op2);
        $display("TUOP[14:12]:   %b (%d)", tuop2, tuop2);
        $display("TD[30:23]:     %b (%d)", td, td);
        $display("RS1[19:15]:    %b (%d)", rs1, rs1);
        $display("OFFSETEN[15]:  %b (%d)", offseten, offseten);
        $display("RMTEN[20]:     %b (%d)", rmten, rmten);
        
        // Expected instruction analysis
        $display("\n=== Expected: tld.trr.mx58.share T0, (t2), zero ===");
        $display("- Should be a tld (load) instruction");
        $display("- Should be trr format (tile-register-register)");
        $display("- Should have mx58 modifier");
        $display("- Should be share memory space");
        $display("- TD should be T0 (0)");
        $display("- Should involve t2 register");
        $display("- Should involve zero register");
        
        // Check if this matches our current decoding
        $display("\n=== Current Decoding Analysis ===");
        if (ace_op1 == 7'b1111011) begin
            $display("✓ ACE_OP matches tile instruction");
            
            case (tuop1)
                3'b110: begin
                    $display("✓ TUOP1 = 110 (multi-lane memory instruction)");
                    
                    case (memuop)
                        6'b000001: begin
                            $display("✓ MEMUOP = 000001 (block memory operations)");
                            
                            if (ace_op2 == 7'b1111011 && tuop2 == 3'b001) begin
                                $display("✓ Second word: ACE_OP=1111011, TUOP2=001");
                                $display("→ This should be tld.trr.blk.* instruction");
                            end else begin
                                $display("✗ Second word format mismatch");
                                $display("  Expected: ACE_OP=1111011, TUOP2=001");
                                $display("  Actual: ACE_OP=%b, TUOP2=%b", ace_op2, tuop2);
                            end
                        end
                        default: begin
                            $display("✗ MEMUOP = %b (not block operations)", memuop);
                        end
                    endcase
                end
                default: begin
                    $display("✗ TUOP1 = %b (not 110)", tuop1);
                end
            endcase
        end else begin
            $display("✗ ACE_OP = %b (not tile instruction)", ace_op1);
        end
        
        $display("\n=== Problem Identification ===");
        $display("The issue is likely in the instruction name extraction logic.");
        $display("Current decoder returns generic 'tld_64bit' instead of specific format.");
        $display("Need to enhance extract_instruction_name() for detailed tld.trr.* decoding.");
    end

endmodule
