# Control Register Operation
## CSR指令
读CSR：ACE将读命令在cycle 0发射到CSR regs后，cycle 1即可写回ACE，释放ACE读数据的功能单元。该指令的throughput为0.5，两个cycle可发射一个CSR读指令。
写CSR：ACE将写命令和数据在cycle 0发射到CSR regs后，释放ACE读数据的功能单元。该指令的throughput为1，一个cycle可发射一个CSR读指令。
b64为0时，代表按32bit读写CSR，否则按64bit读写CSR。CSR_idx用于选择CSR。

**b32模式的指令操作寄存器必须32-bit对齐，0x4,0x8的地址都可以访问**
**b64模式的指令操作寄存器必须64-bit对齐，0x8的地址可以访问，0x4的地址访问为非法**
### TCSRR
该指令可将 CSR 原有的值读到 rd 指定的寄存器。
`tcsrr.r.b32/b64 rd, csr_addr`
@import "encode/tuop_100/rw_01/tcsrr.r.md"
### TCSRW
或者将 rs1 的值写入到该寄存器
`tcsrw.r.b32/b64 rs1, csr_addr`
@import "encode/tuop_100/rw_10/tcsrw.r.md"
### TCSRWI
和 TCSRW相同，仅仅是未用到 rs1，用 5b 立即数代替。
`tcsrw.i.b32/b64 imm1, csr_addr`
@import "encode/tuop_100/rw_00/tcsrw.i.md"

## CSR列表
| CSR_name | CSR_addr | CSR_attr | CSR description
| -------- |--------| -------- | --------- |
| tmask | 0x0    | RW     |talu使用的配置信息，由kernel配置，可全局配置所有用到tmask bit的指令的mask|
| tctrl | 0x8    | RW     | ALU和MMA的 rounding/saturate/deform/saturate/MX OCP/信息，由kernel配置|
| tcmdvalid | 0x100 | RO | 表示目前有可执行的kernel。BD发任务cmdValid为1，tend写值后cmdValid改为0。 |
| tend | 0x108    | WO     |thread完成任务需要写该寄存器，对该寄存器写非0可通知CCS该thread已经完成。该寄存器不可读，只会发出一个脉冲信号通知CCS|
| thwid | 0x180    | RO     | 包括2bits core_id/2bits pe_id/4bits cluster_id/8bits chip_id |
| tsynccnt | 0x190 | RO | 包括core_idle/mem_idle/smem_ld_cnt/smem_st_cnt/gmem_ld_cnt/gmem_ld_cnt/cmt_group，该信号需要通到ACE pipeline上 |
| reserved0 | 0x1a0 | RO | |
| reserved1 | 0x1b0 | RO | |
| reserved2 | 0x1c0 | RO | |
| reserved3 | 0x1d0 | RO | |
| tsmembase | 0x200    | RW     |kernel在share memory使用的起始地址， 由CCS配置，256B对齐，kernel软件无需使用。**仅在访存指令访问本PE L2B时，访存的地址由硬件加上这个offset作为实际的logic addr。可在debug模式修改该寄存器。** |
| tsmemsize | 0x208    | RW     |kernel在share memory使用的地址空间范围，由CCS配置，256B对齐，kernel软件无需使用。可在debug模式修改该寄存器。|
| tkerneladdr | 0x210    | RO     |kernel启动addr， 由CCS配置，**需ERT软件读回RV标量寄存器后使用**|
| tparamaddr | 0x220    | RO     |kernel启动需要的参数的地址，由CCS配置，8B对齐**需kernel软件读回RV标量寄存器后使用**|
| tparamsize | 0x228    | RO     |kernel启动需要的参数的size，由CCS配置，8B对齐**需kernel软件读回RV标量寄存器后使用**|
| tkernelidx | 0x230     | RO     |包括tb/tbc的x/y/z方向和thread index，由CCS配置，**需kernel软件读回RV标量寄存器后使用** |
| tkerneldims | 0x240     | RO     |包括tb/tbc的x/y/z方向和thread  dimision ，由CCS配置，**需kernel软件读回RV标量寄存器后使用** |
| tregbase | 0x250    | RO     |kernel在tile reg使用的寄存器offset，由CCS配置，256B对齐，kernel软件无需使用，仅用于debug|
| tregsize | 0x254    | RO     |kernel在tile reg使用的寄存器数量， 由CCS配置，kernel软件无需使用，仅用于debug|
| tprivatebase | 0x260    | RO     |kernel在global memory上使用的thread private地址空间起始地址，256B对齐，由BD配置|
| tprivatesize | 0x268    | RO     |kernel在global memory上使用的thread private地址空间总容量，256B对齐，由BD配置|
| ttbmap | 0x270    | RO     |数据流模式下tb和pe的匹配表，由CCS配置|

**如下图所示，0x0-0x3fc的CSR寄存器都要都要通过以下通路进行交互。CCS可通过Block Dispatch模块配置到Tile CSR，之后向RV Core发起中断启动kernel。**
**同理，在thread完成后，RV Core写tend寄存器，CSR可发起结束信号发到Block Dispatch，BD返回thread-done信号给CCS**
@import "graph/thread_launch.svg"

### tmask
Bit 0为1时，0-31B有效；Bit 1为1时，32-63B有效。
@import "csr/tmask.md"
### tctrl
@import "csr/tctrl.md"
|  | 3'000 | 3'001 | 3'010 | 3'011 | 3'100 |
| - | - | - | - | - | - |
| tfrnd | RNE(rn) | RTZ(rz)  | RTN(rm) | RTP(rp) | RTA(rna) |

|  | 3'000 | 3'001 | 3'010 | 3'011 |
| - | - | - | - | - |
| tirnd | RNI | RZI | RMI | RPI |

|  | 作用指令 | bit 0 等于 1 | bit 1 等于 1 | bit 2 等于 1 |
| - | - | - | - | - |
| tsat | FMA/MUL/ADD | F32_SAT | F16_SAT | BF16_SAT |
| tsatinfinite | FMA/MUL/ADD | F32_SATFINITE | F16_SATFINITE | BF16_SATFINITE |
| trelu | FMA/MUL/ADD | F32_RELU | F16_RELU | BF16_RELU |
| tnan | MIN/MAX | F32_NAN | F16_NAN | BF16_NAN |
| tnocp | Convert指令 | 使用[优化方案](https://siorigin.feishu.cn/wiki/Nubqwp5bXi8pfOk5AhEcLNVFnsc#share-EXssdSrvjohWOkxk8mtcIzLLnvc)进行运算，否则**默认**使用[OCP方案](https://siorigin.feishu.cn/wiki/Nubqwp5bXi8pfOk5AhEcLNVFnsc#share-MloEdQzCnoXBDBxDeYrcnNsqnQW)进行运算 | |

|  | 作用指令 | CSR bits | bit 32 等于 1 |
| - | - | - | - |
| tmsatfinite | MMA | tctrl[32] | TMAC运算结果在overlow和underflow时，结果会转为最大和最小值。 | 

详见[TALU CSR Fields](https://siorigin.feishu.cn/wiki/NCMfwcBjhiMdackpDpScX9UZnvb)
### tcmdvalid
@import "csr/tcmdvalid.md"
### tend
@import "csr/tend.md"
### thwid
@import "csr/thwid.md"
### tsynccounter
@import "csr/tsynccnt.md"
### tsmembase
@import "csr/tsmembase.md"
### tsmemsize
@import "csr/tsmemsize.md"
### tkerneladdr
@import "csr/tkerneladdr.md"
### tparamaddr
@import "csr/tparamaddr.md"
### tparamsize
@import "csr/tparamsize.md"
### tkernelidx
@import "csr/tkernelidx.md"
### tkerneldims
@import "csr/tkerneldims.md"
### tregbase
@import "csr/tregbase.md"
### tregsize
@import "csr/tregsize.md"
### tprivatebase
@import "csr/tprivatebase.md"
### tprivatesize
@import "csr/tprivatesize.md"
### ttbmap
@import "csr/ttbmap.md"