# VCS测试命令

## 方法1: 使用提供的脚本

```bash
# 直接运行测试脚本
./run_vcs_test.sh
```

## 方法2: 手动VCS命令

### 编译命令 (测试字段修复)
```bash
vcs -sverilog \
    -timescale=1ns/1ps \
    +v2k \
    -debug_access+all \
    -kdb \
    -lca \
    -full64 \
    +lint=TFIPC-L \
    +warn=all \
    -o simv \
    tile_instruction_decoder.sv \
    test_field_based_fix.sv
```

### 运行命令
```bash
./simv +vcs+lic+wait
```

## 方法3: 最简化命令

如果上面的命令有问题，可以尝试最简化版本：

```bash
# 编译
vcs -sverilog tile_instruction_decoder.sv vcs_test_instruction.sv

# 运行
./simv
```

## 期望输出

测试成功时，你应该看到类似这样的输出：

```
=== VCS Test for Instruction 0x8003907b8200647b ===
Testing field extraction fix for tld.trr.blk.mx48.share

Test Data:
  Full instruction: 0x8003907b8200647b
  Word 1 (low):     0x8200647b
  Word 2 (high):    0x8003907b

Step 1: Initialize collector
  Is tile instruction: YES
  Expected length: INSTR_64BIT
  Complete after first word: NO

Step 2: Add second word
  Complete after second word: YES
  Final instruction_data: 0x00000000000000008003907b8200647b

Step 3: Disassemble instruction
  Disassembly result: tld.trr.blk.mx48.share t0, (x7), x0

Step 4: Verify result
  Expected result: tld.trr.blk.mx48.share t0, (x7), x0
  Actual result:   tld.trr.blk.mx48.share t0, (x7), x0
  ✓ SUCCESS: Perfect match!

Step 5: Debug field extraction
  Manual field extraction:
    td (word2[30:23]):  0
    rs1 (word2[19:15]): 7
    rs2 (word1[24:20]): 0
  ✓ Field extraction values are correct!

Step 6: Test instruction identification
  Instruction identification fields:
    ace_op1: 0x7b (TILE)
    tuop1: 6 (110)
    memuop: 1 (000001)
    lsuop: 1 (01)
    ace_op2: 0x7b
    tuop2: 1 (001)
  ✓ Instruction identification is correct for tld.trr.blk.mx48.share

=== VCS Test Complete ===

OVERALL RESULT: ✓ SUCCESS - Fix is working correctly!
```

## 关键检查点

运行测试后，请检查以下几点：

1. **指令识别**: 是否正确识别为 `tld.trr.blk.mx48.share`
2. **字段提取**: 是否得到 `td=0, rs1=7, rs2=0`
3. **最终结果**: 是否输出 `tld.trr.blk.mx48.share t0, (x7), x0`
4. **整体状态**: 是否显示 `SUCCESS - Fix is working correctly!`

如果所有检查点都通过，说明修复是成功的。

## 故障排除

如果遇到编译错误：
1. 确保VCS版本支持SystemVerilog 2012
2. 检查是否有语法错误
3. 尝试使用最简化的编译命令

如果遇到运行时错误：
1. 检查是否有逻辑错误
2. 查看具体的错误信息
3. 确认指令数据格式正确
