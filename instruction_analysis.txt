Instruction Analysis for 0x027b1009e4fb

Binary representation:
Word 1: 0x1009e4fb = 0001 0000 0000 1001 1110 0100 1111 1011
Word 2: 0x027b     = 0000 0010 0111 1011

Bit field extraction from Word 1 (0x1009e4fb):
Bits [6:0]   (ACE_OP): 111 1011 = 0x7B = 123 (TILE instruction confirmed)
Bits [14:12] (tuop):   110 (6 = tuop_110)
Bits [11:10] (vecuop1): 10 (2)
Bit  9       (immen):  0 (immediate enable = false)
Bit  8       (rsen):   1 (register enable = true)
Bits [24:20] (rs2):   11110 = 30
Bit  28      (neg2):   1 (neg2 is set)
Bit  27      (neg1):   0 (neg1 is not set)

Bit field extraction from Word 2 (0x027b):
Bits [6:0]   (ACE_OP): 111 1011 = 0x7B = 123 (TILE instruction confirmed)
Bits [14:12] (tuop):   010 (2 = tuop_010) 
Bits [19:16] (vecuop2[5:2]): 0000 (0 = tadd operation)

Pattern Analysis:
- tuop_110 (6) + tuop_010 (2): ✓ MATCH (dual-lane vector ALU)
- vecuop1 = 10: ✓ MATCH (2-operand ALU)
- vecuop2[5:2] = 0000: ✓ MATCH (tadd operation)
- rsen = 1 && immen = 0: ✓ MATCH (ttr variant - tile-tile-register)
- neg2 = 1: ✓ PRESENT (neg2 modifier)
- neg1 = 0: absent

Expected result: tadd.ttr.f32.neg2

Register decoding:
- rs2 = 30 (should be x30, but display might show x19 due to different indexing)
