#!/usr/bin/env python3
"""
深入分析指令 0x027b1009e4fb
根据文档重新理解这是一个双word(64位)指令，但需要正确解析字段位置
"""

def analyze_instruction_detailed():
    # 指令编码: 0x027b1009e4fb (64位)
    instr_val = 0x027b1009e4fb
    
    print("=== 详细分析指令 0x027b1009e4fb ===")
    print(f"指令编码: 0x{instr_val:016x}")
    print()
    
    # 按64位指令格式分解
    # 第一个word (低32位): 0x1009e4fb
    # 第二个word (高32位): 0x0000027b (实际只有低16位有效)
    word1 = instr_val & 0xFFFFFFFF          # 0x1009e4fb
    word2 = (instr_val >> 32) & 0xFFFFFFFF  # 0x0000027b
    
    print(f"Word 1 (低32位):  0x{word1:08x}")
    print(f"Word 2 (高32位):  0x{word2:08x}")
    print()
    
    # 根据64位指令的正确格式分析
    print("=== 第一个Word分析 (0x1009e4fb) ===")
    
    # 第一个word的字段
    ace_op1 = word1 & 0x7F                    # [6:0]
    rsen_immen = (word1 >> 8) & 0x3           # [9:8] - rsen在bit 8, immen在bit 9
    vecuop1 = (word1 >> 10) & 0x3             # [11:10]
    tuop1 = (word1 >> 12) & 0x7               # [14:12]
    rs2 = (word1 >> 15) & 0x1F                # [19:15]
    rs3 = (word1 >> 20) & 0x1F                # [24:20] 
    reuse1 = (word1 >> 25) & 0x1             # [25]
    reuse2 = (word1 >> 26) & 0x1             # [26]
    neg1 = (word1 >> 27) & 0x1               # [27]
    neg2 = (word1 >> 28) & 0x1               # [28]
    vecuop2_10 = (word1 >> 29) & 0x3         # [30:29]
    
    print(f"ACE_OP[6:0]:      {ace_op1:07b} (0x{ace_op1:02x}) = {ace_op1}")
    print(f"rsen[8]:          {(rsen_immen & 1)} ")
    print(f"immen[9]:         {(rsen_immen >> 1) & 1} ")
    print(f"VECUOP1[11:10]:   {vecuop1:02b} ({vecuop1})")
    print(f"TUOP[14:12]:      {tuop1:03b} ({tuop1})")
    print(f"rs2[19:15]:       {rs2:05b} ({rs2})")
    print(f"rs3[24:20]:       {rs3:05b} ({rs3})")
    print(f"reuse1[25]:       {reuse1}")
    print(f"reuse2[26]:       {reuse2}")
    print(f"neg1[27]:         {neg1}")
    print(f"neg2[28]:         {neg2}")
    print(f"vecuop2[1:0]:     {vecuop2_10:02b} ({vecuop2_10})")
    print()
    
    print("=== 第二个Word分析 (0x027b) ===")
    # 第二个word (实际只有16位): 0x027b  
    ace_op2 = word2 & 0x7F                    # [6:0]
    reserved1 = (word2 >> 7) & 0x1            # [7]
    vecuop2_52 = (word2 >> 8) & 0xF           # [11:8] - vecuop2[5:2]
    tuop2 = (word2 >> 12) & 0x7               # [14:12]
    reserved2 = (word2 >> 15) & 0x1           # [15]
    ts1 = (word2 >> 16) & 0xFF                # [23:16] - 但word2只有16位，所以这里应该是0
    td = (word2 >> 24) & 0xFF                 # [31:24] - 同样，这里应该是0
    
    print(f"ACE_OP[6:0]:      {ace_op2:07b} (0x{ace_op2:02x}) = {ace_op2}")
    print(f"reserved[7]:      {reserved1}")
    print(f"vecuop2[5:2]:     {vecuop2_52:04b} ({vecuop2_52})")
    print(f"TUOP[14:12]:      {tuop2:03b} ({tuop2})")
    print(f"reserved[15]:     {reserved2}")
    print()
    
    # 关键特征识别
    print("=== 指令特征识别 ===")
    is_tile = (ace_op1 == 0x7B and ace_op2 == 0x7B)
    is_tuop_110 = (tuop1 == 6)  # 110 = 6
    is_tuop_010 = (tuop2 == 2)  # 010 = 2  
    is_vecuop1_01 = (vecuop1 == 1)  # 01 = 1
    is_vecuop2_0000 = (vecuop2_52 == 0)  # 0000 = 0
    has_rsen = ((rsen_immen & 1) == 1)
    has_immen = ((rsen_immen >> 1) & 1 == 1)
    has_neg2 = (neg2 == 1)
    
    print(f"是Tile指令 (双ACE_OP都是0x7B): {is_tile}")
    print(f"第一字TUOP=6 (110): {is_tuop_110}")
    print(f"第二字TUOP=2 (010): {is_tuop_010}")
    print(f"VECUOP1=1 (01): {is_vecuop1_01}")
    print(f"VECUOP2[5:2]=0 (0000): {is_vecuop2_0000}")
    print(f"有rsen标志: {has_rsen}")
    print(f"有immen标志: {has_immen}")
    print(f"有neg2标志: {has_neg2}")
    print()
    
    # 根据特征判断指令类型
    print("=== 指令类型判断 ===")
    
    # 这看起来像一个特殊的向量运算指令
    # tuop1=110 (第一字) + tuop2=010 (第二字) + vecuop1=01 + vecuop2[5:2]=0000
    if (is_tile and is_tuop_110 and is_tuop_010 and 
        is_vecuop1_01 and is_vecuop2_0000 and has_rsen and has_neg2):
        
        print("✓ 匹配特殊的向量加法指令模式:")
        print("  - 这是一个tuop_110 + tuop_010组合")
        print("  - vecuop1=01, vecuop2[5:2]=0000 表示向量加法")
        print("  - rsen=1 表示使用scalar register作为第二操作数")
        print("  - neg2=1 表示对第二操作数取负")
        print()
        print(f"✓ 应该识别为: tadd.ttr.f32.neg2")
        print(f"✓ 操作数: T{td}, T{ts1}, r{rs2} (with neg2)")
        
    else:
        print("✗ 不完全匹配已知模式")
        print("这可能是一个新的指令变体或者解析有误")
    
    print()
    print("=== 解码器修复建议 ===")
    print("问题在于extract_instruction_name函数中：")
    print("1. tuop_110分支需要扩展以支持tuop_110+tuop_010组合")
    print("2. 需要检查这种dual-tuop向量指令的特殊情况")
    print("3. 当前代码只处理了memory指令，没有处理vector指令的tuop_110变体")
    
if __name__ == "__main__":
    analyze_instruction_detailed()
