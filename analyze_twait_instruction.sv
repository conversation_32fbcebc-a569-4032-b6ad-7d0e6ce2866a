// Analyze instruction 0x2086507b bit fields
module analyze_twait_instruction;

    reg [31:0] test_instruction;
    reg [6:0] ace_op;
    reg [2:0] tuop;
    reg [2:0] ctrluop;
    reg [2:0] waitop;
    reg [4:0] rs1;
    reg [1:0] rw;
    reg is_tile, is_sync_wait;
    reg mem_bit;
    
    initial begin
        test_instruction = 32'h2086507b;
        
        $display("=== Analyzing Instruction 0x%08x ===", test_instruction);
        $display("Binary: %b", test_instruction);
        
        // Extract key fields
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        ctrluop = test_instruction[25:23];
        waitop = test_instruction[30:28];
        rs1 = test_instruction[19:15];
        mem_bit = test_instruction[26];
        rw = test_instruction[31:30];
        
        $display("");
        $display("=== Bit Field Analysis ===");
        $display("ACE_OP[6:0]:    %b (%h) - %s", ace_op, ace_op, 
                ace_op == 7'b1111011 ? "TILE INSTRUCTION" : "NOT TILE");
        $display("RW[31:30]:      %b (%d)", rw, rw);
        $display("WAITOP[30:28]:  %b (%d)", waitop, waitop);
        $display("CTRLUOP[25:23]: %b (%d)", ctrluop, ctrluop);
        $display("MEM_BIT[26]:    %b (%d)", mem_bit, mem_bit);
        $display("RS1[19:15]:     %b (%d) - register a%d/x%d", rs1, rs1, rs1, rs1);
        $display("TUOP[14:12]:    %b (%d)", tuop, tuop);
        
        // Instruction analysis
        is_tile = (ace_op == 7'b1111011);
        is_sync_wait = is_tile && (tuop == 3'b101);
        
        $display("");
        $display("=== Instruction Classification ===");
        $display("Is tile instruction:     %s", is_tile ? "YES" : "NO");
        $display("Is tuop_101 (sync):      %s", tuop == 3'b101 ? "YES" : "NO");
        $display("Should be sync/wait:     %s", is_sync_wait ? "YES" : "NO");
        
        if (is_tile && tuop == 3'b101) begin
            $display("");
            $display("=== TUOP_101 Analysis ===");
            $display("CTRLUOP = %d, WAITOP = %d", ctrluop, waitop);
            
            case (ctrluop)
                3'b000: $display("CTRLUOP 000: tsync operations");
                3'b001: begin
                    $display("CTRLUOP 001: twait operations");
                    case (waitop)
                        3'b000: $display("  WAITOP 000: twait.i.ls (load/store immediate)");
                        3'b001: $display("  WAITOP 001: twait.r.ls (load/store register)");
                        3'b010: $display("  WAITOP 010: twait.r.tacp_cg (tacp commit group)");
                        3'b011: $display("  WAITOP 011: twait.r.rmtfence (remote fence)");
                        3'b100: $display("  WAITOP 100: tkill.r");
                        3'b111: $display("  WAITOP 111: twait (basic)");
                        default: $display("  WAITOP %d: unknown", waitop);
                    endcase
                end
                3'b010: $display("CTRLUOP 010: trmtfence operations");
                default: $display("CTRLUOP %d: unknown", ctrluop);
            endcase
        end
        
        $display("");
        $display("=== Expected vs Current Decoding ===");
        $display("Expected: twait.r.tacp_cg a2");
        $display("Current:  unknown_sync");
        $display("");
        
        // Check what should be the correct decoding
        if (is_tile && tuop == 3'b101 && ctrluop == 3'b001 && waitop == 3'b010) begin
            $display("✓ This should be: twait.r.tacp_cg x%d (a%d)", rs1, rs1);
            $display("✓ Matches expected pattern for twait.r.tacp_cg");
        end else begin
            $display("✗ Does not match expected pattern");
        end
        
        $display("");
        $display("=== Problem Identification ===");
        $display("The issue is likely in extract_instruction_name() function:");
        $display("1. Current logic only handles ctrluop=001 && waitop=111");
        $display("2. Missing support for other waitop values like 010 (tacp_cg)");
        $display("3. Need to add comprehensive waitop decoding");
    end

endmodule
