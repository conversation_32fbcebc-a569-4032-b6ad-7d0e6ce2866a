# Tile Extension ISA Disassembler

这是一个用于Tile Extension指令集架构的二进制反编译器，能够将二进制指令转换为可读的汇编代码字符串。

## 功能特性

- **完整的指令支持**: 支持215个Tile Extension指令，包括内存操作、矩阵乘法、控制寄存器、同步等指令类型
- **多种位宽支持**: 支持32位、64位、96位和128位指令，自动检测指令位宽
- **智能字段提取**: 自动提取指令中的寄存器、立即数等字段
- **汇编语法格式化**: 输出符合汇编语法的指令字符串
- **错误处理**: 对无效输入和未知指令提供适当的错误处理

## 文件说明

- `tile_disassembler.py` - 主要的反编译器实现
- `test_disassembler.py` - 基础测试套件
- `comprehensive_test.py` - 全面的测试程序
- `demo_disassembler.py` - 演示程序
- `check_encoding.py` - 原有的指令编码检查程序

## 使用方法

### 命令行使用

```bash
# 反编译32位指令
python3 tile_disassembler.py 0x407b

# 反编译64位指令
python3 tile_disassembler.py 0x8000007b0000607b

# 反编译96位指令
python3 tile_disassembler.py 0xf260707b8000007b0000697b

# 反编译128位指令
python3 tile_disassembler.py 0xf260707bf260707b8000047b3800617b

# 反编译多个指令（自动检测位宽）
python3 tile_disassembler.py 0x407b 0x8000007b0000607b 0xf260707b8000007b0000697b

# 列出所有支持的指令
python3 tile_disassembler.py --list
```

### 编程接口使用

```python
from tile_disassembler import TileDisassembler

# 创建反编译器实例
disasm = TileDisassembler()

# 反编译十六进制字符串（自动检测位宽）
result = disasm.disassemble_hex_string("0x8000007b0000607b")
print(result)  # 输出: tld.trii.linear.u32.global t0, (x0)

# 反编译二进制数据（指定位宽）
result = disasm.disassemble_instruction(0x8000007b0000607b, 64)
print(result)  # 输出: tld.trii.linear.u32.global t0, (x0)

# 反编译96位指令
result = disasm.disassemble_hex_string("0xf260707b8000007b0000697b")
print(result)  # 输出: tld.trvi.asp.index.u32.global t0, (x0)

# 获取所有支持的指令列表
instructions = disasm.list_instructions()
```

## 测试和演示

### 运行基础测试
```bash
python3 test_disassembler.py
```

### 运行全面测试
```bash
python3 comprehensive_test.py
```

### 运行演示程序
```bash
python3 demo_disassembler.py
```

### 交互式测试
```bash
python3 test_disassembler.py --interactive
```

## 支持的指令类型

反编译器支持以下类型的指令：

1. **内存操作指令** (174个)
   - `tld.*` - Tile Load指令 (32/64/96位)
   - `tst.*` - Tile Store指令 (64/96位)
   - `tacp.*` - Tile Copy指令 (128位)
   - 支持linear、stride、index、block等访问模式

2. **矩阵乘法指令** (4个)
   - `tmma.*` - Tile Matrix Multiply Add指令 (64位)

3. **控制/状态寄存器指令** (3个)
   - `tcsrr.*` - CSR读取指令 (32位)
   - `tcsrw.*` - CSR写入指令 (32位)

4. **同步指令** (6个)
   - `twait.*` - 等待指令 (32位)
   - `tsync.*` - 同步指令 (32位)

5. **ACE扩展指令** (28个)
   - `ace_*` - Andes Custom Extension指令 (32位)

按位宽分布：
- **32位指令**: 37个 (主要是CSR、同步和ACE指令)
- **64位指令**: 157个 (主要是内存操作和矩阵乘法)
- **96位指令**: 17个 (复杂的索引内存操作)
- **128位指令**: 4个 (tile复制操作)

## 指令格式示例

```
# 32位CSR指令
0x407b -> tcsrw.i 0x0

# 64位内存加载指令
0x8000007b0000607b -> tld.trii.linear.u32.global t0, (x0)

# 96位索引内存加载指令
0xf260707b8000007b0000697b -> tld.trvi.asp.index.u32.global t0, (x0)

# 128位tile复制指令
0xf260707bf260707b8000047b3800617b -> tacp.rvv.asp2.dsttm 0, 0, x0, 0, 0, 0, 0

# ACE_op字段识别
0x7b -> ace_mem_high_rs1_0_rd_0_rs4_0 0, 0, 0, 0, 0
```

## 技术实现

### 指令解析流程

1. **加载指令定义**: 从`encode/`和`ace_encode/`目录读取wavedrom格式的指令定义
2. **解析字段布局**: 计算每个字段在指令中的位置和掩码
3. **指令匹配**: 使用位掩码匹配找到对应的指令定义
4. **字段提取**: 从二进制数据中提取各个字段的值
5. **格式化输出**: 根据指令类型格式化为汇编语法

### 位字段计算

反编译器正确处理wavedrom格式中的位字段定义：
- 字段按MSB到LSB顺序定义
- 支持固定位（attr字段）和可变位（name字段）
- 自动计算匹配掩码和匹配值

### 指令格式化

不同类型的指令使用不同的格式化规则：
- 内存指令: `instr Td, (rs1), rs2/imm`
- 矩阵乘法: `tmma Td, Ts1, Ts2`
- CSR指令: `tcsrr rd, csr_addr, rs1/imm`
- 同步指令: `twait sync_id/cnt`

## 依赖要求

- Python 3.6+
- PyYAML (用于解析指令定义文件)

安装依赖：
```bash
pip install pyyaml
```

## 项目结构

```
tile-extension/
├── encode/                    # 指令编码定义目录
├── ace_encode/               # ACE扩展指令编码定义
├── tile_disassembler.py      # 主反编译器
├── test_disassembler.py      # 基础测试
├── comprehensive_test.py     # 全面测试
├── demo_disassembler.py      # 演示程序
├── check_encoding.py         # 原有编码检查程序
└── README.md                 # 本文档
```

## 贡献

欢迎提交问题报告和改进建议。在提交代码前，请确保：

1. 运行所有测试并通过
2. 添加适当的测试用例
3. 遵循现有的代码风格

## 许可证

本项目遵循项目原有的许可证条款。
