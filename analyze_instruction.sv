module analyze_instruction;
    reg [63:0] instruction;
    reg [31:0] word1, word2;
    
    initial begin
        instruction = 64'h207b1009e4fb;
        word1 = instruction[31:0];   // 0x1009e4fb
        word2 = instruction[63:32];  // 0x207b
        
        $display("=== 分析指令 0x%016x ===", instruction);
        $display("Word 1: 0x%08x = %032b", word1, word1);
        $display("Word 2: 0x%08x = %032b", word2, word2);
        $display("");
        
        // 分析 Word 1 的关键位域
        $display("Word 1 位域分析:");
        $display("  [6:0]   ACE_OP: %07b = 0x%02x (%s)", word1[6:0], word1[6:0], 
                 word1[6:0] == 7'h7B ? "TILE" : "NOT TILE");
        $display("  [14:12] tuop: %03b = %0d", word1[14:12], word1[14:12]);
        $display("  [11:10] vecuop1: %02b = %0d", word1[11:10], word1[11:10]);
        $display("  [9]     immen: %b", word1[9]);
        $display("  [8]     rsen: %b", word1[8]);
        $display("  [24:20] rs2: %05b = %0d", word1[24:20], word1[24:20]);
        $display("  [30:25] memuop: %06b = %0d", word1[30:25], word1[30:25]);
        $display("  [28]    neg2: %b", word1[28]);
        $display("  [27]    neg1: %b", word1[27]);
        $display("");
        
        // 分析 Word 2 的关键位域
        $display("Word 2 位域分析:");
        $display("  [6:0]   ACE_OP: %07b = 0x%02x", word2[6:0], word2[6:0]);
        $display("  [14:12] tuop: %03b = %0d", word2[14:12], word2[14:12]);
        $display("  [19:16] vecuop2[5:2]: %04b = %0d", word2[19:16], word2[19:16]);
        $display("");
        
        // 判断指令类型
        $display("指令模式分析:");
        if (word1[6:0] == 7'h7B) begin
            $display("  ✓ 这是一个TILE指令");
            
            case (word1[14:12])
                3'b110: begin
                    $display("  ✓ tuop_110 (多车道内存指令)");
                    case (word2[14:12])
                        3'b010: $display("  ✓ 第二字tuop_010 (向量ALU)");
                        3'b000: $display("  ✓ 第二字tuop_000 (内存操作)");
                        default: $display("  ? 第二字tuop_%03b", word2[14:12]);
                    endcase
                end
                3'b010: $display("  ✓ tuop_010 (向量操作)");
                default: $display("  ? tuop_%03b", word1[14:12]);
            endcase
            
            // 检查tadd模式
            if (word1[11:10] == 2'b10 && word2[19:16] == 4'b0000) begin
                $display("  ✓ vecuop1=10 + vecuop2[5:2]=0000 => tadd操作");
                if (word1[8] && !word1[9]) begin
                    $display("  ✓ rsen=1, immen=0 => ttr变体");
                    if (word1[28]) begin
                        $display("  ✓ neg2=1 => 带neg2修饰符");
                        $display("  结论: 这应该是 tadd.ttr.f32.neg2");
                    end
                end
            end
        end
        
        $finish;
    end
endmodule
