# Memory Operation
## Share Memory地址信息
参考[SIPU v1.5架构变动概要](https://siorigin.feishu.cn/docx/AgMadCwMjoJksQxJvJecDT2RnJd)
### Local Chip Local PEC Local PE Share Memory地址信息
| 位范围 | 字段名称 | 描述 | 大小（位）| 备注 |
| - | - | - | - | - |
| 47-45 | **110** | 这些bits表示该地址为Local Chip | 3 | |
| 44-36 | **000000010** | 这些bits表示该地址为Share Memory | 9 | |
| 35-33 | （保留为全0） | 未使用 | 3 | 该模式未使用。 |
| 32-31 | （保留为全0） | 未使用 | 2 | 在一个PEC中选择4个PE。|
| 30-24 | （保留为全0） | 未使用 | 7 | 未来扩展使用。|
| 23-22 | **MODE** | **模式选择字段** | 2 | 支持2种模式（00,01），分别代表模式0和模式1。|
| 21-20 | （保留） | 未使用 | 2 | 未来扩展使用。|
| 19-0 | SMEM Address | Local PE的Share Memory地址 | 2 | 可寻址范围：0x0–0xFFFFF（1MB）|

| 模式 | 描述 |
| - | - |
| 模式0 | 访问的地址的MODE字段为00时，访问的local L2B空间从smembase起始。 |
| 模式1 | 访问的地址的MODE字段为01时，访存的local L2B空间从0起始。|
### Local Chip Local PEC Remote PE Share Memory地址信息
| 位范围 | 字段名称 | 描述 | 大小（位）| 备注 |
| - | - | - | - | - |
| 47-45 | **110** | 这些bits表示该地址为Local Chip | 3 | |
| 44-36 | **000000010** | 这些bits表示该地址为Share Memory | 9 | |
| 35-33 | （保留为全0） | 未使用 | 3 | 该模式未使用。 |
| 32-31 | **PE_ID** | **PE选择字段** | 2 | 在一个PEC中选择4个PE。|
| 30-24 | （保留为全0） | 未使用 | 7 | 未来扩展使用。|
| 23-22 | **MODE=10** | **模式选择字段** | 2 | 10，代表模式2，可见下表。|
| 21-20 | （保留为全0） | 未使用 | 2 | 未来扩展使用。|
| 19-0 | SMEM Address | 选定PE的Share Memory地址 | 2 | 可寻址范围：0x0–0xFFFFF（1MB）|

| 模式 | 描述 |
| - | - |
| 模式2 | 访问的地址的MODE字段为10时，访存的L2B空间即物理地址。|
### Local Chip Share Memory地址信息(全局地址)
| 位范围 | 字段名称 | 描述 | 大小（位）| 备注 |
| - | - | - | - | - |
| 47-45 | **110** | 这些bits表示该地址为Local Chip | 3 | |
| 44-36 | **000000010** | 这些bits表示该地址为Share Memory | 9 | |
| 35-33 | **PEC_ID** | **PEC选择字段** | 3 | 选择chip中的PEC。|
| 32-31 | **PE_ID** | **PE选择字段** | 2 | 在一个PEC中选择4个PE。|
| 30-24 | （保留为全0） | 未使用 | 7 | 未来扩展使用。|
| 23-22 | **MODE=11** | **模式选择字段** | 2 | 11，代表模式3，可见下表。|
| 21-20 | （保留为全0） | 未使用 | 2 | 未来扩展使用。|
| 19-0 | SMEM Address | 选定PE的Share Memory地址 | 2 | 可寻址范围：0x0–0xFFFFF（1MB）|

| 模式 | 描述 |
| - | - |
| 模式3 | 访问的地址的MODE字段为11时，访存的L2B空间即物理地址。|

### Remote Chip Share Memory地址信息(全局地址)
| 位范围 | 字段名称 | 描述 | 大小（位）| 备注 |
| - | - | - | - | - |
| 47-45 | **101** | 这些bits表示该地址为Remote Chip Chip Share Memory | 3 | |
| 44-36 | **CHIP_ID** |  | 89 | |
| 35-33 | **PEC_ID** | **PEC选择字段** | 3 | 选择chip中的PEC。|
| 32-31 | **PE_ID** | **PE选择字段** | 2 | 在一个PEC中选择4个PE。|
| 30-24 | （保留为全0） | 未使用 | 7 | 未来扩展使用。|
| 23-22 | **MODE=11** | **模式选择字段** | 2 | 11，代表模式3，可见下表。|
| 21-20 | （保留为全0） | 未使用 | 2 | 未来扩展使用。|
| 19-0 | SMEM Address | 选定PE的Share Memory地址 | 2 | 可寻址范围：0x0–0xFFFFF（1MB）|

| 模式 | 描述 |
| - | - |
| 模式3 | 访问的地址的MODE字段为11时，访存的L2B空间即物理地址。|

##  Tile Unit-stride Load/Store
**该类指令使用LSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表unit-stride。 |
| memuop | 000000代表32Byte-load，001000代表32Byte-store。 |
| tilesize | tilesize为000、001、010、011是表示连续访问几个 tile（m1/m2/m4/m8），即1024B/2048B/4096B。<br>tilesize为111、110、101是表示连续访问几分之一的tile（mf2/mf4/mf8），即512B/256B/128B。<br>在该模式下一个tile操作一个1024B. |
| Td/Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定读入和写回的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，需32Byte对齐。 |
| imm1 | 立即数表示在rs1地址上加的32Byte倍数的offset，不支持负数。 |
| imm2 | 立即数表示在rs1地址上加的1024Byte倍数的offset，不支持负数。 |
| rs3 | 64-bit整型标量寄存器编号，表示在rs1地址上加的偏移，需32Byte对齐。该偏移值为无符号数，不支持负数。 |
| tm/tmask | tile mask，表示32B维度的mask，由tile mask寄存器确定是否执行，该mask仅在tilesize为m1/mf2/mf4/mf8时有效。 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |

#### 伪代码
以[SIPU 1.0 TLSU架构设计方案*******.1](https://siorigin.feishu.cn/docx/TEL8d9XBMou1gkxcXj0cTAdkn2g#GjV4drrmcopHuAxm8WFcv9YwnYf)为准，下列伪代码也可供参考
```
bits(64) address;
bits(256) data; // 32 Bytes
bits(32) mask;
if regoffen
    address = rs1+rs3+imm1*32;
else
    address = rs1+((imm2<<5)+imm1)*32;
if SMEM_access && address in local PE L2B && address in mode 0
    address += CSR(tsmembase);

if tilesize > 3
    for t = 0 to tilesize
        for e = 0 to (1024/32 - 1)
            if (tilesize == 0 && tmask && mask[e])
                if (memuop == 000000)
                    data = mem_load_32B(address + e*32);
                    T[Td+t][e] = data;
                else
                    data = T[Ts1+t][e];
                    mem_store_32B(address + e*32, data);
else
    for e = 0 to (128*(tilesize - 4))
        if (tmask && mask[e])
            if (memuop == 000000)
                data = mem_load_32B(address + e*32);
                T[Td+t][e] = data;
            else
                data = T[Ts1+t][e];
                mem_store_32B(address + e*32, data);
```
### Tile Unit-stride Global Memory Load
`tld.trir.linear.u32.global.[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, rs3`
@import "encode/tuop_000/memuop_000000/lsuop_00/offseten_1/rmten_0/tld.trir.linear.u32.global.md"
`tld.trir.linear.u32.global.remote.[unorder].[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, rs3`
@import "encode/tuop_000/memuop_000000/lsuop_00/offseten_1/rmten_1/tld.trir.linear.u32.global.remote.md"
`tld.trii.linear.u32.global.[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, imm2`
@import "encode/tuop_000/memuop_000000/lsuop_00/offseten_0/rmten_0/tld.trii.linear.u32.global.md"
`tld.trii.linear.u32.global.remote.[unorder].[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, imm2`
@import "encode/tuop_000/memuop_000000/lsuop_00/offseten_0/rmten_1/tld.trii.linear.u32.global.remote.md"
### Tile Unit-stride Global Memory Store
`tst.trir.linear.u32.global.[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, rs3`
@import "encode/tuop_000/memuop_001000/lsuop_00/offseten_1/rmten_0/tst.trir.linear.u32.global.md"
`tst.trir.linear.u32.global.remote.[unorder].[early_resp].[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, rs3`
@import "encode/tuop_000/memuop_001000/lsuop_00/offseten_1/rmten_1/tst.trir.linear.u32.global.remote.md"
`tst.trii.linear.u32.global.[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, imm2`
@import "encode/tuop_000/memuop_001000/lsuop_00/offseten_0/rmten_0/tst.trii.linear.u32.global.md"
`tst.trii.linear.u32.global.remote.[unorder].[early_resp].[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, imm2`
@import "encode/tuop_000/memuop_001000/lsuop_00/offseten_0/rmten_1/tst.trii.linear.u32.global.remote.md"
### Tile Unit-stride Share Memory Load
`tld.trir.linear.u32.share.[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, rs3`
@import "encode/tuop_001/memuop_000000/lsuop_00/offseten_1/rmten_0/tld.trir.linear.u32.share.md"
`tld.trir.linear.u32.share.remote.[unorder].[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, rs3`
@import "encode/tuop_001/memuop_000000/lsuop_00/offseten_1/rmten_1/tld.trir.linear.u32.share.remote.md"
`tld.trii.linear.u32.share.[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, imm2`
@import "encode/tuop_001/memuop_000000/lsuop_00/offseten_0/rmten_0/tld.trii.linear.u32.share.md"
`tld.trii.linear.u32.share.remote.[unorder].[m2/m4/m8/mf2/mf4/mf8].[tm] Td, (rs1), imm1, imm2`
@import "encode/tuop_001/memuop_000000/lsuop_00/offseten_0/rmten_1/tld.trii.linear.u32.share.remote.md"
### Tile Unit-stride Share Memory Store
`tst.trir.linear.u32.share.[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, rs3`
@import "encode/tuop_001/memuop_001000/lsuop_00/offseten_1/rmten_0/tst.trir.linear.u32.share.md"
`tst.trir.linear.u32.share.remote.[unorder].[early_resp].[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, rs3`
@import "encode/tuop_001/memuop_001000/lsuop_00/offseten_1/rmten_1/tst.trir.linear.u32.share.remote.md"
`tst.trii.linear.u32.share.[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, imm2`
@import "encode/tuop_001/memuop_001000/lsuop_00/offseten_0/rmten_0/tst.trii.linear.u32.share.md"
`tst.trii.linear.u32.share.remote.[unorder].[early_resp].[m2/m4/m8/mf2/mf4/mf8].[tm] Ts1, (rs1), imm1, imm2`
@import "encode/tuop_001/memuop_001000/lsuop_00/offseten_0/rmten_1/tst.trii.linear.u32.share.remote.md"
## Tile Strided Load/Store
**该类指令使用LSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 01代表strided。 |
| memuop | 000000代表32Byte-load，001000代表32Byte-store。 |
| Td/Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定读入和写回的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，需32Byte对齐。 |
| rs2 | 64-bit整型标量寄存器编号，表示在rs1地址上加的32Byte倍数的stride，为无符号数。load指令支持0和正数，store指令仅支持正数。 |
| imm2 | 立即数表示在rs1地址上加的offset，数值是32Byte的倍数，不支持负数。 |
| rs3 | 64-bit整型标量寄存器编号，表示在rs1地址上加的偏移，需32Byte对齐。该偏移值为无符号数，**不支持负数，但可支持为0**。 |
| tm/tmask | tile mask，表示32B维度的mask，由tile mask寄存器确定是否执行 |

#### 伪代码

以[SIPU 1.0 TLSU架构设计方案*******.2](https://siorigin.feishu.cn/docx/TEL8d9XBMou1gkxcXj0cTAdkn2g#GjV4drrmcopHuAxm8WFcv9YwnYf)为准，下列伪代码也可供参考

```
bits(64) address;
bits(256) data;
bits(32) mask;
if offseten
    address = rs1+rs3;
else
    address = rs1+(imm2<<5);
if SMEM_access && address in local PE L2B && address in mode 0
    address += CSR(tsmembase);

for e = 0 to (1024/32 - 1)
    if (tmask && mask[e])
        if (memuop == 000000)
            data = mem_load_32B(address + e*rs2*32);
            T[Td][e] = data;
        else
            data = T[Ts1][e];
            mem_store_32B(address + e*rs2*32, data);
```
### Tile Strided Global Memory Load
`tld.trrr.stride.u32.global.[tm] Td, (rs1), rs2, rs3`
@import "encode/tuop_000/memuop_000000/lsuop_01/offseten_1/tld.trrr.stride.u32.global.md"
`tld.trri.stride.u32.global.[tm] Td, (rs1), rs2, imm2`
@import "encode/tuop_000/memuop_000000/lsuop_01/offseten_0/tld.trri.stride.u32.global.md"
### Tile Stride Global Memory Store
`tst.trrr.stride.u32.global.[tm] Ts1, (rs1), rs2, rs3`
@import "encode/tuop_000/memuop_001000/lsuop_01/offseten_1/tst.trrr.stride.u32.global.md"
`tst.trri.stride.u32.global.[tm] Ts1, (rs1), rs2, imm2`
@import "encode/tuop_000/memuop_001000/lsuop_01/offseten_0/tst.trri.stride.u32.global.md"
### Tile Strided Share Memory Load
`tld.trrr.stride.u32.share.[tm] Td, (rs1), rs2, rs3`
@import "encode/tuop_001/memuop_000000/lsuop_01/offseten_1/tld.trrr.stride.u32.share.md"
`tld.trri.stride.u32.share.[tm] Td, (rs1), rs2, imm2`
@import "encode/tuop_001/memuop_000000/lsuop_01/offseten_0/tld.trri.stride.u32.share.md"
### Tile Stride Share Memory Store
`tst.trrr.stride.u32.share.[tm] Ts1, (rs1), rs2, rs3`
@import "encode/tuop_001/memuop_001000/lsuop_01/offseten_1/tst.trrr.stride.u32.share.md"
`tst.trri.stride.u32.share.[tm] Ts1, (rs1), rs2, imm2`
@import "encode/tuop_001/memuop_001000/lsuop_01/offseten_0/tst.trri.stride.u32.share.md"
## Tile Indexed Load/Store
**该类指令使用LSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 10代表index模式。 |
| memuop | 000000代表32Byte-load，001000代表32Byte-store。 |
| Td/Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定读入和写回的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，需32Byte对齐。 |
| vs2 | 1024-bit向量寄存器编号，读出的向量寄存器中的内容是32bitx32的offset vertor，其中的每个元素表示在rs1地址上加的32Byte倍数的offset。load/store指令支持index为**0和正负数**。若store指令的index vector中，有几个index值相同，vector中编号靠后的数据将覆盖编号靠前的数据。 |
| imm2 | 立即数表示在rs1地址上加的offset，数值是32Byte的倍数，不支持负数。 |
| rs3 | 64-bit整型标量寄存器编号，表示在rs1地址上加的偏移，需32Byte对齐。该偏移值为无符号数，不支持负数。 |
| tm/tmask | tile mask，表示32B维度的mask，由tile mask寄存器确定是否执行 |

这类指令中用到 VS2 的 index vector 是通过额外加一条 ASP store 指令写入 tile core 执行的。见[ASP vector input](#asp-vector-input)
**汇编指令有两种格式：**
- 96bit带asp字段的指令
- 64bit不带asp字段的指令和一条ASP store指令
- 若该指令的64bit后续的并非ASP store指令，硬件无法检测错误，需在编译器算子库保证。
- 该指令仅能在vset(i)vl(i)设置SEW=32, vl=32, lmul=1时使用，用于和32bits的元素进行交互。

#### 伪代码
以[SIPU 1.0 TLSU架构设计方案*******.3](https://siorigin.feishu.cn/docx/TEL8d9XBMou1gkxcXj0cTAdkn2g#PPsvddEecoKK09xjzELc86DhnVg)为准，下列伪代码也可供参考
```
bits(64) address;
bits(256) data;
bits(32) mask;
if offseten
    address = rs1+rs3;
else
    address = rs1+(imm2<<5);
if SMEM_access && address in local PE L2B && address in mode 0
    address += CSR(tsmembase);

for e = 0 to (1024/32 - 1)
    if (tmask && mask[e])
        if (memuop == 000000)
            data = mem_load_32B(address + vs2[e]*32);
            T[Td][e] = data;
        else
            data = T[Ts1][e];
            mem_store_32B(address + vs2[e]*32, data);
```
### Tile Indexed Global Memory Load
`tld.trvr.asp.index.u32.global.[tm] Td, (rs1), vs2, rs3`
`tld.trvr.index.u32.global.[tm] Td, (rs1), vs2, rs3和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_000/memuop_000000/lsuop_10/offseten_1/tld.trvr.asp.index.u32.global.md"
`tld.trvi.asp.index.u32.global.[tm] Td, (rs1), vs2, imm2`
`tld.trvi.index.u32.global.[tm] Td, (rs1), vs2, imm2和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_000/memuop_000000/lsuop_10/offseten_0/tld.trvi.asp.index.u32.global.md"
### Tile Indexed Global Memory Store
`tst.trvr.asp.index.u32.global.[tm] Ts1, (rs1), vs2, rs3`
`tst.trvr.index.u32.global.[tm] Ts1, (rs1), vs2, rs3和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_000/memuop_001000/lsuop_10/offseten_1/tst.trvr.asp.index.u32.global.md"
`tst.trvi.asp.index.u32.global.[tm] Ts1, (rs1), vs2, imm2`
`tst.trvi.index.u32.global.[tm] Ts1, (rs1), vs2, imm2和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_000/memuop_001000/lsuop_10/offseten_0/tst.trvi.asp.index.u32.global.md"
### Tile Indexed Share Memory Load
`tld.trvr.asp.index.u32.share.[tm] Td, (rs1), vs2, rs3`
`tld.trvr.index.u32.share.[tm] Td, (rs1), vs2, rs3和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_001/memuop_000000/lsuop_10/offseten_1/tld.trvr.asp.index.u32.share.md"
`tld.trvi.asp.index.u32.share.[tm] Td, (rs1), vs2, imm2`
`tld.trvi.index.u32.share.[tm] Td, (rs1), vs2, imm2和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_001/memuop_000000/lsuop_10/offseten_0/tld.trvi.asp.index.u32.share.md"
### Tile Indexed Share Memory Store
`tst.trvr.asp.index.u32.share.[tm] Ts1, (rs1), vs2, rs3`
`tst.trvr.index.u32.share.[tm] Ts1, (rs1), vs2, rs3和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_001/memuop_001000/lsuop_10/offseten_1/tst.trvr.asp.index.u32.share.md"
`tst.trvi.asp.index.u32.share.[tm] Ts1, (rs1), vs2, imm2`
`tst.trvi.index.u32.share.[tm] Ts1, (rs1), vs2, imm2和taspin.vec vs2`是可拆分为[63:0]和[95:64]两条指令的汇编格式
@import "encode/tuop_001/memuop_001000/lsuop_10/offseten_0/tst.trvi.asp.index.u32.share.md"
## Tile Block Load/Store (No MX)
**该类指令使用LSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表非mx format load/store，01代表mx4或mx8 load/store，10代表mx6的load/store。 |
| memuop | 000001代表block-load，001001代表block-store。 |
| tilesize | tilesize为0、1、2是表示连续访问几个tile（m1、m2、m4）。在非MX6模式下，1个tile是1个tile寄存器；在MX6模式下，1个tile是3个tile寄存器 |
| Td/Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定读入和写回的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址。rs1需按照256Byte对齐。 |
| rs3 | 64-bit整型标量寄存器编号，表示基于base起始的的tile的坐标，不支持负数。|
| imm2 | 5-bit立即数，表示基于base起始的tile的坐标，不支持负数。 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |
#### 伪代码
见[SIPU 1.0 TLSU 架构设计方案*******](https://siorigin.feishu.cn/docx/TEL8d9XBMou1gkxcXj0cTAdkn2g#L8tZdlLpPoSmNYx2OJGcGtNOnoS)
### Tile Block Global Memory Load
`tld.trr.blk.global.[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_000/memuop_000001/lsuop_00/offseten_1/rmten_0/tld.trr.blk.global.md"
`tld.trr.blk.global.remote.[unorder].[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_000/memuop_000001/lsuop_00/offseten_1/rmten_1/tld.trr.blk.global.remote.md"
`tld.tri.blk.global.[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_000/memuop_000001/lsuop_00/offseten_0/rmten_0/tld.tri.blk.global.md"
`tld.tri.blk.global.remote.[unorder].[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_000/memuop_000001/lsuop_00/offseten_0/rmten_1/tld.tri.blk.global.remote.md"
### Tile Block Global Memory Store
`tst.trr.blk.global.[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_000/memuop_001001/lsuop_00/offseten_1/rmten_0/tst.trr.blk.global.md"
`tst.trr.blk.global.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_000/memuop_001001/lsuop_00/offseten_1/rmten_1/tst.trr.blk.global.remote.md"
`tst.tri.blk.global.[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_00/offseten_0/rmten_0/tst.tri.blk.global.md"
`tst.tri.blk.global.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_00/offseten_0/rmten_1/tst.tri.blk.global.remote.md"
### Tile Block Share Memory Load
`tld.trr.blk.share.[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_001/memuop_000001/lsuop_00/offseten_1/rmten_0/tld.trr.blk.share.md"
`tld.trr.blk.share.remote.[unorder].[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_001/memuop_000001/lsuop_00/offseten_1/rmten_1/tld.trr.blk.share.remote.md"
`tld.tri.blk.share.[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_001/memuop_000001/lsuop_00/offseten_0/rmten_0/tld.tri.blk.share.md"
`tld.tri.blk.share.remote.[unorder].[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_001/memuop_000001/lsuop_00/offseten_0/rmten_1/tld.tri.blk.share.remote.md"
### Tile Block Share Memory Store
`tst.trr.blk.share.[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_00/offseten_1/rmten_0/tst.trr.blk.share.md"
`tst.trr.blk.share.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_00/offseten_1/rmten_1/tst.trr.blk.share.remote.md"
`tst.tri.blk.share.[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_001/memuop_001001/lsuop_00/offseten_0/rmten_0/tst.tri.blk.share.md"
`tst.tri.blk.share.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_001/memuop_001001/lsuop_00/offseten_0/rmten_1/tst.tri.blk.share.remote.md"
## Tile Block MX4/8 Load/Store
**该类指令使用LSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表非mx format load/store，01代表mx4或mx8 load/store，10代表mx6的load/store。 |
| memuop | 000001代表block-load，001001代表block-store。 |
| tilesize | tilesize为0、1、2是表示连续访问几个tile（m1、m2、m4）。在非MX6模式下，1个tile是1个tile寄存器；在MX6模式下，1个tile是3个tile寄存器 |
| Td/Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定读入和写回的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址。rs1需按照256Byte对齐。 |
| rs3 | 64-bit整型标量寄存器编号，表示基于base起始的的tile的坐标，不支持负数。操作1 tile，按照1对齐；操作2 tiles，需要按照2对齐；操作4 tiles，需要按照4对齐。 |
| imm2 | 5-bit立即数，表示基于base起始的tile的坐标，不支持负数。操作1 tile，按照1对齐；操作2 tiles，需要按照2对齐；操作4 tiles，需要按照4对齐。 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |
#### 伪代码
见[SIPU 1.0 TLSU 架构设计方案*******](https://siorigin.feishu.cn/docx/TEL8d9XBMou1gkxcXj0cTAdkn2g#L8tZdlLpPoSmNYx2OJGcGtNOnoS)
### Tile Block MX4/8 Global Memory Load
`tld.trr.blk.mx48.global.[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_000/memuop_000001/lsuop_01/offseten_1/rmten_0/tld.trr.blk.mx48.global.md"
`tld.trr.blk.mx48.global.remote.[unorder].[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_000/memuop_000001/lsuop_01/offseten_1/rmten_1/tld.trr.blk.mx48.global.remote.md"
`tld.tri.blk.mx48.global.[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_000/memuop_000001/lsuop_01/offseten_0/rmten_0/tld.tri.blk.mx48.global.md"
`tld.tri.blk.mx48.global.remote.[unorder].[m2/m4] Td, (rs1), imm2
@import "encode/tuop_000/memuop_000001/lsuop_01/offseten_0/rmten_1/tld.tri.blk.mx48.global.remote.md"
### Tile Block MX4/8 Global Memory Store
`tst.trr.blk.mx48.global.[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_000/memuop_001001/lsuop_01/offseten_1/rmten_0/tst.trr.blk.mx48.global.md"
`tst.trr.blk.mx48.global.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_000/memuop_001001/lsuop_01/offseten_1/rmten_1/tst.trr.blk.mx48.global.remote.md"
`tst.tri.blk.mx48.global.[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_01/offseten_0/rmten_0/tst.tri.blk.mx48.global.md"
`tst.tri.blk.mx48.global.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_01/offseten_0/rmten_1/tst.tri.blk.mx48.global.remote.md"
### Tile Block MX4/8 Share Memory Load
`tld.trr.blk.mx48.share.[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_001/memuop_000001/lsuop_01/offseten_1/rmten_0/tld.trr.blk.mx48.share.md"
`tld.trr.blk.mx48.share.remote.[unorder].[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_001/memuop_000001/lsuop_01/offseten_1/rmten_1/tld.trr.blk.mx48.share.remote.md"
`tld.tri.blk.mx48.share.[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_001/memuop_000001/lsuop_01/offseten_0/rmten_0/tld.tri.blk.mx48.share.md"
`tld.tri.blk.mx48.share.remote.[unorder].[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_001/memuop_000001/lsuop_01/offseten_0/rmten_1/tld.tri.blk.mx48.share.remote.md"
### Tile Block MX4/8 Share Memory Store
`tst.trr.blk.mx48.share.[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_01/offseten_1/rmten_0/tst.trr.blk.mx48.share.md"
`tst.trr.blk.mx48.share.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_01/offseten_1/rmten_1/tst.trr.blk.mx48.share.remote.md"
`tst.tri.blk.mx48.share.[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_001/memuop_001001/lsuop_01/offseten_0/rmten_0/tst.tri.blk.mx48.share.md"
`tst.trr.blk.mx48.share.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_01/offseten_1/rmten_1/tst.trr.blk.mx48.share.remote.md"
## Tile Block MX6 Load/Store
**该类指令使用LSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表非mx format load/store，01代表mx4或mx8 load/store，10代表mx6的load/store。 |
| memuop | 000001代表block-load，001001代表block-store。 |
| tilesize | tilesize为0、1、2是表示连续访问几个tile（m1、m2、m4）。在非MX6模式下，1个tile是1个tile寄存器；在MX6模式下，1个tile是3个tile寄存器 |
| Td/Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定读入和写回的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址。rs1需按照256Byte对齐。 |
| rs3 | 64-bit整型标量寄存器编号，表示基于base起始的的tile的坐标，不支持负数。操作1 tile，按照1对齐；操作2 tiles，需要按照2对齐；操作4 tiles，需要按照4对齐。 |
| imm2 | 5-bit立即数，表示基于base起始的tile的坐标，不支持负数。操作1 tile，按照1对齐；操作2 tiles，需要按照2对齐；操作4 tiles，需要按照4对齐。 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |
#### 伪代码
见[SIPU 1.0 TLSU 架构设计方案*******](https://siorigin.feishu.cn/docx/TEL8d9XBMou1gkxcXj0cTAdkn2g#L8tZdlLpPoSmNYx2OJGcGtNOnoS)
### Tile Block MX6 Global Memory Load
`tld.trr.blk.mx6.global.[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_000/memuop_000001/lsuop_10/offseten_1/rmten_0/tld.trr.blk.mx6.global.md"
`tld.trr.blk.mx6.global.remote.[unorder].[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_000/memuop_000001/lsuop_10/offseten_1/rmten_1/tld.trr.blk.mx6.global.remote.md"
`tld.tri.blk.mx6.global.[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_000/memuop_000001/lsuop_10/offseten_0/rmten_0/tld.tri.blk.mx6.global.md"
`tld.tri.blk.mx6.global.remote.[unorder].[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_000/memuop_000001/lsuop_10/offseten_0/rmten_1/tld.tri.blk.mx6.global.remote.md"
### Tile Block MX6 Global Memory Store
`tst.trr.blk.mx6.global.[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_000/memuop_001001/lsuop_10/offseten_1/rmten_0/tst.trr.blk.mx6.global.md"
`tst.trr.blk.mx6.global.remote.[unorder].[early_resp].[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_10/offseten_1/rmten_1/tst.trr.blk.mx6.global.remote.md"
`tst.tri.blk.mx6.global.[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_10/offseten_0/rmten_0/tst.tri.blk.mx6.global.md"
`tst.tri.blk.mx6.global.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_000/memuop_001001/lsuop_10/offseten_0/rmten_1/tst.tri.blk.mx6.global.remote.md"
### Tile Block MX6 Share Memory Load
`tld.trr.blk.mx6.share.[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_001/memuop_000001/lsuop_10/offseten_1/rmten_0/tld.trr.blk.mx6.share.md"
`tld.trr.blk.mx6.share.remote.[unorder].[early_resp].[m2/m4] Td, (rs1), rs3`
@import "encode/tuop_001/memuop_000001/lsuop_10/offseten_1/rmten_1/tld.trr.blk.mx6.share.remote.md"
`tld.tri.blk.mx6.share.[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_001/memuop_000001/lsuop_10/offseten_0/rmten_0/tld.tri.blk.mx6.share.md"
`tld.tri.blk.mx6.share.remote.[unorder].[early_resp].[m2/m4] Td, (rs1), imm2`
@import "encode/tuop_001/memuop_000001/lsuop_10/offseten_0/rmten_1/tld.tri.blk.mx6.share.remote.md"
### Tile Block MX6 Share Memory Store
`tst.trr.blk.mx6.share.[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_10/offseten_1/rmten_0/tst.trr.blk.mx6.share.md"
`tst.trr.blk.mx6.share.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), rs3`
@import "encode/tuop_001/memuop_001001/lsuop_10/offseten_1/rmten_1/tst.trr.blk.mx6.share.remote.md"
`tst.tri.blk.mx6.share.[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_001/memuop_001001/lsuop_10/offseten_0/rmten_0/tst.tri.blk.mx6.share.md"
`tst.tri.blk.mx6.share.remote.[unorder].[early_resp].[m2/m4] Ts1, (rs1), imm2`
@import "encode/tuop_001/memuop_001001/lsuop_10/offseten_0/rmten_1/tst.tri.blk.mx6.share.remote.md"
## Tile Atomic Scalar Load
**该类指令使用LSU单元进行操作。该指令可访问所有的Share Memory/SIDRAM（包括跨C2C的Share Memory/SIDRAM空间），部分操作可通过PCIe访问Host CPU DRAM**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表scalar atomic load，01代表scalar atomic store, 10代表tile atomic store, 11代表scalar atomic broadcast store（将scalar的数据扩展为1024B atomic写入memory） |
| memuop | 1xxxxx代表各种atomic指令，uop类型见下方表格 |
| vec_size | 代表连续操作的标量数据的数量，该值为0/1/2代表v1/v2/v4，分别代表有几个指令类型的标量数据pack为一个1倍、2倍或4倍的数据参与操作。16bitx2为4Byte操作，16bitx4和32bitx2都认为是8Byte操作。**cas指令仅能支持x1，无法支持x2/x4** |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，根据atomic size对齐。2Byte操作按2Byte对齐，4Byte操作按4Byte对齐，8Byte操作按8Byte对齐。 |
| rs2 | 64-bit整型标量寄存器编号，普通运算中参与运算的值，以及cas和swap中的new value |
| rs3 | 64-bit整型标量寄存器编号，仅在atomic cas运算中作为compare value |
| rd | 64-bit整型标量寄存器编号，指定返回值写回标量寄存器的index。 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |

| memuop[5:3] | operation | Comments |
| - | - | - |
| 100 | add | For U32/U64: <br>i. carry out, no overflow, discard carry<br>For I32/I64:<br>i. limit result to MIN...MAX(no overflow)<br>For F32/BF16/FP16:<br>i. rounds to nearest even<br>ii. subnormal inputs and results don't flush to zero<br>iii. Format refer to [SIPU 1.0 TMAC 架构设计方案 v0.6](https://siorigin.feishu.cn/docx/LPaIdDLccomQNBxCrwEcjuNDnpg#Q22wdDn0AoXMCCxJ5NocN9Q7nzb)<br>iv. Special Data refer to [SIPU 1.0 TALU 架构设计方案 v1.0](https://siorigin.feishu.cn/docx/ZZpDd3iRcor0Lrx5MCncXSTwnP3#WoM3dLW5sotBoMxEq5gciTNZnqf)<br>v. Siformat function: f32_add_sub, bf16_add_sub, f16_add_sub |
| 101 | min |For F32/BF16/FP16:<br>i. TNAN flag default open and refer to [TALU CSR Fields](https://siorigin.feishu.cn/wiki/NCMfwcBjhiMdackpDpScX9UZnvb#share-UdizdPuNaoZJYmxEnnVc8vsonJd)<br>ii. Siformat function: f32_min, bf16_min, f16_min
| 110 | max |For F32/BF16/FP16:<br>i. TNAN flag default open and refer to [TALU CSR Fields](https://siorigin.feishu.cn/wiki/NCMfwcBjhiMdackpDpScX9UZnvb#share-UdizdPuNaoZJYmxEnnVc8vsonJd)<br>ii. Siformat function: f32_max, bf16_max, f16_max
| 111 | logic |

| memuop[2:0] | add operation | add rounding | min operation | max operation | logical |
| - | - | - | - | - | - |
| 000 | add_u32<br>(32bitx1 mode support access over PCIe) || min_u32 | max_u32 | and_b32 |
| 001 | add_i32 || min_i32 | max_i32 | or_b32 |
| 010 | add_f32 | RNE(rn) | min_f32 | max_f32 | xor_b32 |
| 100 | add_bf16 | RNE(rn) | min_bf16 | max_bf16 | inc_u32 |
| 101 | add_f16 | RNE(rn) | min_f16 | max_f16 | dec_u32 |
| 110 | add_u64<br>(64bitx1 mode support access over PCIe) || min_u64 | max_u64 | swap_b32<br>(32bitx1 mode support access over PCIe) |
| 111 | add_i64 || min_i64 | max_i64 | cas_b32<br>(only support 32bitx1, support access over PCIe) |

### Tile Atomic Global Memory Scalar Load
`tld.rrr.atom.atomop.global.[v2/v4] rd, (rs1), rs2`
举例`tld.rrr.atom.addbf16.global.[v2/v4] rd, (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_0/rmten_0/tld.rrr.atom.atomop.global.md"
`tld.rrr.atom.atomop.global.remote.[v2/v4] rd, (rs1), rs2`
举例`tld.rrr.atom.addbf16.global.remote.[v2/v4] rd, (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_1/tld.rrr.atom.atomop.global.remote.md"
`tld.rrr.atom.atomop.global.remote.unorder.[v2/v4] rd, (rs1), rs2`
举例`tld.rrr.atom.addbf16.global.remote.unorder.[v2/v4] rd, (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_0/tld.rrr.atom.atomop.global.remote.unorder.md"
`tld.rrrr.atom.casb32.global rd, (rs1), rs2, rs3`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_1/rmten_0/tld.rrrr.atom.casb32.global.md"
`tld.rrrr.atom.casb32.global.remote rd, (rs1), rs2, rs3`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_1/tld.rrrr.atom.casb32.global.remote.md"
`tld.rrrr.atom.casb32.global.remote.unorder rd, (rs1), rs2, rs3`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_0/tld.rrrr.atom.casb32.share.remote.unorder.md"
### Tile Atomic Share Memory Scalar Load
`tld.rrr.atom.atomop.share.[v2/v4] rd, (rs1), rs2`
举例`tld.rrr.atom.maxf16.share.[v2/v4] rd, (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_0/rmten_0/tld.rrr.atom.atomop.share.md"
`tld.rrr.atom.atomop.share.remote.[v2/v4] rd, (rs1), rs2`
举例`tld.rrr.atom.maxf16.share.remote.[v2/v4] rd, (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_1/tld.rrr.atom.atomop.share.remote.md"
`tld.rrr.atom.atomop.share.remote.unorder.[v2/v4] rd, (rs1), rs2`
举例`tld.rrr.atom.maxf16.share.remote.unorder.[v2/v4] rd, (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_0/tld.rrr.atom.atomop.share.remote.unorder.md"
`tld.rrrr.atom.casb32.share rd, (rs1), rs2, rs3`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_0/tld.rrrr.atom.casb32.share.md"
`tld.rrrr.atom.casb32.share.remote rd, (rs1), rs2, rs3`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_1/tld.rrrr.atom.casb32.share.remote.md"
`tld.rrrr.atom.casb32.share.remote.unorder rd, (rs1), rs2, rs3`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_0/tld.rrrr.atom.casb32.share.remote.unorder.md"
## Tile Atomic Scalar Store
**该类指令使用LSU单元进行操作。该指令可访问所有的Share Memory/SIDRAM（包括跨C2C的Share Memory/SIDRAM空间）**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表scalar atomic load，01代表scalar atomic store, 10代表tile atomic store, 11代表scalar atomic broadcast store（将scalar的数据扩展为1024B atomic写入memory） |
| memuop | 1xxxxx代表各种atomic指令，uop类型见下方表格 |
| vec_size | 代表连续操作的标量数据的数量，该值为0/1/2代表v1/v2/v4，分别代表有几个指令类型的标量数据pack为一个1倍、2倍或4倍的数据参与操作。16bitx2为4Byte操作，16bitx4和32bitx2都认为是8Byte操作。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，根据atomic size对齐。2Byte操作按2Byte对齐，4Byte操作按4Byte对齐，8Byte操作按8Byte对齐。 |
| rs2 | 64-bit整型标量寄存器编号，普通运算中参与运算的值 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |

| memuop[5:3] | operation |
| - | - |
| 100 | add |
| 101 | min |
| 110 | max |
| 111 | logic |

| memuop[2:0] | add operation | add Rounding | min operation | max operation | logical |
| - | - | - | - | - | - |
| 000 | add_u32 || min_u32 | max_u32 | and_b32 |
| 001 | add_i32 || min_i32 | max_i32 | or_b32 |
| 010 | add_f32 | RNE(rn) | min_f32 | max_f32 | xor_b32 |
| 100 | add_bf16 | RNE(rn) | min_bf16 | max_bf16 | inc_u32 |
| 101 | add_f16 | RNE(rn) | min_f16 | max_f16 | dec_u32 |
| 110 | add_u64 || min_u64 | max_u64 |
| 111 | add_i64 || min_i64 | max_i64 |
### Tile Atomic Global Memory Scalar Store
`tst.rr.atom.atomop.global.[v2/v4] (rs1), rs2`
举例`tst.rr.atom.minbf16.global.[v2/v4] (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_01/rmten_0/tst.rr.atom.atomop.global.md"
`tst.rr.atom.atomop.global.remote.[early_resp].[v2/v4] (rs1), rs2`
举例`tst.rr.atom.minbf16.global.remote.[early_resp].[v2/v4] (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_01/rmten_1/order_1/tst.rr.atom.atomop.global.remote.md"
`tst.rr.atom.atomop.global.remote.unorder.[early_resp].[v2/v4] (rs1), rs2`
举例`tst.rr.atom.minbf16.global.remote.unorder.[early_resp].[v2/v4] (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_01/rmten_1/order_0/tst.rr.atom.atomop.global.remote.unorder.md"

### Tile Atomic Share Memory Scalar Store
`tst.rr.atom.atomop.share.[v2/v4] (rs1), rs2`
举例`tst.rr.atom.minu64.share (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_01/rmten_0/tst.rr.atom.atomop.share.md"
`tst.rr.atom.atomop.share.remote.[early_resp].[v2/v4] (rs1), rs2`
举例`tst.rr.atom.minu64.share.remote.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_01/rmten_1/order_1/tst.rr.atom.atomop.share.remote.md"
`tst.rr.atom.atomop.share.remote.unorder.[early_resp].[v2/v4] (rs1), rs2`
举例`tst.rr.atom.minu64.share.remote.unorder.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_01/rmten_1/order_0/tst.rr.atom.atomop.share.remote.unorder.md"
## Tile Atomic Scalar Broadcast Store
**该类指令使用LSU单元进行操作。该指令可访问所有的Share Memory/SIDRAM（包括跨C2C的Share Memory/SIDRAM空间）**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表scalar atomic load，01代表scalar atomic store, 10代表tile atomic store, 11代表scalar atomic broadcast store（将scalar的数据扩展为1024B atomic写入memory） |
| memuop | 1xxxxx代表各种atomic指令，uop类型见下方表格 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，需按32Byte对齐。  |
| rs2 | 64-bit整型标量寄存器编号，普通运算中参与运算的值 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |

| memuop[5:3] | operation |
| - | - |
| 100 | add |
| 101 | min |
| 110 | max |
| 111 | logic |

| memuop[2:0] | add operation | add rounding | min operation | max operation | logical |
| - | - | - | - | - | - |
| 000 | add_u32 || min_u32 | max_u32 | and_b32 |
| 001 | add_i32 || min_i32 | max_i32 | or_b32 |
| 010 | add_f32 | RNE(rn) | min_f32 | max_f32 | xor_b32 |
| 100 | add_bf16 | RNE(rn) | min_bf16 | max_bf16 | inc_u32 |
| 101 | add_f16 | RNE(rn) | min_f16 | max_f16 | dec_u32 |
| 110 | add_u64 || min_u64 | max_u64 |
| 111 | add_i64 || min_i64 | max_i64 |
### Tile Atomic Global Memory Scalar Broadcast Store
`tst.rr.atom.atomop.global.broadcast (rs1), rs2`
举例`tst.rr.atom.andb32.global.broadcast (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_11/rmten_0/tst.rr.atom.atomop.global.broadcast.md"
`tst.rr.atom.atomop.global.remote.broadcast.[early_resp] (rs1), rs2`
举例`tst.rr.atom.andb32.global.remote.broadcast.[early_resp] (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_11/rmten_1/order_1/tst.rr.atom.atomop.global.remote.broadcast.md"
`tst.rr.atom.atomop.global.remote.broadcast.unorder.[early_resp] (rs1), rs2`
举例`tst.rr.atom.andb32.global.remote.broadcast.unorder.[early_resp] (rs1), rs2`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_11/rmten_1/order_0/tst.rr.atom.atomop.global.remote.broadcast.unorder.md"
### Tile Atomic Share Memory Scalar Broadcast Store
`tst.rr.atomxxx.share.broadcast (rs1), rs2`
举例`tst.rr.atom.decu32.share.broadcast (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_11/rmten_0/tst.rr.atom.atomop.share.broadcast.md"
`tst.rr.atomxxx.share.remote.broadcast.[early_resp] (rs1), rs2`
举例`tst.rr.atom.decu32.share.remote.broadcast.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_11/rmten_1/order_1/tst.rr.atom.atomop.share.remote.broadcast.md"
`tst.rr.atomxxx.share.remote.broadcast.unorder.[early_resp] (rs1), rs2`
举例`tst.rr.atom.decu32.share.remote.broadcast.unorder.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_11/rmten_1/order_0/tst.rr.atom.atomop.share.remote.broadcast.unorder.md"
## Tile Atomic Tile Store
**该类指令使用LSU单元进行操作。该指令可访问所有的Share Memory/SIDRAM（包括跨C2C的Share Memory/SIDRAM空间）**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，001为share memory，110代表低32bit指令|
| lsuop | 00代表scalar atomic load，01代表scalar atomic store, 10代表tile atomic store, 11代表scalar atomic broadcast store（将scalar的数据扩展为1024B atomic写入memory） |
| memuop | 1xxxxx代表各种atomic指令，uop类型见下方表格 |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定src tile的寄存器位置。 |
| rs1 | 64-bit整型标量寄存器编号，指定base地址，需按32Byte对齐。  |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |

| memuop[5:3] | operation |
| - | - |
| 100 | add |
| 101 | min |
| 110 | max |
| 111 | logic |

| memuop[2:0] | add operation | add rounding | min operation | max operation | logical |
| - | - | - | - | - | - |
| 000 | add_u32 || min_u32 | max_u32 | and_b32 |
| 001 | add_i32 || min_i32 | max_i32 | or_b32 |
| 010 | add_f32 | RNE(rn) | min_f32 | max_f32 | xor_b32 |
| 100 | add_bf16 | RNE(rn) | min_bf16 | max_bf16 | inc_u32 |
| 101 | add_f16 | RNE(rn) | min_f16 | max_f16 | dec_u32 |
| 110 | add_u64 || min_u64 | max_u64 |
| 111 | add_i64 || min_i64 | max_i64 |
### Tile Atomic Global Memory Tile Store
`tst.tr.atom.atomop.global (rs1), Ts1`
举例`tst.tr.atom.mini32.global (rs1), Ts1`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_10/rmten_0/tst.tr.atom.atomop.global.md"
`tst.tr.atom.atomop.global.remote.[early_resp] (rs1), Ts1`
举例`tst.tr.atom.mini32.global.remote.[early_resp] (rs1), Ts1`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_10/rmten_1/order_1/tst.tr.atom.atomop.global.remote.md"
`tst.tr.atom.atomop.global.remote.unorder.[early_resp] (rs1), Ts1`
举例`tst.tr.atom.mini32.global.remote.unorder.[early_resp] (rs1), Ts1`
@import "encode/tuop_000/memuop_1xxxxx/lsuop_10/rmten_1/order_0/tst.tr.atom.atomop.global.remote.unorder.md"
### Tile Atomic Share Memory Tile Store
`tst.tr.atom.atomop.share (rs1), Ts1`
举例`tst.tr.atom.minu64.share (rs1), Ts1`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_10/rmten_0/tst.tr.atom.atomop.share.md"
`tst.tr.atom.atomop.share.remote.[early_resp] (rs1), Ts1`
举例`tst.tr.atom.minu64.share.remote.[early_resp] (rs1), Ts1`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_10/rmten_1/order_1/tst.tr.atom.atomop.share.remote.md"
`tst.tr.atom.atomop.share.remote.unorder.[early_resp] (rs1), Ts1`
举例`tst.tr.atom.minu64.share.remote.unorder.[early_resp] (rs1), Ts1`
@import "encode/tuop_001/memuop_1xxxxx/lsuop_10/rmten_1/order_0/tst.tr.atom.atomop.share.remote.unorder.md"
## Tile Tensor Async Copy
**该类指令使用DTE单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，110代表低32bit指令，111为asp指令|
| tacpuop | 00连续数据的搬运(linear)，无需tensormap，只需要将rs1的地址连续搬运到rs4的地址。size为rs2指定。<br>01代表tiled tensormap表达的数据和tiled memory上进行数据搬运(tile)。其中仅有一个输入操作数(vs1或vs4)是tiled tensormap表达的数据块。另一个输入操作数(rs1或rs4)是连续tile的数据。<br>10代表linear tensormap表达的数据和tiled layout进行转换并传输(convert)。其中仅有一个输入操作数(vs1或vs4)是linear tensormap表达的数据块。另一个输入操作数(rs1或rs4)是连续tile的数据。 |
| dstrmt | 0: local chip 1: remote chip。|
| srcrmt | 0: local chip 1: remote chip。|
| dsttm | dst memory使用tensormap |
| srctm | src memory使用tensormap |
| mbaren | 1: mbarrier enable，开启后将会在执行完成后更新mbarrier object |
| multi(P1) | 广播到cluster中的4个PE中的L2B，指令中表示为broadcast |
| rs1 | 64-bit整型标量寄存器编号，指定src memory的地址。 |
| vs1 | 1024-bit向量寄存器编号，指定src tensormap。 |
| rs2 | 64-bit整型标量寄存器编号，传输的size。 |
| vs2 | 1024-bit向量寄存器编号，指定各个维度的坐标。 |
| rs3 | 64-bit整型标量寄存器编号，指定mbarrier同步的地址，需64bits/8Byte对齐。 |
| rs4 | 64-bit整型标量寄存器编号，指定dst memory的地址。|
| vs4 | 1024-bit向量寄存器编号，指定dst tensormap。 |

**- 该指令仅能在vset(i)vl(i)设置SEW=32, vl=32, lmul=1时使用，用于和32bits的元素进行交互。**

指令操作信息需参考[SiPU 1.0 DTE 架构设计方案](https://siorigin.feishu.cn/docx/SoN1dFhjrotUKDxrpXJcYgqGnXV)
**Tensormap定义**
@import "table/tensormap.csv"

**使用tensormap的汇编指令有两种格式：**
- 128bit带asp2字段的指令
- 64bit不带asp字段的指令和**SEW=32**的2条ASP store指令
- 若该指令的64bit后续的并非**SEW=32**的2条ASP store指令，硬件无法检测错误，需在编译器算子库保证。

使用mbar字段的指令需要按照下列伪代码的**mbarrier dec_tx_cnt**操作执行
```
bits(64) address;
bits(64) old_data, data; // 8 Bytes

address = rs3;
if address in local PE L2B
    address += CSR(tsmembase);
else
    raise fault;

old_data = mem_load_8B(address);
data = ((old_data - (1 << 32)) & 0x0000ffff00000000);
if ((data & 0x0000ffff0000ffff) == 0) {
    data |= ((old_data & 0x00000000ffff0000)>>16);
    data += (0x1 << 48);
}

mem_store_8B(address, data);
```

`tacp.rrr.linear.[dstrmt].[srcrmt].[broadcast], (rs1), rs2, (rs4)`
举例`tacp.rrr.linear.dstrmt (rs1), rs2, (rs4)`
@import "encode/tuop_000/memuop_011100/tmap_00/tacp.rrr.linear.md"

`tacp.rrrr.linear.mbar.[dstrmt].[srcrmt].[broadcast], (rs1), rs2, (rs3), (rs4)`
举例`tacp.rrrr.linear.mbar.srcrmt  (rs1), rs2, (rs3), (rs4)`
@import "encode/tuop_000/memuop_011100/tmap_01/tacp.rrrr.linear.mbar.md"

`tacp.rvv.asp2.tile/convert.dsttm.[dstrmt].[srcrmt].[broadcast] (rs1), vs2, vs4`
也可以表示为64bit指令`tacp.rvv.tile.dsttm.dstrmt (rs1), vs2, vs4`和2条32bit指令`taspin.vec`的组合
@import "encode/tuop_000/memuop_011100/tmap_10/srctm_0/dsttm_1/tacp.rvv.asp2.dsttm.md"

`tacp.vvr.asp2.tile/convert.srctm.[dstrmt].[srcrmt].[broadcast] vs1, vs2, (rs4)`
也可以表示为64bit指令`tacp.vvr.convert.srctm.srcrmt vs1, vs2, (rs4)`和2条32bit指令`taspin.vec`的组合
@import "encode/tuop_000/memuop_011100/tmap_10/srctm_1/dsttm_0/tacp.vvr.asp2.srctm.md"

`tacp.rvrv.asp2.tile/convert.mbar.dsttm.[dstrmt].[srcrmt].[broadcast] (rs1), vs2, (rs3), vs4`
也可以表示为64bit指令`tacp.rvrv.tile.mbar.dsttm.dstrmt.[broadcast] (rs1), vs2, (rs3), vs4`和2条32bit指令`taspin.vec`的组合
@import "encode/tuop_000/memuop_011100/tmap_11/srctm_0/dsttm_1/tacp.rvrv.asp2.mbar.dsttm.md"

`tacp.vvrr.asp2.tile/convert.mbar.srctm.[dstrmt].[srcrmt].[broadcast] vs1, vs2, (rs3), (rs4)`
也可以表示为64bit指令`tacp.vvrr.convert.mbar.srctm.srcrmt.[broadcast] vs1, vs2, (rs3), (rs4)`和2条32bit指令`taspin.vec`的组合
@import "encode/tuop_000/memuop_011100/tmap_11/srctm_1/dsttm_0/tacp.vvrr.asp2.mbar.srctm.md"

##  Tile Tensor Async Copy Commit Group
| Syntax | Description |
| - | - |
| tuop | 000时为global memory，110代表低32bit指令，111为asp指令|
| memuop | 010001为tacp.commit_group指令|

`tacp.commit_group`
@import "encode/tuop_000/memuop_010001/lsuop_00/tacp.commit_group.md"

## Memory Barrier
**该类指令使用TLSU单元进行操作**
| Syntax | Description |
| - | - |
| tuop | 001代表share memory指令，110代表低32bit指令|
| lsuop | 00代表scalar atomic load，01代表scalar atomic store |
| memuop | memuop=010000: mbar_init<br>memuop=010001: mbar_inc_tx_cnt<br>memuop=010010: mbar_arrive<br>memuop=010011: mbar_test_wait<br>memuop=010101: mbar_dec_tx_cnt |
| rs1 | 64-bit整型标量寄存器编号，指定地址，需按8Byte(64bit)对齐。 |
| rs2 | 64-bit整型标量寄存器编号，指定mbar的cnt值 |
| rd | 64-bit整型标量寄存器编号，指定返回值写回标量寄存器的index。 |
| rmt/remote | 指令中有该标签(**该指令编码中bit为1**)标明该指令为Remote SIPU Chip指令，通过C2C访问，这类指令不支持访问Local SIPU Chip |
| unorder | 指令中有该标签(**该指令编码中bit为0**)标明该Remote SIPU Chip指令的前序同类型（都是读或都是写）同地址指令在通路中不会按顺序发射。例如读后读同一个地址和写后写同一个地址，实际的执行不按照顺序进行。 |
| early_resp(!real_resp/!r_rsp) | 指令中有该标签标明(**该指令编码中bit为0**)该Remote SIPU Chip Store指令**不**会收到memory真正返回的reponse。指令结束时不能保证这次写一定写入对应Memory中 |

MBAR格式定义
| Element |# Bits | Start Bit | End bit | Meaning | Min Value | Max Value | Note |
| - | - | - | - | - | - | - | - |
| pending_arrival_cnt | 16 | 0 | 15 |Barrier当前phase还没有arrive的thread count。 | 0 | 65535 | 32768实际最大值(256chipx16clusterx4pex2thread) |
| expected_arrival_cnt | 16 | 16 | 31 | Barrier下一个phase完成需要的thread arrival count。 | 1 | 65535 | 32768实际最大值(256chipx16cluster*4pex2thread) |
| pending_tx_cnt | 16 | 32 | 47 | 当前phase还没有完成的tx count。SiPU定义简化为tacp指令数。 | -32768 | 32767 |
| current_phase_id | 16 | 48 | 63 | "barrier当前正在进行的phase id。如果用于查询的phase不等于该phase_id，表示phase已完成。" | 0 | 65535	| |

### tmbar.rr.init
`tmbar.rr.init (rs1), rs2`
@import "encode/tuop_001/memuop_010000/rmten_0/tmbar.rr.init.md"
`tmbar.rr.init.remote.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_010000/rmten_1/order_1/tmbar.rr.init.remote.md"
`tmbar.rr.init.remote.unorder.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_010000/rmten_1/order_0/tmbar.rr.init.remote.unorder.md"
#### 伪代码
以[SiPU 1.0 Sync 多核同步机制架构设计方案](https://siorigin.feishu.cn/docx/LXQAdkfy9op2TGxSsV0cN2eRnge#DI8Gdo7eJoO0Rex4TkNcgAD3n6b)为准，下列伪代码也可供参考
```
bits(64) address;
bits(64) data; // 8 Bytes

address = rs1;
if address in local PE L2B
    address += CSR(tsmembase);
else
    raise fault;

data = (rs2 & 0xffff) | ((rs2 & 0xffff) << 16);

mem_store_8B(address, data);
```
### tmbar.rr.inc_tx_cnt
`tmbar.rr.inc_tx_cnt (rs1), rs2`
@import "encode/tuop_001/memuop_010001/rmten_0/tmbar.rr.inc_tx_cnt.md"
`tmbar.rr.inc_tx_cnt.remote.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_010001/rmten_1/order_1/tmbar.rr.inc_tx_cnt.remote.md"
`tmbar.rr.inc_tx_cnt.remote.unorder.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_010001/rmten_1/order_0/tmbar.rr.inc_tx_cnt.remote.unorder.md"
#### 伪代码
以[SiPU 1.0 Sync 多核同步机制架构设计方案](https://siorigin.feishu.cn/docx/LXQAdkfy9op2TGxSsV0cN2eRnge#Viqqd0zzjobBZfxWOOCcZe9Zn3c)为准，下列伪代码也可供参考
```
bits(64) address;
bits(64) old_data, data; // 8 Bytes

address = rs1;
if address in local PE L2B
    address += CSR(tsmembase);
else
    raise fault;

old_data = mem_load_8B(address);
data = ((old_data + (rs2 << 32)) & 0x0000ffff0000ffff);
if ((data & 0x0000ffff0000ffff) == 0) {
    data |= ((old_data & 0x00000000ffff0000)>>16);
    data += (0x1 << 48);
}

mem_store_8B(address, data);
```

### tmbar.rr.dec_tx_cnt
`tmbar.rr.dec_tx_cnt (rs1), rs2`
@import "encode/tuop_001/memuop_010101/rmten_0/tmbar.rr.dec_tx_cnt.md"
`tmbar.rr.dec_tx_cnt.remote.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_010101/rmten_1/order_1/tmbar.rr.dec_tx_cnt.remote.md"
`tmbar.rr.dec_tx_cnt.remote.unorder.[early_resp] (rs1), rs2`
@import "encode/tuop_001/memuop_010101/rmten_1/order_0/tmbar.rr.dec_tx_cnt.remote.unorder.md"
#### 伪代码
以[SiPU 1.0 Sync 多核同步机制架构设计方案](https://siorigin.feishu.cn/docx/LXQAdkfy9op2TGxSsV0cN2eRnge#Viqqd0zzjobBZfxWOOCcZe9Zn3c)为准，下列伪代码也可供参考
```
bits(64) address;
bits(64) old_data, data; // 8 Bytes

address = rs1;
if address in local PE L2B
    address += CSR(tsmembase);
else
    raise fault;

old_data = mem_load_8B(address);
data = ((old_data - (rs2 << 32)) & 0x0000ffff0000ffff);
if ((data & 0x0000ffff0000ffff) == 0) {
    data |= ((old_data & 0x00000000ffff0000)>>16);
    data += (0x1 << 48);
}

mem_store_8B(address, data);
```
### tmbar.r.arrive
`tmbar.r.arrive (rs1)`
@import "encode/tuop_001/memuop_010010/return_0/rmten_0/tmbar.r.arrive.md"
`tmbar.r.arrive.remote.[early_resp] (rs1)`
@import "encode/tuop_001/memuop_010010/return_0/rmten_1/order_1/tmbar.r.arrive.remote.md"
`tmbar.r.arrive.remote.unorder.[early_resp] (rs1)`
@import "encode/tuop_001/memuop_010010/return_0/rmten_1/order_0/tmbar.r.arrive.remote.unorder.md"
#### 伪代码
以[SiPU 1.0 Sync 多核同步机制架构设计方案](https://siorigin.feishu.cn/docx/LXQAdkfy9op2TGxSsV0cN2eRnge#LP99dcgiJoVx37xOZqXc88qunLh)为准，下列伪代码也可供参考
```
bits(64) address;
bits(64) old_data, data; // 8 Bytes

address = rs1;
if address in local PE L2B
    address += CSR(tsmembase);

old_data = mem_load_8B(address);
data = ((old_data - 1) & 0xffff) | (old_data & 0xffffffffffff0000);
if ((data & 0x0000ffff0000ffff) == 0) {
    data |= ((old_data & 0x00000000ffff0000)>>16);
    data += (0x1 << 48);
}

mem_store_8B(address, data);
```
### tmbar.rr.arrive
`tmbar.rr.arrive rd, (rs1)`
@import "encode/tuop_001/memuop_010010/return_1/rmten_0/tmbar.rr.arrive.md"
`tmbar.rr.arrive.remote rd, (rs1)`
@import "encode/tuop_001/memuop_010010/return_1/rmten_1/order_1/tmbar.rr.arrive.remote.md"
`tmbar.rr.arrive.remote.unorder rd, (rs1)`
@import "encode/tuop_001/memuop_010010/return_1/rmten_1/order_0/tmbar.rr.arrive.remote.unorder.md"
#### 伪代码
以[SiPU 1.0 Sync 多核同步机制架构设计方案](https://siorigin.feishu.cn/docx/LXQAdkfy9op2TGxSsV0cN2eRnge#LP99dcgiJoVx37xOZqXc88qunLh)为准，下列伪代码也可供参考
```
bits(64) address;
bits(64) old_data, data; // 8 Bytes

address = rs1;
if address in local PE L2B
    address += CSR(tsmembase);

old_data = mem_load_8B(address);
data = ((old_data - 1) & 0xffff) | (old_data & 0xffffffffffff0000);
if ((data & 0x0000ffff0000ffff) == 0) {
    data |= ((old_data & 0x00000000ffff0000)>>16);
    data += (0x1 << 48);
}

mem_store_8B(address, data);
rd = (old_data >> 48);
```
### tmbar.rrr.test_wait
`tmbar.rrr.test_wait rd, (rs1), rs2`
@import "encode/tuop_001/memuop_010011/rmten_0/tmbar.rrr.test_wait.md"
`tmbar.rrr.test_wait.remote rd, (rs1), rs2`
@import "encode/tuop_001/memuop_010011/rmten_1/order_1/tmbar.rrr.test_wait.remote.md"
`tmbar.rrr.test_wait.remote.unorder rd, (rs1), rs2`
@import "encode/tuop_001/memuop_010011/rmten_1/order_0/tmbar.rrr.test_wait.remote.unorder.md"
#### 伪代码
以[SiPU 1.0 Sync 多核同步机制架构设计方案](https://siorigin.feishu.cn/docx/LXQAdkfy9op2TGxSsV0cN2eRnge#LP99dcgiJoVx37xOZqXc88qunLh)为准，下列伪代码也可供参考
```
bits(64) address;
bits(64) data; // 8 Bytes

address = rs1;
if address in local PE L2B
    address += CSR(tsmembase);
else
    raise fault;

data = mem_load_8B(address);
if ((data >> 48) != rs2) {
    rd = 1;
} else {
    rd = 0;
}
```