// Instruction Fetch Unit with Tile Extension Support
// Demonstrates practical usage of the tile decoder functions
// in a processor instruction fetch pipeline

`timescale 1ns/1ps

module instruction_fetch_unit (
    input  logic        clk,
    input  logic        rst_n,
    input  logic        fetch_enable,
    input  logic [31:0] instruction_word,
    input  logic        word_valid,
    
    output logic        instruction_ready,
    output logic [127:0] complete_instruction,
    output logic [1:0]   instruction_length_code, // 0=32, 1=64, 2=96, 3=128
    output logic         is_tile_instruction,
    output string        disassembly_string,
    output logic         fetch_next_word
);

    import tile_decoder_pkg::*;

    // Internal state
    typedef enum logic [1:0] {
        IDLE,
        COLLECTING,
        COMPLETE,
        ERROR
    } fetch_state_e;

    fetch_state_e current_state, next_state;
    instr_collector_t collector, next_collector;
    logic start_new_instruction;
    logic add_word;
    logic instruction_complete;

    // State machine for instruction collection
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            current_state <= IDLE;
            collector <= '{default: '0};
        end else begin
            current_state <= next_state;
            collector <= next_collector;
        end
    end

    // Next state logic
    always_comb begin
        next_state = current_state;
        next_collector = collector;
        start_new_instruction = 1'b0;
        add_word = 1'b0;
        instruction_complete = 1'b0;
        fetch_next_word = 1'b0;

        case (current_state)
            IDLE: begin
                if (fetch_enable && word_valid) begin
                    start_new_instruction = 1'b1;
                    next_state = COLLECTING;
                end
            end

            COLLECTING: begin
                if (word_valid) begin
                    if (collector.collected_words == 0) begin
                        // This shouldn't happen, but handle gracefully
                        start_new_instruction = 1'b1;
                    end else begin
                        add_word = 1'b1;
                    end

                    // Check if instruction will be complete after adding this word
                    if (next_collector.is_complete) begin
                        instruction_complete = 1'b1;
                        next_state = COMPLETE;
                    end else begin
                        fetch_next_word = 1'b1;
                    end
                end
            end

            COMPLETE: begin
                // Instruction is ready for consumption
                if (fetch_enable) begin
                    // Start next instruction if there's a new word
                    if (word_valid) begin
                        start_new_instruction = 1'b1;
                        next_state = COLLECTING;
                    end else begin
                        next_state = IDLE;
                    end
                end
            end

            ERROR: begin
                // Reset to idle on next fetch_enable
                if (fetch_enable) begin
                    next_state = IDLE;
                end
            end
        endcase

        // Handle collector updates
        if (start_new_instruction) begin
            next_collector = tile_decoder_pkg::init_collector(instruction_word);
            if (!next_collector.is_complete) begin
                fetch_next_word = 1'b1;
            end else begin
                instruction_complete = 1'b1;
                next_state = COMPLETE;
            end
        end else if (add_word) begin
            next_collector = tile_decoder_pkg::add_word_to_collector(
                collector, instruction_word);
        end
    end

    // Output assignments
    assign instruction_ready = (current_state == COMPLETE);
    assign complete_instruction = collector.instruction_data;
    assign instruction_length_code = collector.expected_length;
    assign is_tile_instruction = collector.is_tile_instr;

    // Generate disassembly string when instruction is complete
    always_comb begin
        if (instruction_ready && collector.is_tile_instr) begin
            disassembly_string = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
        end else if (instruction_ready) begin
            disassembly_string = "non_tile_instruction";
        end else begin
            disassembly_string = "incomplete";
        end
    end

    // Debug and monitoring
    `ifdef DEBUG_TILE_FETCH
    always_ff @(posedge clk) begin
        if (start_new_instruction) begin
            $display("[%0t] New instruction started: 0x%08x", $time, instruction_word);
            $display("  Is tile: %s, Expected length: %s", 
                    next_collector.is_tile_instr ? "YES" : "NO",
                    next_collector.expected_length.name());
        end
        
        if (add_word) begin
            $display("[%0t] Added word %0d: 0x%08x", $time, 
                    collector.collected_words + 1, instruction_word);
        end
        
        if (instruction_complete) begin
            $display("[%0t] Instruction complete: %s", $time, disassembly_string);
            $display("  Full instruction: 0x%032x", next_collector.instruction_data);
        end
    end
    `endif

endmodule

// Wrapper module for easy integration
module tile_instruction_processor (
    input  logic        clk,
    input  logic        rst_n,
    input  logic        enable,
    input  logic [31:0] instruction_stream [0:7], // Input instruction stream
    input  logic [2:0]  stream_length,            // Number of valid words in stream
    
    output logic        processing_complete,
    output string       disassembled_instructions [0:7],
    output logic [2:0]  num_instructions
);

    import tile_decoder_pkg::*;

    logic [31:0] current_word;
    logic word_valid;
    logic fetch_enable;
    logic instruction_ready;
    logic [127:0] complete_instruction;
    logic [1:0] instruction_length_code;
    logic is_tile_instruction;
    string disassembly_string;
    logic fetch_next_word;

    // Stream processing state
    logic [2:0] word_index;
    logic [2:0] instruction_count;
    logic processing_active;

    instruction_fetch_unit ifu (
        .clk(clk),
        .rst_n(rst_n),
        .fetch_enable(fetch_enable),
        .instruction_word(current_word),
        .word_valid(word_valid),
        .instruction_ready(instruction_ready),
        .complete_instruction(complete_instruction),
        .instruction_length_code(instruction_length_code),
        .is_tile_instruction(is_tile_instruction),
        .disassembly_string(disassembly_string),
        .fetch_next_word(fetch_next_word)
    );

    // Stream processing control
    always_ff @(posedge clk or negedge rst_n) begin
        if (!rst_n) begin
            word_index <= 0;
            instruction_count <= 0;
            processing_active <= 1'b0;
            processing_complete <= 1'b0;
        end else begin
            if (enable && !processing_active) begin
                // Start processing
                processing_active <= 1'b1;
                processing_complete <= 1'b0;
                word_index <= 0;
                instruction_count <= 0;
            end else if (processing_active) begin
                // Handle instruction completion
                if (instruction_ready) begin
                    disassembled_instructions[instruction_count] <= disassembly_string;
                    instruction_count <= instruction_count + 1;
                end

                // Handle word advancement
                if (fetch_next_word || instruction_ready) begin
                    if (word_index < stream_length - 1) begin
                        word_index <= word_index + 1;
                    end else begin
                        // End of stream
                        processing_active <= 1'b0;
                        processing_complete <= 1'b1;
                    end
                end
            end
        end
    end

    // Word selection and control signals
    always_comb begin
        current_word = instruction_stream[word_index];
        word_valid = processing_active && (word_index < stream_length);
        fetch_enable = processing_active;
    end

    assign num_instructions = instruction_count;

endmodule
