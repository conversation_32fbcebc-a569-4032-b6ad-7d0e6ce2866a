# ALU Operation
**该类指令使用ALU单元，reuse bit仅能用于后续的ALU指令**
参考[SIPU 1.0 TALU 架构设计方案](https://siorigin.feishu.cn/docx/ZZpDd3iRcor0Lrx5MCncXSTwnP3)
## Rounding Support
@import "table/alu_rnd.csv"
## Reuse Support
**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。**
举例，下列reuse方式**不能**保证功能行为
- reuse后寄存器id不同
`tadd.ttt.f32.reuse1 T0, T1, T2`
`tadd.ttt.f32 T0, T3, T2`
- reuse后被reuse寄存器被写入，后续的指令使用被reuse寄存器
`tadd.ttt.f32.reuse1 T0, T1, T2`
`tld.trii.linear.u32.global T1, (a0), 0, 0`
`tadd.ttt.f32 T0, T1, T2`
- reuse后被reuse寄存器被写入，后续reuse的指令使用被reuse寄存器
`tadd.ttt.f32.reuse1 T0, T1, T2`
`tld.trii.linear.u32.global T1, (a0), 0, 0`
`tadd.ttt.f32.reuse1 T0, T1, T2`
- 指令中reuse的操作数也是目的操作数
`tadd.ttt.f32.reuse1 T1, T1, T2`
## Tile Conversion Operations
| Syntax | Description |
| - | - |
| tuop | 010时为ALU类指令，110代表低32bit指令|
| vecuop1 | b'00: talu_cvt (conversion/quantization)<br>b'01: talu_1src (SFU，transpose)<br>b'10: talu_2src (ADD/MUL/MIN/MAX)<br>b'11: talu_3src (FMA)<br>|
| vecuop2 | 见后续的conversion uop encoding |
| Td | 8192-bit/1024-Byte tile寄存器编号，指定写回的寄存器位置。**该指令中，若TS1或TD操作多个寄存器，TS1和TD操作的寄存器中不能有任何一个相同** |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定src使用起始的tile寄存器。 |
| neg1 | 对conversion输入值取负 |
| reuse1 | src1 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| relu | 该bit为1时，在完成运算后的数据若小于0，则将值改为0 |
| sat(satfinite) | b'0: Non-satfinite<br> b'1: Satfinite |
| rnd | b'00: RNE和RNI为默认值，指令里无需显式指定<br> b'01: RTZ和RZI<br>b'10: RTN和RMI<br>b'11: RTP和RPI |
| vecen | 该bit为1时，conversion结果的粒度是1/8 Tile，且tileReg内是N个128B的向量。<br>该bti为0时，conversion结果的粒度是1 Tile。 |
| shape | vecen为0时：<br>00代表32 rows(r32)模式<br>01代表16 rows(r16)模式<br>10代表8 rows(r8)模式<br> |
| vecsize | 在vecen为1时有效：<br>00代表128B或384B(mxfp6)<br>01代表256B或768B(mxfp6)<br>10代表512B或1536B(mxfp6)<br>11代表1024B或3072B(mxfp6) |
### conversion uop
@import "table/conversion_uop.csv"
| uop类型 | TSAT | TNAN | TRELU | TIRND | TFRND | SATINIFINITE |
| - | - | - | - | - | - | - |
| f32->bf16 ||| [.relu] ||[.rne/.rtz/.rtn/.rtp] | [.sat] |
| f32->f16 ||| [.relu] ||[.rne/.rtz/.rtn/.rtp] | [.sat] |
| f16->f32 ||| [.relu] ||[.rne/.rtz/.rtn/.rtp] | [.sat] |
| bf16->f32 ||| [.relu] ||[.rne/.rtz/.rtn/.rtp] | [.sat] |
| s32->f32 ||| [.relu] ||[.rne/.rtz/.rtn/.rtp] | [.sat] |
| s32->bf16 ||| [.relu] ||[.rne/.rtz/.rtn/.rtp] | [.sat] |
| f32->(u)int8/4 ||| [.relu] |[.rni/.rzi/.rmi/.rpi] || [.sat] |
| bf16->(u)int8/4 ||| [.relu] |[.rni/.rzi/.rmi/.rpi] || [.sat] |
| f32->f8 ||| [.relu] ||[.rne] | [.sat] |
| f16->f8 ||| [.relu] ||[.rne] | [.sat] |
| bf16->f8 ||| [.relu] ||[.rne] | [.sat] |
| f32->mxf8/6/4 ||| [.relu] ||[.rne] | [.sat] |
| f16->mxf8/6/4 ||| [.relu] ||[.rne] | [.sat] |
| bf16->mxf8/6/4 ||| [.relu] ||[.rne] | [.sat] |
| f32->mxi8/4 ||| [.relu] ||[.rne] | [.sat] |
| f16->mxi8/4 ||| [.relu] ||[.rne] | [.sat] |
| bf16->mxi8/4 ||| [.relu] ||[.rne] | [.sat] |
#### matrix conversion
该指令用于matrix tile format的conversion，该指令需要指定r32/r16/r8模式。
`tcvt.tt.dtype.atype.r32/r16/r8.[rtz/rtn/rtp/rzi/rmi/rpi].[satfinite].[reuse1].[neg1].[relu] Td, Ts1`
`tcvt.tt.mxf8_e4m3/mxf8_e5m2/mxf6_e3m2/mxf6_e2m3/mxf4/mxi8/mxi4/f8_e4m3/f8_e5m2/f32/bf16/f16/u8/s8/u4/s4.f32/bf16/f16/s32.r32/r16/r8.[rtz/rtn/rtp/rzi/rmi/rpi].[satfinite].[reuse1].[neg1].[relu] Td, Ts1`

举例M32N32 conversion：
`tcvt.tt.mxf8_e4m3.bf16.r32 T4, T2`
@import "encode/tuop_010/vecuop1_00/rsen_0/vecen_0/tcvt.tt.md"
#### vector conversion
该指令用于vector tile的conversion，该指令无需指定r32/r16/r8模式，使用1x1024B的layout排布工作。
**该指令的结果可能仅写到Tile Reg的前一部分（如1/8、1/4、1/2）。该指令执行完成，Tile Reg中其余数据（如7/8、3/4、1/2）是不确定的。**

`tcvt.tt.vec.dtype.atype.[m2/m4/m8].[rtz/rtn/rtp/rzi/rmi/rpi].[satfinite].[reuse1].[neg1].[relu] Td, Ts1`
`tcvt.tt.vec.mxf8_e4m3/mxf8_e5m2/mxf6_e3m2/mxf6_e2m3/mxf4/mxi8/mxi4/f8_e4m3/f8_e5m2/f32/bf16/f16/u8/s8/u4/s4.f32/bf16/f16/s32.[m2/m4/m8].[rtz/rtn/rtp/rzi/rmi/rpi].[satfinite].[reuse1].[neg1].[relu] Td, Ts1`

举例vector 128B conversion：
`tcvt.tt.vec.mxf8_e5m2.f32 T4, T0`
@import "encode/tuop_010/vecuop1_00/rsen_0/vecen_1/tcvt.tt.vec.md"
## Tile Transpose Function Operations
| Syntax | Description |
| - | - |
| tuop | 010时为ALU类指令，110代表低32bit指令|
| vecuop1 | b'00: talu_cvt (conversion/quantization)<br>b'01: talu_1src (SFU，transpose)<br>b'10: talu_2src (ADD/MUL/MIN/MAX)<br>b'11: talu_3src (FMA)<br>|
| vecuop2 | 见后续的transpose uop encoding |
| Td | 8192-bit/1024-Byte tile寄存器编号，指定写回的寄存器位置。 |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定src使用起始的tile寄存器。 |
| reuse1 | src1 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| tmask | tile mask，表示32B维度的mask，由tile mask寄存器确定是否执行 |

@import "table/transpose_uop.csv"
`ttrans.tt.b8m32n32/b16m16n32/b16m32n16/b32m8n32/b32m32n8/b32m16n16.[reuse1].[tm] Td, Ts1`
**该类指令支持TD和TS1为同一个Tile Reg，但同tile reg对应的reuse不能开启**
@import "encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_11/ttrans.tt.md"
## Tile 2 Operand Vector Operations
| Syntax | Description |
| - | - |
| tuop | 010时为ALU类指令，110代表低32bit指令|
| vecuop1 | b'00: talu_cvt (conversion/quantization)<br>b'01: talu_1src (SFU，transpose)<br>b'10: talu_2src (ADD/MUL/MIN/MAX)<br>b'11: talu_3src (FMA)<br>|
| vecuop2 | 见后续的2op uop encoding |
| Td | 8192-bit/1024-Byte tile寄存器编号，指定写回的寄存器位置。 |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定src1使用起始的tile寄存器。 |
| Ts2 | 8192-bit/1024-Byte tile寄存器编号，指定src2使用起始的tile寄存器。 |
| rs2 | 64-bit/8-Byte tile寄存器编号，指定src2使用起始的scalar寄存器。<br>**注：RV的整型标量寄存器可用于传入浮点数。RV的浮点寄存器需move到整型寄存器后再参与ALU运算** |
| imm | 5bits立即数，用于索引下方special number参与运算|
| neg1 | 对输入src1的输入值取负，可以支持TileReg/ScalarReg/Immediate |
| neg2 | 对输入src2的输入值取负，可以支持TileReg/ScalarReg/Immediate |
| reuse1 | src1 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| reuse2 | src2 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| tmask | tile mask，表示32B维度的mask，由tile mask寄存器确定是否执行 |

**CSR中可以设置2OP指令中的一些属性：**
|  | 作用指令 | bit 0 等于 1 | bit 1 等于 1 | bit 2 等于 1 |
| - | - | - | - | - |
| tsat | MUL/ADD | F32_SAT | F16_SAT | BF16_SAT |
| tsatinfinite | MUL/ADD | F32_SATFINITE | F16_SATFINITE | BF16_SATFINITE |
| trelu | MUL/ADD | F32_RELU | F16_RELU | BF16_RELU |
| tnan | MIN/MAX | F32_NAN | F16_NAN | BF16_NAN |

@import "table/2op_uop.csv"
@import "table/special_num.csv"
**scalar reg中存放的需为浮点数，在执行该指令前需从浮点标量寄存器move到整型标量寄存器**
**该类指令支持TD和TS1、TD和TS2、TS1和TS2为同一个Tile Reg，但同tile reg对应的reuse不能开启**
### src1 src2 都是tile reg
`tadd.ttt.f32/bf16/f16.[reuse1].[reuse2].[neg1].[neg2].[tm] Td, Ts1, Ts2`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_0000/tadd.ttt.md"
`tmul.ttt.f32/bf16/f16.[reuse1].[reuse2].[neg1].[neg2].[tm] Td, Ts1, Ts2`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_0001/tmul.ttt.md"
`tmin.ttt.f32/bf16/f16.[reuse1].[reuse2].[tm] Td, Ts1, Ts2`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_1000/tmin.ttt.md"
`tmax.ttt.f32/bf16/f16.[reuse1].[reuse2].[tm] Td, Ts1, Ts2`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_1001/tmax.ttt.md"

### src1为tile reg，src2为scalar reg
**注：该场景下使用的scalar reg是从RV core的int reg中来的，浮点运算需要先move到int reg后参与浮点运算**
`tadd.ttr.f32/bf16/f16.[reuse1].[neg1].[neg2].[tm] Td, Ts1, rs2`
@import "encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0000/tadd.ttr.md"
`tmul.ttr.f32/bf16/f16.[reuse1].[neg1].[neg2].[tm] Td, Ts1, rs2`
@import "encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0001/tmul.ttr.md"
`tmin.ttr.f32/bf16/f16.[reuse1].[tm] Td, Ts1, rs2`
@import "encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_1000/tmin.ttr.md"
`tmax.ttr.f32/bf16/f16.[reuse1].[tm] Td, Ts1, rs2`
@import "encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_1001/tmax.ttr.md"

### src1为tile reg，src2为immediate number
`tadd.tti.f32/bf16/f16.[reuse1].[neg1].[neg2].[tm] Td, Ts1, imm`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_0000/tadd.tti.md"
`tmul.tti.f32/bf16/f16.[reuse1].[neg1].[neg2].[tm] Td, Ts1, imm`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_1//vecuop2[5_2]_0001/tmul.tti.md"
`tmin.tti.f32/bf16/f16.[reuse1].[tm] Td, Ts1, imm`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_1000/tmin.tti.md"
`tmax.tti.f32/bf16/f16.[reuse1].[tm] Td, Ts1, mm`
@import "encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_1001/tmax.tti.md"
## Tile 3 Oprand Vector Operations
| Syntax | Description |
| - | - |
| tuop | 010时为ALU类指令，110代表低32bit指令|
| vecuop1 | b'00: talu_cvt (conversion/quantization)<br>b'01: talu_1src (SFU，transpose)<br>b'10: talu_2src (ADD/MUL/MIN/MAX)<br>b'11: talu_3src (FMA)<br>|
| vecuop2 | 见后续的2op uop encoding |
| Td | 8192-bit/1024-Byte tile寄存器编号，指定写回的寄存器位置。 |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定src1使用起始的tile寄存器。 |
| Ts2 | 8192-bit/1024-Byte tile寄存器编号，指定src2使用起始的tile寄存器。 |
| rs2 | 64-bit/8-Byte tile寄存器编号，指定src2使用起始的scalar寄存器。<br>**注：RV的整型标量寄存器可用于传入浮点数。RV的浮点寄存器需move到整型寄存器后再参与ALU运算** |
| imm_2 | src2的5bits立即数，用于索引下方special number参与运算|
| Ts3 | 8192-bit/1024-Byte tile寄存器编号，指定src3使用起始的tile寄存器。 
| rs3 | 64-bit/8-Byte tile寄存器编号，指定src3使用起始的scalar寄存器。<br>**注：RV的整型标量寄存器可用于传入浮点数。RV的浮点寄存器需move到整型寄存器后再参与ALU运算** |
| imm_3 | src3的5bits立即数，用于索引下方special number参与运算|
| neg1 | 对输入src1的输入值取负，可以支持TileReg/ScalarReg/Immediate |
| neg2 | 对输入src2的输入值取负，可以支持TileReg/ScalarReg/Immediate |
| neg3 | 对输入src3的输入值取负，可以支持TileReg/ScalarReg/Immediate |
| reuse1 | src1 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| reuse2 | src2 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| reuse3 | src3 reuse，后续的ALU指令可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |
| tmask | tile mask，表示32B维度的mask，由tile mask寄存器确定是否执行 |

**CSR中可以设置3OP指令中的一些属性：**
|  | 作用指令 | bit 0 等于 1 | bit 1 等于 1 | bit 2 等于 1 |
| - | - | - | - | - |
| tsat | FMA | F32_SAT | F16_SAT | BF16_SAT |
| tsatinfinite | FMA | F32_SATFINITE | F16_SATFINITE | BF16_SATFINITE |
| trelu | FMA | F32_RELU | F16_RELU | BF16_RELU |

@import "table/3op_uop.csv"
**scalar reg中存放的需为浮点数，在执行该指令前需从浮点标量寄存器move到整型标量寄存器**
**该类指令支持TD和TS1、TD和TS2、TD和TS3、TS1和TS2、TS1和TS3、TS2和TS3为同一个Tile Reg，但同tile reg对应的reuse不能开启**
### src1为tile reg，src2为tile reg，src3为tile reg
Ts1、Ts2、Ts3 表示运算的输入，Td 表示运算输出。vecuop2 代表操作类型。
`tfma.tttt.f32/bf16/f16.[reuse1].[reuse2].[reuse3].[neg1].[neg2].[neg3].[tm] Td, Ts1, Ts2, Ts3`
@import "encode/tuop_010/vecuop1_11/rs2imm2en_0/rs3imm3en_0/tfma.tttt.md"
### src1为tile reg，src2为tile reg，src3为scalar reg
`tfma.tttr.f32/bf16/f16.[reuse1].[reuse2].[neg1].[neg2].[neg3].[tm] Td, Ts1, Ts2, rs3`
@import "encode/tuop_010/vecuop1_11/rs2imm2en_0/rs3en_1/tfma.tttr.md"
### src1为tile reg，src2为tile reg，src3为immediate number
`tfma.ttti.f32/bf16/f16.[reuse1].[reuse2].[neg1].[neg2].[neg3].[tm] Td, Ts1, Ts2, imm3`
@import "encode/tuop_010/vecuop1_11/rs2imm2en_0/imm3en_1/tfma.ttti.md"
### src1为tile reg，src2为scalar reg，src3为tile reg
`tfma.ttrt.f32/bf16/f16.[reuse1].[reuse3].[neg1].[neg2].[neg3].[tm] Td, Ts1, rs2, Ts3`
@import "encode/tuop_010/vecuop1_11/rs2en_1/rs3imm3en_0/tfma.ttrt.md"
### src1为tile reg，src2为scalar reg，src3为scalar reg
`tfma.ttrr.f32/bf16/f16.[reuse1].[neg1].[neg2].[neg3].[tm] Td, Ts1, rs2, rs3`
@import "encode/tuop_010/vecuop1_11/rs2en_1/rs3en_1/tfma.ttrr.md"
### src1为tile reg，src2为scalar reg，src3为immediate number
`tfma.ttri.f32/bf16/f16.[reuse1].[neg1].[neg2].[neg3].[tm] Td, Ts1, rs2, imm3`
@import "encode/tuop_010/vecuop1_11/rs2en_1/imm3en_1/tfma.ttri.md"
### src1为tile reg，src2为immediate number，src3为tile reg
`tfma.ttit.f32/bf16/f16.[reuse1].[reuse3].[neg1].[neg2].[neg3].[tm] Td, Ts1, imm2, Ts3`
@import "encode/tuop_010/vecuop1_11/imm2en_1/rs3imm3en_0/tfma.ttit.md"
### src1为tile reg，src2为immediate number，src3为scalar reg
`tfma.ttir.f32/bf16/f16.[reuse1].[neg1].[neg2].[neg3].[tm] Td, Ts1, imm2, rs3`
@import "encode/tuop_010/vecuop1_11/imm2en_1/rs3en_1/tfma.ttir.md"
### src1为tile reg，src2为immediate number，src3为immediate number
`tfma.ttii.f32/bf16/f16.[reuse1].[neg1].[neg2].[neg3].[tm] Td, Ts1, imm2, imm3`
@import "encode/tuop_010/vecuop1_11/imm2en_1/imm3en_1/tfma.ttii.md"

# Tile Special Function Operations
**该类指令使用单独的SFU单元，reuse bit仅能用于后续的SFU指令，不能作为ALU指令的reuse。**
| Syntax | Description |
| - | - |
| tuop | 010时为ALU类指令（含SFU类指令），110代表低32bit指令|
| vecuop1 | b'00: talu_cvt (conversion/quantization)<br>b'01: talu_1src (SFU，transpose)<br>b'10: talu_2src (ADD/MUL/MIN/MAX)<br>b'11: talu_3src (FMA)<br>|
| vecuop2 | 见后续的sfu uop encoding |
| Td | 8192-bit/1024-Byte tile寄存器编号，指定写回的寄存器位置。 |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定src使用起始的tile寄存器。 |
| reuse1 | src1 reuse，后续的**SFU指令**可复用该寄存器的值。reuse仅可作用于tile register。**reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。** |

**该类指令支持TD和TS1为同一个Tile Reg，但同tile reg对应的reuse不能开启**
## sfu uop
@import "table/sfu_uop.csv"
`tsfuop`可以为sgmd/sin/cos/exp2/log2/exp/rcp/sqrt/rsqrt/tanh。**EXP指令目前暂不支持**
Ts1 表示运算的输入，Td 表示运算输出。vecuop2 代表操作类型。
`tsfuop.tt.f32.[reuse1] Td, Ts1`
举例：`tsgmd.tt.f32.[reuse1] Td, Ts1`
@import "encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_00/tsfuop.tt.f32.md"
`tsfuop.tt.bf16.[reuse1] Td, Ts1`
举例：`tsin.tt.bf16.[reuse1] Td, Ts1`
@import "encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_01/tsfuop.tt.bf16.md"
`tsfuop.tt.f16.[reuse1] Td, Ts1`
举例：`texp2.tt.f16.[reuse1] Td, Ts1`
@import "encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_10/tsfuop.tt.f16.md"