# ACE_LOW Instruction Length Detection Fix

## Problem Description

The instruction `0x0C00E07B` was being incorrectly identified as a 32-bit instruction and decompiled as `ace_low`, when it should actually be the first 32-bit word of a 64-bit instruction.

## Root Cause Analysis

### 1. Incorrect Length Detection
The `get_instruction_length` function had overly restrictive logic for `tuop_110` instructions:
- Only recognized `memuop` values 0, 1, and 28 as multi-lane instructions
- All other `memuop` values were incorrectly classified as 32-bit ACE operations

### 2. Invalid Instruction Name
The `extract_instruction_name` function returned `ace_low` for unrecognized patterns, but `ace_low` is not a valid instruction name in the disassembly output.

## Instruction Analysis

### Binary Breakdown of 0x0C00E07B
```
0x0C00E07B = 00001100000000001110000001111011

Field breakdown:
- ACE_OP [6:0]   = 1111011 (0x7B) ✓ Tile instruction
- tuop [14:12]   = 110 (6)        ✓ tuop_110
- lsuop [11:10]  = 00 (0)         
- memuop [30:25] = 000110 (6)     ✗ Not in known multi-lane list
- rs2en [8]      = 0
- rs3en [31]     = 0
```

### Problem Identification
The instruction has `memuop = 6 (000110)`, which was not in the list of recognized multi-lane `memuop` values:
- `000000` (0): Standard memory operations ✓
- `000001` (1): Block memory operations ✓  
- `011100` (28): Tile copy operations ✓
- `000110` (6): **Missing from the list** ✗

## Solution Implemented

### 1. Conservative Length Detection Strategy
Modified the `get_instruction_length` function to use a conservative approach for `tuop_110`:

```systemverilog
// Before (overly restrictive)
else begin
    return INSTR_32BIT;  // Single-lane ACE operations
end

// After (conservative)
else begin
    // For other memuop values, use conservative approach:
    // Most tuop_110 instructions are 64-bit, only a few specific
    // patterns are 32-bit ACE instructions
    return INSTR_64BIT;  // Conservative: assume 64-bit for unknown patterns
end
```

### 2. Removed Invalid Instruction Names
Replaced `ace_low` returns with more descriptive error messages:

```systemverilog
// Before
return "ace_low"; // Regular ACE operation

// After  
return "unknown_64bit_memuop"; // Unknown 64-bit operation
```

### 3. Rationale for Conservative Approach
- Most `tuop_110` instructions in the ISA are multi-lane (64-bit or longer)
- True 32-bit ACE instructions have specific patterns that can be detected separately
- Better to err on the side of expecting more data than truncating a multi-word instruction

## Verification Results

### Before Fix
```
Instruction: 0x0C00E07B
Length: 32-bit (incorrect)
Complete: YES (incorrect)
Result: ace_low 0 (invalid)
```

### After Fix
```
Instruction: 0x0C00E07B  
Length: 64-bit (correct)
Complete: NO (correct, needs second word)
Result: unknown_64bit_memuop t120, (x20) (valid format)
```

### Regression Testing
- ✅ 32-bit CSR instructions still work: `0x0000407B` → `tcsrw.i 0`
- ✅ Known 64-bit instructions still work: `0x0000607B` → `tld.trii.linear.u32.global`
- ✅ No impact on other instruction types

## Files Modified

1. **tile_instruction_decoder.sv**
   - Modified `get_instruction_length` function for conservative `tuop_110` handling
   - Replaced `ace_low` returns with descriptive error messages
   - Added comments explaining the conservative strategy

## Impact and Benefits

### Immediate Fixes
1. **Correct Length Detection**: `0x0C00E07B` now properly detected as 64-bit
2. **No Invalid Instructions**: Eliminated `ace_low` from disassembly output
3. **Better Error Messages**: Unknown patterns now have descriptive names

### Long-term Improvements
1. **Robust Handling**: Conservative approach handles unknown instruction patterns gracefully
2. **Extensibility**: Easy to add specific detection for new instruction types
3. **Debugging**: Better error messages help identify unimplemented instruction patterns

## Future Enhancements

### 1. Specific ACE Instruction Detection
For true 32-bit ACE instructions, could add specific pattern detection:
```systemverilog
// Check for specific 32-bit ACE patterns
if (is_32bit_ace_pattern(first_word)) begin
    return INSTR_32BIT;
end else begin
    return INSTR_64BIT; // Conservative default
end
```

### 2. Extended memuop Support
Add support for more known `memuop` values as they are discovered:
```systemverilog
case (memuop)
    6'b000110: return INSTR_64BIT; // Specific pattern for memuop=6
    // Add more as needed
endcase
```

## Testing

Run the verification test:
```bash
iverilog -g2012 -o test_64bit_complete tile_instruction_decoder.sv test_64bit_complete.sv && ./test_64bit_complete
```

This confirms that:
- ✅ Instruction 0x0C00E07B is correctly identified as 64-bit
- ✅ No longer returns invalid `ace_low` instruction
- ✅ Conservative approach works for unknown patterns
- ✅ No regression in existing functionality
