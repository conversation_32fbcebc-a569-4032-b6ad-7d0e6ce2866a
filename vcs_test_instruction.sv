// VCS Test for instruction 0x8003907b8200647b
// This test verifies the field extraction fix for tld.trr.blk.mx48.share

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module vcs_test_instruction;

    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result;
        
        $display("=== VCS Test for Instruction 0x8003907b8200647b ===");
        $display("Testing field extraction fix for tld.trr.blk.mx48.share");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        full_instruction = {word2, word1};
        
        $display("Test Data:");
        $display("  Full instruction: 0x%016x", full_instruction);
        $display("  Word 1 (low):     0x%08x", word1);
        $display("  Word 2 (high):    0x%08x", word2);
        $display("");
        
        // Step 1: Initialize collector with first word
        $display("Step 1: Initialize collector");
        collector = tile_decoder_pkg::init_collector(word1);
        $display("  Is tile instruction: %s", collector.is_tile_instr ? "YES" : "NO");
        $display("  Expected length: %s", collector.expected_length.name());
        $display("  Complete after first word: %s", collector.is_complete ? "YES" : "NO");
        $display("");
        
        // Step 2: Add second word if needed
        if (!collector.is_complete) begin
            $display("Step 2: Add second word");
            collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
            $display("  Complete after second word: %s", collector.is_complete ? "YES" : "NO");
            $display("  Final instruction_data: 0x%032x", collector.instruction_data);
            $display("");
        end
        
        // Step 3: Disassemble if complete
        if (collector.is_complete) begin
            $display("Step 3: Disassemble instruction");
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Disassembly result: %s", result);
            $display("");
            
            // Step 4: Verify result
            $display("Step 4: Verify result");
            $display("  Expected result: tld.trr.blk.mx48.share t0, (x7), x0");
            $display("  Actual result:   %s", result);
            
            if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
                $display("  ✓ SUCCESS: Perfect match!");
            end else begin
                $display("  ✗ FAILURE: Results do not match");
            end
            $display("");
            
            // Step 5: Debug field extraction
            $display("Step 5: Debug field extraction");
            
            // Manual field extraction for verification
            logic [7:0] debug_td;
            logic [4:0] debug_rs1, debug_rs2;
            
            // Extract using the fixed positions
            debug_td = collector.instruction_data[32+30:32+23];  // word2[30:23]
            debug_rs1 = collector.instruction_data[32+19:32+15]; // word2[19:15]
            debug_rs2 = collector.instruction_data[24:20];       // word1[24:20]
            
            $display("  Manual field extraction:");
            $display("    td (word2[30:23]):  %0d", debug_td);
            $display("    rs1 (word2[19:15]): %0d", debug_rs1);
            $display("    rs2 (word1[24:20]): %0d", debug_rs2);
            
            // Verify expected values
            if (debug_td == 0 && debug_rs1 == 7 && debug_rs2 == 0) begin
                $display("  ✓ Field extraction values are correct!");
            end else begin
                $display("  ✗ Field extraction values are incorrect");
                $display("    Expected: td=0, rs1=7, rs2=0");
                $display("    Actual:   td=%0d, rs1=%0d, rs2=%0d", debug_td, debug_rs1, debug_rs2);
            end
            $display("");
            
            // Step 6: Test instruction identification
            $display("Step 6: Test instruction identification");
            
            logic [6:0] ace_op1, ace_op2;
            logic [2:0] tuop1, tuop2;
            logic [5:0] memuop;
            logic [1:0] lsuop;
            
            ace_op1 = collector.instruction_data[6:0];
            tuop1 = collector.instruction_data[14:12];
            memuop = collector.instruction_data[30:25];
            lsuop = collector.instruction_data[11:10];
            ace_op2 = collector.instruction_data[32+6:32+0];
            tuop2 = collector.instruction_data[32+14:32+12];
            
            $display("  Instruction identification fields:");
            $display("    ace_op1: 0x%02x (%s)", ace_op1, ace_op1 == 7'h7b ? "TILE" : "NOT_TILE");
            $display("    tuop1: %0d (%s)", tuop1, tuop1 == 3'b110 ? "110" : "OTHER");
            $display("    memuop: %0d (%s)", memuop, memuop == 6'b000001 ? "000001" : "OTHER");
            $display("    lsuop: %0d (%s)", lsuop, lsuop == 2'b01 ? "01" : "OTHER");
            $display("    ace_op2: 0x%02x", ace_op2);
            $display("    tuop2: %0d (%s)", tuop2, tuop2 == 3'b001 ? "001" : "OTHER");
            
            // Check identification conditions
            logic id_correct = (ace_op1 == 7'h7b) && (tuop1 == 3'b110) && 
                              (memuop == 6'b000001) && (lsuop == 2'b01) && (tuop2 == 3'b001);
            
            if (id_correct) begin
                $display("  ✓ Instruction identification is correct for tld.trr.blk.mx48.share");
            end else begin
                $display("  ✗ Instruction identification failed");
            end
            
        end else begin
            $display("ERROR: Instruction collection not complete!");
        end
        
        $display("");
        $display("=== VCS Test Complete ===");
        $display("");
        
        // Final summary
        if (collector.is_complete && result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
            $display("OVERALL RESULT: ✓ SUCCESS - Fix is working correctly!");
        end else begin
            $display("OVERALL RESULT: ✗ FAILURE - Fix needs more work");
        end
        
        $finish;
    end

endmodule
