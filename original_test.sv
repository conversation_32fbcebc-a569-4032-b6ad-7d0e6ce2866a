// Test original functionality before my changes
import tile_decoder_pkg::*;

module original_test;

    initial begin
        logic [31:0] test_instruction;
        logic [127:0] instruction_data;
        string result;

        $display("=== Original functionality test ===");
        
        // Test a known working instruction first (CSR instruction)
        test_instruction = 32'h0000407B; // This should be a CSR instruction
        instruction_data = {96'h0, test_instruction};
        
        $display("Testing CSR instruction: 0x%08x", test_instruction);
        
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("✓ Recognized as tile instruction");
            
            result = tile_decoder_pkg::extract_instruction_name(instruction_data, tile_decoder_pkg::INSTR_32BIT);
            $display("✓ CSR instruction result: %s", result);
        end else begin
            $display("✗ Not recognized as tile instruction");
        end
        
        $display("=== Test Complete ===");
    end

endmodule
