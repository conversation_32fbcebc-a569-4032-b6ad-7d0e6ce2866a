---
html:
  embed_local_images: false
  embed_svg: true
  offline: false
  toc: true

print_background: false
---
## [Tile Extension ISA Review反馈表](https://siorigin.feishu.cn/wiki/NQDbwniyBiV4CdknZVfcGadMn8b) {ignore=true}

# Version History {ignore=true}
@import "table/version.csv"
# 目录 {ignore=true}

<!-- @import "[TOC]" {cmd="toc" depthFrom=1 depthTo=6 orderedList=true} -->

<!-- code_chunk_output -->

1. [Risc-V指令](#risc-v指令)
2. [Tile Layout](#tile-layout)
    1. [Tile Reg使用方式](#tile-reg使用方式)
    2. [Tile Reg排布方式](#tile-reg排布方式)
    3. [Tile Format Memory Layout](#tile-format-memory-layout)
3. [汇编指令命令方式](#汇编指令命令方式)
4. [Memory Operation](#memory-operation)
    1. [Share Memory地址信息](#share-memory地址信息)
        1. [Local Chip Local PEC Local PE Share Memory地址信息](#local-chip-local-pec-local-pe-share-memory地址信息)
        2. [Local Chip Local PEC Remote PE Share Memory地址信息](#local-chip-local-pec-remote-pe-share-memory地址信息)
        3. [Local Chip Share Memory地址信息(全局地址)](#local-chip-share-memory地址信息全局地址)
        4. [Remote Chip Share Memory地址信息(全局地址)](#remote-chip-share-memory地址信息全局地址)
    2. [Tile Unit-stride Load/Store](#tile-unit-stride-loadstore)
            1. [伪代码](#伪代码)
        2. [Tile Unit-stride Global Memory Load](#tile-unit-stride-global-memory-load)
        3. [Tile Unit-stride Global Memory Store](#tile-unit-stride-global-memory-store)
        4. [Tile Unit-stride Share Memory Load](#tile-unit-stride-share-memory-load)
        5. [Tile Unit-stride Share Memory Store](#tile-unit-stride-share-memory-store)
    3. [Tile Strided Load/Store](#tile-strided-loadstore)
            1. [伪代码](#伪代码-1)
        2. [Tile Strided Global Memory Load](#tile-strided-global-memory-load)
        3. [Tile Stride Global Memory Store](#tile-stride-global-memory-store)
        4. [Tile Strided Share Memory Load](#tile-strided-share-memory-load)
        5. [Tile Stride Share Memory Store](#tile-stride-share-memory-store)
    4. [Tile Indexed Load/Store](#tile-indexed-loadstore)
            1. [伪代码](#伪代码-2)
        2. [Tile Indexed Global Memory Load](#tile-indexed-global-memory-load)
        3. [Tile Indexed Global Memory Store](#tile-indexed-global-memory-store)
        4. [Tile Indexed Share Memory Load](#tile-indexed-share-memory-load)
        5. [Tile Indexed Share Memory Store](#tile-indexed-share-memory-store)
    5. [Tile Block Load/Store (No MX)](#tile-block-loadstore-no-mx)
            1. [伪代码](#伪代码-3)
        2. [Tile Block Global Memory Load](#tile-block-global-memory-load)
        3. [Tile Block Global Memory Store](#tile-block-global-memory-store)
        4. [Tile Block Share Memory Load](#tile-block-share-memory-load)
        5. [Tile Block Share Memory Store](#tile-block-share-memory-store)
    6. [Tile Block MX4/8 Load/Store](#tile-block-mx48-loadstore)
            1. [伪代码](#伪代码-4)
        2. [Tile Block MX4/8 Global Memory Load](#tile-block-mx48-global-memory-load)
        3. [Tile Block MX4/8 Global Memory Store](#tile-block-mx48-global-memory-store)
        4. [Tile Block MX4/8 Share Memory Load](#tile-block-mx48-share-memory-load)
        5. [Tile Block MX4/8 Share Memory Store](#tile-block-mx48-share-memory-store)
    7. [Tile Block MX6 Load/Store](#tile-block-mx6-loadstore)
            1. [伪代码](#伪代码-5)
        2. [Tile Block MX6 Global Memory Load](#tile-block-mx6-global-memory-load)
        3. [Tile Block MX6 Global Memory Store](#tile-block-mx6-global-memory-store)
        4. [Tile Block MX6 Share Memory Load](#tile-block-mx6-share-memory-load)
        5. [Tile Block MX6 Share Memory Store](#tile-block-mx6-share-memory-store)
    8. [Tile Atomic Scalar Load](#tile-atomic-scalar-load)
        1. [Tile Atomic Global Memory Scalar Load](#tile-atomic-global-memory-scalar-load)
        2. [Tile Atomic Share Memory Scalar Load](#tile-atomic-share-memory-scalar-load)
    9. [Tile Atomic Scalar Store](#tile-atomic-scalar-store)
        1. [Tile Atomic Global Memory Scalar Store](#tile-atomic-global-memory-scalar-store)
        2. [Tile Atomic Share Memory Scalar Store](#tile-atomic-share-memory-scalar-store)
    10. [Tile Atomic Scalar Broadcast Store](#tile-atomic-scalar-broadcast-store)
        1. [Tile Atomic Global Memory Scalar Broadcast Store](#tile-atomic-global-memory-scalar-broadcast-store)
        2. [Tile Atomic Share Memory Scalar Broadcast Store](#tile-atomic-share-memory-scalar-broadcast-store)
    11. [Tile Atomic Tile Store](#tile-atomic-tile-store)
        1. [Tile Atomic Global Memory Tile Store](#tile-atomic-global-memory-tile-store)
        2. [Tile Atomic Share Memory Tile Store](#tile-atomic-share-memory-tile-store)
    12. [Tile Tensor Async Copy](#tile-tensor-async-copy)
    13. [Tile Tensor Async Copy Commit Group](#tile-tensor-async-copy-commit-group)
    14. [Memory Barrier](#memory-barrier)
        1. [tmbar.rr.init](#tmbarrrinit)
            1. [伪代码](#伪代码-6)
        2. [tmbar.rr.inc_tx_cnt](#tmbarrrinc_tx_cnt)
            1. [伪代码](#伪代码-7)
        3. [tmbar.rr.dec_tx_cnt](#tmbarrrdec_tx_cnt)
            1. [伪代码](#伪代码-8)
        4. [tmbar.r.arrive](#tmbarrarrive)
            1. [伪代码](#伪代码-9)
        5. [tmbar.rr.arrive](#tmbarrrarrive)
            1. [伪代码](#伪代码-10)
        6. [tmbar.rrr.test_wait](#tmbarrrrtest_wait)
            1. [伪代码](#伪代码-11)
5. [MMA Operation](#mma-operation)
    1. [MMA运算公式为D = A x B^T^或D = A x B^T^ + D](#mma运算公式为d--a-x-bt或d--a-x-bt--d)
            1. [指令运算过程](#指令运算过程)
    2. [运算模式](#运算模式)
        1. [mmauop encoding](#mmauop-encoding)
    3. [Tile Matrix Multiply Add](#tile-matrix-multiply-add)
        1. [GEMM指令（A、B矩阵从tileReg输入）](#gemm指令a-b矩阵从tilereg输入)
            1. [32 Rows Layout](#32-rows-layout)
                1. [M32N32 Normal运算模式](#m32n32-normal运算模式)
                2. [M32N32 A8W4运算模式](#m32n32-a8w4运算模式)
                3. [M32N32 MXFP6运算模式](#m32n32-mxfp6运算模式)
            2. [16 Rows Layout](#16-rows-layout)
                1. [M16N32 Normal运算模式](#m16n32-normal运算模式)
                2. [M16N32 A8W4运算模式](#m16n32-a8w4运算模式)
                3. [M16N32 MXFP6运算模式](#m16n32-mxfp6运算模式)
            3. [8 Rows Layout](#8-rows-layout)
                1. [M8N32 Normal运算模式](#m8n32-normal运算模式)
                2. [M8N32 A8W4运算模式](#m8n32-a8w4运算模式)
                3. [M8N32 MXFP6运算模式](#m8n32-mxfp6运算模式)
        2. [GEMV指令（A向量、B矩阵从tileReg输入）](#gemv指令a向量-b矩阵从tilereg输入)
            1. [GEMV运算模式](#gemv运算模式)
                1. [M1N32 Normal运算模式](#m1n32-normal运算模式)
                2. [M1N32 A8W4运算模式](#m1n32-a8w4运算模式)
                3. [M1N32 MXFP6运算模式](#m1n32-mxfp6运算模式)
    4. [Tile Matrix Multiply Add from Memory](#tile-matrix-multiply-add-from-memory)
        1. [GEMM.mem指令](#gemmmem指令)
            1. [GEMM.mem指令（A矩阵从shareMemory输入，B矩阵从tileReg输入）](#gemmmem指令a矩阵从sharememory输入b矩阵从tilereg输入)
            2. [GEMM.mem指令（A矩阵从tileReg输入，B矩阵从shareMemory输入）](#gemmmem指令a矩阵从tilereg输入b矩阵从sharememory输入)
            3. [GEMM.mem指令（A矩阵、B矩阵从shareMemory输入）](#gemmmem指令a矩阵-b矩阵从sharememory输入)
            4. [32 Rows Layout](#32-rows-layout-1)
            5. [16 Rows Layout](#16-rows-layout-1)
            6. [8 Rows Layout](#8-rows-layout-1)
        2. [GEMV.mem指令](#gemvmem指令)
            1. [GEMV.mem指令（A矩阵从tileReg输入，B矩阵从shareMemory输入）](#gemvmem指令a矩阵从tilereg输入b矩阵从sharememory输入)
6. [ALU Operation](#alu-operation)
    1. [Rounding Support](#rounding-support)
    2. [Reuse Support](#reuse-support)
    3. [Tile Conversion Operations](#tile-conversion-operations)
        1. [conversion uop](#conversion-uop)
            1. [matrix conversion](#matrix-conversion)
            2. [vector conversion](#vector-conversion)
    4. [Tile Transpose Function Operations](#tile-transpose-function-operations)
    5. [Tile 2 Operand Vector Operations](#tile-2-operand-vector-operations)
        1. [src1 src2 都是tile reg](#src1-src2-都是tile-reg)
        2. [src1为tile reg，src2为scalar reg](#src1为tile-regsrc2为scalar-reg)
        3. [src1为tile reg，src2为immediate number](#src1为tile-regsrc2为immediate-number)
    6. [Tile 3 Oprand Vector Operations](#tile-3-oprand-vector-operations)
        1. [src1为tile reg，src2为tile reg，src3为tile reg](#src1为tile-regsrc2为tile-regsrc3为tile-reg)
        2. [src1为tile reg，src2为tile reg，src3为scalar reg](#src1为tile-regsrc2为tile-regsrc3为scalar-reg)
        3. [src1为tile reg，src2为tile reg，src3为immediate number](#src1为tile-regsrc2为tile-regsrc3为immediate-number)
        4. [src1为tile reg，src2为scalar reg，src3为tile reg](#src1为tile-regsrc2为scalar-regsrc3为tile-reg)
        5. [src1为tile reg，src2为scalar reg，src3为scalar reg](#src1为tile-regsrc2为scalar-regsrc3为scalar-reg)
        6. [src1为tile reg，src2为scalar reg，src3为immediate number](#src1为tile-regsrc2为scalar-regsrc3为immediate-number)
        7. [src1为tile reg，src2为immediate number，src3为tile reg](#src1为tile-regsrc2为immediate-numbersrc3为tile-reg)
        8. [src1为tile reg，src2为immediate number，src3为scalar reg](#src1为tile-regsrc2为immediate-numbersrc3为scalar-reg)
        9. [src1为tile reg，src2为immediate number，src3为immediate number](#src1为tile-regsrc2为immediate-numbersrc3为immediate-number)
7. [Tile Special Function Operations](#tile-special-function-operations)
    1. [sfu uop](#sfu-uop)
8. [Move and ASP_store Operation](#move-and-asp_store-operation)
    1. [Move Instructions](#move-instructions)
        1. [Tile Tile to Vector](#tile-tile-to-vector)
        2. [Tile Vector to Tile](#tile-vector-to-tile)
        3. [Tile Tile to Scalar](#tile-tile-to-scalar)
        4. [Tile Scalar to Tile](#tile-scalar-to-tile)
        5. [Tile To Tile](#tile-to-tile)
        6. [Tile Immediate to Tile](#tile-immediate-to-tile)
9. [Synchronization Operation](#synchronization-operation)
    1. [Tile Synchronization](#tile-synchronization)
    2. [Tile Wait](#tile-wait)
        1. [twait.ls](#twaitls)
        2. [twait.tacp_cg](#twaittacp_cg)
        3. [twait.rmtfence](#twaitrmtfence)
        4. [twait](#twait)
        5. [tkill](#tkill)
        6. [ace_bsync](#ace_bsync)
        7. [ace_nbsync](#ace_nbsync)
    3. [Tile Remote Fence](#tile-remote-fence)
10. [Control Register Operation](#control-register-operation)
    1. [CSR指令](#csr指令)
        1. [TCSRR](#tcsrr)
        2. [TCSRW](#tcsrw)
        3. [TCSRWI](#tcsrwi)
    2. [CSR列表](#csr列表)
        1. [tmask](#tmask)
        2. [tctrl](#tctrl)
        3. [tcmdvalid](#tcmdvalid)
        4. [tend](#tend)
        5. [thwid](#thwid)
        6. [tsynccounter](#tsynccounter)
        7. [tsmembase](#tsmembase)
        8. [tsmemsize](#tsmemsize)
        9. [tkerneladdr](#tkerneladdr)
        10. [tparamaddr](#tparamaddr)
        11. [tparamsize](#tparamsize)
        12. [tkernelidx](#tkernelidx)
        13. [tkerneldims](#tkerneldims)
        14. [tregbase](#tregbase)
        15. [tregsize](#tregsize)
        16. [tprivatebase](#tprivatebase)
        17. [tprivatesize](#tprivatesize)
        18. [ttbmap](#ttbmap)

<!-- /code_chunk_output -->


# Risc-V指令
本文档主要描述在AX45MPV外使用Andes Custom Extension(ACE)扩展的Tile Core Extension指令。Risc-V指令和本文档的tile extension可从https://prid.siorigin.com/查询。
具体指令意义可从[The RISC-V Instruction SetManual Volume I](https://siorigin.feishu.cn/file/SYbqbNt90oOvZKxN3ascwlcwnFI)和[The RISC-V Instruction SetManual: Volume II](https://siorigin.feishu.cn/file/BnbHbdUwNompFSxEcmxchHhonql)查询。
## 注：Risc-V Atomic Extension在SIPU1.0不能使用，其内容和[Tile Atomic Scalar Load](#tile-atomic-scalar-load)、[Tile Atomic Scalar Store](#tile-atomic-scalar-store)冲突。 {ignore=true}


# Tile Layout
## Tile Reg使用方式
1. 一条指令中多个Tile Reg的操作数不允许重叠。举例：MMA操作中的src和dst OP都不能操作同一个Tile Reg。
2. 指令无法操作编号为[tregsize](#tregsize)以及更大的Tile Reg。多Tile Reg也遵守该规则。多Tile Reg操作的指令也无法操作编号为tregsize及数字更大的Tile Reg。
3. 指令无法操作编号为256和数字更大的Tile Reg。多Tile Reg操作的指令也无法操作编号为256及数字更大的Tile Reg。
## Tile Reg排布方式
@import "graph/tile_reg.svg"
如图所示，每个Tile Reg都有64B的Header存储。
32x32B的数据排布模式是行(M)方向32B排布，再按列(K)方向排布32个32B。
16x64B的数据排布模式是行(M)方向32B排布，再按列(K)方向排布16个32B，再按行方向(M)方向排布2组16个32B。
8x128B的数据排布模式是行(M)方向32B排布，再按列(K)方向排布8个32B，再按行方向(M)方向排布4组8个32B。
Vector Tile的数据排布模式是按列(K)方向排布1x1024B。
## Tile Format Memory Layout
@import "graph/memory_layout.png"
如图所示，memory layout有Non MX Dense和MX8/4以及MX6的memory layout方式。
详情见[Tile Format文档](https://siorigin.feishu.cn/docx/BhzVd25X8ox5nWx8C5Nc7mJCnWb#share-CzcLdTGfloE5f4xQFKNc1U2Wn0c)。

# 汇编指令命令方式
以`tld.trr.blk.global.[m2/m4]. Td, (rs1), rs3`为例。
| tld. | trr. | blk. | global. | [m2/m4]. | Td, | （rs1）, | rs3 |
| - | - | - | - | - | - | - | - |
| 指令名 | 操作数类型 | 运行模式为block模式 | 访问global memory | 访问的tile数(m1不需填写) |目的tile寄存器 | 源scalar寄存器1 | 源scalar寄存器3 |

@import "mem_inst.md"
@import "mma_inst.md"
@import "talu_inst.md"
@import "move_inst.md"
@import "sync_inst.md"
@import "csr_inst.md"