// Simple test demonstrating how to use tile decoder functions
// in initial blocks and other contexts

`timescale 1ns/1ps

// Import the package to access all functions
import tile_decoder_pkg::*;

module simple_decoder_test;

    // Test data
    logic [31:0] test_instructions[] = '{
        32'h0000407b,  // 32-bit CSR instruction
        32'h0000607b,  // First word of 64-bit memory instruction
        32'h8000007b,  // Second word of 64-bit memory instruction
        32'h0000697b,  // First word of 96-bit instruction
        32'h12345678   // Non-tile instruction
    };

    // Variables for testing
    logic [31:0] current_word;
    instr_collector_t collector;
    instr_length_e expected_length;
    logic is_tile;
    string disasm_result;

    // Main test in initial block
    initial begin
        $display("=== Simple Tile Decoder Test ===");
        $display("Time: %0t", $time);
        $display("");

        // Test 1: Basic function calls
        $display("Test 1: Basic Function Calls");
        $display("------------------------------");
        
        current_word = 32'h0000407b;
        $display("Testing word: 0x%08x", current_word);
        
        // Call package functions directly
        is_tile = is_tile_instruction(current_word);
        expected_length = get_instruction_length(current_word);
        
        $display("  Is tile instruction: %s", is_tile ? "YES" : "NO");
        $display("  Expected length: %s (%0d bits)", 
                expected_length.name(), get_instruction_bits(expected_length));
        $display("");

        // Test 2: Complete 32-bit instruction processing
        $display("Test 2: Complete 32-bit Instruction");
        $display("------------------------------------");
        
        if (is_tile) begin
            collector = init_collector(current_word);
            $display("  Collector initialized:");
            $display("    Collected words: %0d", collector.collected_words);
            $display("    Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                disasm_result = disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("    Disassembly: %s", disasm_result);
            end
        end
        $display("");

        // Test 3: Multi-word instruction processing
        $display("Test 3: Multi-word Instruction (64-bit)");
        $display("----------------------------------------");
        
        // Start with first word
        current_word = 32'h0000607b;
        $display("  Word 1: 0x%08x", current_word);
        
        collector = init_collector(current_word);
        $display("    Expected length: %s", collector.expected_length.name());
        $display("    Complete after word 1: %s", collector.is_complete ? "YES" : "NO");
        
        if (!collector.is_complete) begin
            // Add second word
            current_word = 32'h8000007b;
            $display("  Word 2: 0x%08x", current_word);
            
            collector = add_word_to_collector(collector, current_word);
            $display("    Complete after word 2: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                disasm_result = disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("    Full instruction: 0x%016x", collector.instruction_data[63:0]);
                $display("    Disassembly: %s", disasm_result);
            end
        end
        $display("");

        // Test 4: Non-tile instruction
        $display("Test 4: Non-tile Instruction");
        $display("-----------------------------");
        
        current_word = 32'h12345678;
        $display("  Testing word: 0x%08x", current_word);
        
        is_tile = is_tile_instruction(current_word);
        $display("    Is tile instruction: %s", is_tile ? "YES" : "NO");
        
        if (!is_tile) begin
            $display("    This is a regular RISC-V instruction, not a tile instruction");
        end
        $display("");

        // Test 5: Batch processing simulation
        $display("Test 5: Batch Processing Simulation");
        $display("-----------------------------------");
        
        process_instruction_batch();
        
        $display("=== All Tests Complete ===");
        $finish;
    end

    // Task to simulate processing a batch of instructions
    task automatic process_instruction_batch();
        logic [31:0] instruction_stream[] = '{
            32'h0000407b,  // 32-bit CSR
            32'h0000607b, 32'h8000007b,  // 64-bit memory load
            32'h0000697b, 32'h8000007b, 32'hf260707b,  // 96-bit indexed load
            32'h12345678   // Non-tile instruction
        };
        
        instr_collector_t active_collector;
        logic collector_active = 1'b0;
        int word_index = 0;
        int instruction_count = 0;
        
        $display("  Processing instruction stream...");
        
        while (word_index < instruction_stream.size()) begin
            current_word = instruction_stream[word_index];
            $display("    [%0d] Processing word: 0x%08x", word_index, current_word);
            
            if (!collector_active) begin
                // Start new instruction
                if (is_tile_instruction(current_word)) begin
                    active_collector = init_collector(current_word);
                    collector_active = 1'b1;
                    $display("      -> Started new tile instruction");
                    
                    if (active_collector.is_complete) begin
                        // 32-bit instruction complete immediately
                        disasm_result = disassemble_instruction(
                            active_collector.instruction_data, 
                            active_collector.expected_length);
                        $display("      -> Complete: %s", disasm_result);
                        collector_active = 1'b0;
                        instruction_count++;
                    end
                end else begin
                    $display("      -> Non-tile instruction, skipping");
                end
            end else begin
                // Add word to existing instruction
                active_collector = add_word_to_collector(active_collector, current_word);
                $display("      -> Added to existing instruction");
                
                if (active_collector.is_complete) begin
                    disasm_result = disassemble_instruction(
                        active_collector.instruction_data, 
                        active_collector.expected_length);
                    $display("      -> Complete: %s", disasm_result);
                    collector_active = 1'b0;
                    instruction_count++;
                end
            end
            
            word_index++;
        end
        
        $display("  Processed %0d tile instructions from %0d words", 
                instruction_count, instruction_stream.size());
    endtask

    // Demonstrate function calls in always blocks
    logic clk = 0;
    logic [31:0] input_word = 32'h0000607b;
    logic output_is_tile;
    instr_length_e output_length;

    // Clock generation
    always #5 clk = ~clk;

    // Combinational logic using package functions
    always_comb begin
        output_is_tile = is_tile_instruction(input_word);
        output_length = get_instruction_length(input_word);
    end

    // Sequential logic using package functions
    always_ff @(posedge clk) begin
        if ($time > 100ns && $time < 200ns) begin
            // Change input to test different instructions
            case (($time - 100) / 20)
                0: input_word <= 32'h0000407b;  // 32-bit
                1: input_word <= 32'h0000607b;  // 64-bit
                2: input_word <= 32'h0000697b;  // 96-bit
                3: input_word <= 32'h12345678;  // Non-tile
                default: input_word <= 32'h0;
            endcase
        end
    end

    // Monitor changes
    always @(input_word) begin
        if ($time > 0) begin
            $display("[%0t] Input changed to 0x%08x -> Tile: %s, Length: %s", 
                    $time, input_word, 
                    output_is_tile ? "YES" : "NO", 
                    output_length.name());
        end
    end

endmodule
