#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_0000022a0cb6ad00 .scope module, "simple_csr_test" "simple_csr_test" 2 2;
 .timescale 0 0;
v0000022a0cc1a5c0_0 .var "ace_op", 6 0;
v0000022a0cc175e0_0 .var "is_sync_wait", 0 0;
v0000022a0cc1b1c0_0 .var "is_tile", 0 0;
v0000022a0cb6ae90_0 .var "rw", 1 0;
v0000022a0cb6af30_0 .var "test_instruction", 31 0;
v0000022a0cc1bb10_0 .var "tuop", 2 0;
    .scope S_0000022a0cb6ad00;
T_0 ;
    %vpi_call 2 11 "$display", "=== Testing CSR Instructions as Sync/Wait ===" {0 0 0};
    %pushi/vec4 1073758331, 0, 32;
    %store/vec4 v0000022a0cb6af30_0, 0, 32;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000022a0cc1a5c0_0, 0, 7;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000022a0cc1bb10_0, 0, 3;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0000022a0cb6ae90_0, 0, 2;
    %load/vec4 v0000022a0cc1a5c0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000022a0cc1b1c0_0, 0, 1;
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.0, 8;
    %load/vec4 v0000022a0cc1bb10_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.0;
    %store/vec4 v0000022a0cc175e0_0, 0, 1;
    %vpi_call 2 22 "$display", "Testing tcsrr.r instruction: 0x%08x", v0000022a0cb6af30_0 {0 0 0};
    %vpi_call 2 23 "$display", "  ACE_OP: %b, TUOP: %b, RW: %b", v0000022a0cc1a5c0_0, v0000022a0cc1bb10_0, v0000022a0cb6ae90_0 {0 0 0};
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.1, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.2, 8;
T_0.1 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.2, 8;
 ; End of false expr.
    %blend;
T_0.2;
    %vpi_call 2 24 "$display", "  Is tile instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.3, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.4, 8;
T_0.3 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.4, 8;
 ; End of false expr.
    %blend;
T_0.4;
    %vpi_call 2 25 "$display", "  Is sync/wait instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.5, 8;
    %vpi_call 2 27 "$display", "  Instruction type: tcsrr.r" {0 0 0};
T_0.5 ;
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0000022a0cb6af30_0, 0, 32;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000022a0cc1a5c0_0, 0, 7;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000022a0cc1bb10_0, 0, 3;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0000022a0cb6ae90_0, 0, 2;
    %load/vec4 v0000022a0cc1a5c0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000022a0cc1b1c0_0, 0, 1;
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.7, 8;
    %load/vec4 v0000022a0cc1bb10_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.7;
    %store/vec4 v0000022a0cc175e0_0, 0, 1;
    %vpi_call 2 38 "$display", "\012Testing tcsrw.i instruction: 0x%08x", v0000022a0cb6af30_0 {0 0 0};
    %vpi_call 2 39 "$display", "  ACE_OP: %b, TUOP: %b, RW: %b", v0000022a0cc1a5c0_0, v0000022a0cc1bb10_0, v0000022a0cb6ae90_0 {0 0 0};
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.8, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.9, 8;
T_0.8 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.9, 8;
 ; End of false expr.
    %blend;
T_0.9;
    %vpi_call 2 40 "$display", "  Is tile instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.11, 8;
T_0.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.11, 8;
 ; End of false expr.
    %blend;
T_0.11;
    %vpi_call 2 41 "$display", "  Is sync/wait instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.12, 8;
    %vpi_call 2 43 "$display", "  Instruction type: tcsrw.i" {0 0 0};
T_0.12 ;
    %pushi/vec4 2147500155, 0, 32;
    %store/vec4 v0000022a0cb6af30_0, 0, 32;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000022a0cc1a5c0_0, 0, 7;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000022a0cc1bb10_0, 0, 3;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0000022a0cb6ae90_0, 0, 2;
    %load/vec4 v0000022a0cc1a5c0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000022a0cc1b1c0_0, 0, 1;
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.14, 8;
    %load/vec4 v0000022a0cc1bb10_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.14;
    %store/vec4 v0000022a0cc175e0_0, 0, 1;
    %vpi_call 2 54 "$display", "\012Testing tcsrw.r instruction: 0x%08x", v0000022a0cb6af30_0 {0 0 0};
    %vpi_call 2 55 "$display", "  ACE_OP: %b, TUOP: %b, RW: %b", v0000022a0cc1a5c0_0, v0000022a0cc1bb10_0, v0000022a0cb6ae90_0 {0 0 0};
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.15, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.16, 8;
T_0.15 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.16, 8;
 ; End of false expr.
    %blend;
T_0.16;
    %vpi_call 2 56 "$display", "  Is tile instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.17, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.18, 8;
T_0.17 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.18, 8;
 ; End of false expr.
    %blend;
T_0.18;
    %vpi_call 2 57 "$display", "  Is sync/wait instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.19, 8;
    %vpi_call 2 59 "$display", "  Instruction type: tcsrw.r" {0 0 0};
T_0.19 ;
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v0000022a0cb6af30_0, 0, 32;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000022a0cc1a5c0_0, 0, 7;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000022a0cc1bb10_0, 0, 3;
    %load/vec4 v0000022a0cc1a5c0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000022a0cc1b1c0_0, 0, 1;
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.21, 8;
    %load/vec4 v0000022a0cc1bb10_0;
    %pushi/vec4 5, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.21;
    %store/vec4 v0000022a0cc175e0_0, 0, 1;
    %vpi_call 2 69 "$display", "\012Testing twait instruction (for comparison): 0x%08x", v0000022a0cb6af30_0 {0 0 0};
    %vpi_call 2 70 "$display", "  ACE_OP: %b, TUOP: %b", v0000022a0cc1a5c0_0, v0000022a0cc1bb10_0 {0 0 0};
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.22, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.23, 8;
T_0.22 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.23, 8;
 ; End of false expr.
    %blend;
T_0.23;
    %vpi_call 2 71 "$display", "  Is tile instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.24, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.25, 8;
T_0.24 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.25, 8;
 ; End of false expr.
    %blend;
T_0.25;
    %vpi_call 2 72 "$display", "  Is sync/wait instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.26, 8;
    %vpi_call 2 74 "$display", "  Instruction type: twait" {0 0 0};
T_0.26 ;
    %pushi/vec4 635, 0, 32;
    %store/vec4 v0000022a0cb6af30_0, 0, 32;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000022a0cc1a5c0_0, 0, 7;
    %load/vec4 v0000022a0cb6af30_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000022a0cc1bb10_0, 0, 3;
    %load/vec4 v0000022a0cc1a5c0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000022a0cc1b1c0_0, 0, 1;
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.28, 8;
    %load/vec4 v0000022a0cc1bb10_0;
    %cmpi/e 4, 0, 3;
    %jmp/1 T_0.30, 4;
    %flag_mov 8, 4;
    %load/vec4 v0000022a0cc1bb10_0;
    %cmpi/e 5, 0, 3;
    %flag_or 4, 8;
T_0.30;
    %flag_get/vec4 4;
    %jmp/1 T_0.29, 4;
    %load/vec4 v0000022a0cc1bb10_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %or;
T_0.29;
    %and;
T_0.28;
    %store/vec4 v0000022a0cc175e0_0, 0, 1;
    %vpi_call 2 84 "$display", "\012Testing regular tile instruction (should NOT be sync/wait): 0x%08x", v0000022a0cb6af30_0 {0 0 0};
    %vpi_call 2 85 "$display", "  ACE_OP: %b, TUOP: %b", v0000022a0cc1a5c0_0, v0000022a0cc1bb10_0 {0 0 0};
    %load/vec4 v0000022a0cc1b1c0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.31, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.32, 8;
T_0.31 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.32, 8;
 ; End of false expr.
    %blend;
T_0.32;
    %vpi_call 2 86 "$display", "  Is tile instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000022a0cc175e0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.33, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.34, 8;
T_0.33 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.34, 8;
 ; End of false expr.
    %blend;
T_0.34;
    %vpi_call 2 87 "$display", "  Is sync/wait instruction: %s", S<0,vec4,u24> {1 0 0};
    %vpi_call 2 88 "$display", "  Expected: Should be tile but NOT sync/wait" {0 0 0};
    %vpi_call 2 90 "$display", "\012=== Test Complete ===" {0 0 0};
    %vpi_call 2 91 "$display", "\012Summary:" {0 0 0};
    %vpi_call 2 92 "$display", "- CSR instructions (tuop=100) should be recognized as sync/wait" {0 0 0};
    %vpi_call 2 93 "$display", "- Sync instructions (tuop=101) should be recognized as sync/wait" {0 0 0};
    %vpi_call 2 94 "$display", "- ACE instructions (tuop=111) should be recognized as sync/wait" {0 0 0};
    %vpi_call 2 95 "$display", "- Other tile instructions should NOT be recognized as sync/wait" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "simple_csr_test.sv";
