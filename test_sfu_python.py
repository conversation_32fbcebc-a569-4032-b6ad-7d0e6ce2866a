#!/usr/bin/env python3
"""
Test script for Python SFU instruction disassembler
This script tests the enhanced SFU instruction decoding functionality.
"""

from tile_disassembler import TileDisassembler

def test_sfu_instructions():
    """Test SFU instruction decoding with various examples"""
    
    disasm = TileDisassembler()
    
    print("=== Testing Python SFU Instruction Disassembler ===")
    print()
    
    # Test cases: instruction hex -> expected result
    # Generated by programmatically modifying only the vecuop2[5:2] field
    # Original: 0x0004237b0000627b with vecuop2[5:2] = 3 (texp2)
    
    test_cases = [
        # Original test case: texp2.tt.f32 (vecuop2[5:2] = 3)
        ("0x0004237b0000627b", "texp2.tt.f32 T0, T8"),
        
        # Test other SFU operations (different vecuop2[5:2] values)
        ("0x0004207b0000627b", "tsgmd.tt.f32 T0, T8"),   # vecuop2[5:2] = 0
        ("0x0004217b0000627b", "tsin.tt.f32 T0, T8"),    # vecuop2[5:2] = 1
        ("0x0004227b0000627b", "tcos.tt.f32 T0, T8"),    # vecuop2[5:2] = 2
        ("0x0004247b0000627b", "tlog2.tt.f32 T0, T8"),   # vecuop2[5:2] = 4
        ("0x0004267b0000627b", "trcp.tt.f32 T0, T8"),    # vecuop2[5:2] = 6
        ("0x0004277b0000627b", "tsqrt.tt.f32 T0, T8"),   # vecuop2[5:2] = 7
        ("0x0004287b0000627b", "trsqrt.tt.f32 T0, T8"),  # vecuop2[5:2] = 8
        ("0x0004297b0000627b", "ttanh.tt.f32 T0, T8"),   # vecuop2[5:2] = 9
    ]
    
    passed = 0
    failed = 0
    
    for hex_instr, expected in test_cases:
        result = disasm.disassemble_hex_string(hex_instr)
        
        if result == expected:
            print(f"✓ PASS: {hex_instr} -> {result}")
            passed += 1
        else:
            print(f"✗ FAIL: {hex_instr}")
            print(f"  Expected: {expected}")
            print(f"  Got:      {result}")
            failed += 1
    
    print()
    print(f"=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    if failed == 0:
        print("All tests passed! 🎉")
        return True
    else:
        print(f"{failed} test(s) failed. ❌")
        return False

if __name__ == "__main__":
    success = test_sfu_instructions()
    exit(0 if success else 1)
