// Test the problematic instruction 0x82216b000564fb
// to understand why it's decoded as tcos.tt.f32 instead of tmul.ttr.f32

module test_problematic_instruction;
    import tile_decoder_pkg::*;
    
    // Test instruction
    logic [63:0] test_instruction = 64'h82216b000564fb;
    
    // Results
    instr_length_e length;
    string instruction_name;
    string operands;
    
    initial begin
        $display("Testing instruction: 0x%016x", test_instruction);
        $display("Expected: tmul.ttr.f32 T1,T4,a0");
        $display("");
        
        // Test instruction length detection
        length = get_instruction_length(test_instruction[31:0]);
        $display("Detected length: %s", length.name());
        
        // Test instruction name extraction
        instruction_name = extract_instruction_name(test_instruction, length);
        $display("Detected instruction: %s", instruction_name);
        
        // Test operand formatting
        operands = format_operands(test_instruction, length, instruction_name);
        $display("Operands: %s", operands);
        
        $display("");
        $display("Complete result: %s %s", instruction_name, operands);
        
        // Analyze bit fields
        $display("");
        $display("=== BIT FIELD ANALYSIS ===");
        
        logic [31:0] word1 = test_instruction[31:0];
        logic [31:0] word2 = test_instruction[63:32];
        
        $display("Word 1: 0x%08x = %032b", word1, word1);
        $display("Word 2: 0x%08x = %032b", word2, word2);
        
        // Extract key fields
        logic [6:0] ace_op1 = word1[6:0];
        logic rsen = word1[8];
        logic immen = word1[9];
        logic [1:0] vecuop1 = word1[11:10];
        logic [2:0] tuop1 = word1[14:12];
        
        logic [6:0] ace_op2 = word2[6:0];
        logic [2:0] tuop2 = word2[14:12];
        logic [3:0] vecuop2_52 = word2[19:16];
        
        $display("");
        $display("Field extraction:");
        $display("  ace_op1: %07b = 0x%02x", ace_op1, ace_op1);
        $display("  rsen: %b", rsen);
        $display("  immen: %b", immen);
        $display("  vecuop1: %02b = %0d", vecuop1, vecuop1);
        $display("  tuop1: %03b = %0d", tuop1, tuop1);
        $display("  ace_op2: %07b = 0x%02x", ace_op2, ace_op2);
        $display("  tuop2: %03b = %0d", tuop2, tuop2);
        $display("  vecuop2[5:2]: %04b = %0d", vecuop2_52, vecuop2_52);
        
        // Analyze decoder path
        $display("");
        $display("=== DECODER PATH ANALYSIS ===");
        
        if (ace_op1 == 7'b1111011) begin
            $display("✓ ace_op1 = 1111011 (tile instruction)");
            
            if (tuop1 == 3'b110) begin
                $display("✓ tuop1 = 110 (tuop_110)");
                
                if (tuop2 == 3'b010) begin
                    $display("✓ tuop2 = 010");
                    
                    if (vecuop1 == 2'b01) begin
                        $display("✓ vecuop1 = 01 (SFU operations)");
                        $display("  vecuop2[5:2] = %0d", vecuop2_52);
                        
                        case (vecuop2_52)
                            4'b0000: $display("  → tsgmd operation");
                            4'b0001: $display("  → tsin operation");
                            4'b0010: $display("  → tcos operation ← THIS IS WHY IT'S DECODED AS TCOS");
                            4'b0011: $display("  → texp2 operation");
                            default: $display("  → unknown SFU operation");
                        endcase
                        
                    end else if (vecuop1 == 2'b10) begin
                        $display("✓ vecuop1 = 10 (ALU operations)");
                        $display("  vecuop2[5:2] = %0d", vecuop2_52);
                        
                        case (vecuop2_52)
                            4'b0000: $display("  → tadd operation");
                            4'b0001: $display("  → tmul operation ← THIS WOULD BE TMUL");
                            4'b1000: $display("  → tmin operation");
                            4'b1001: $display("  → tmax operation");
                            default: $display("  → unknown ALU operation");
                        endcase
                    end else begin
                        $display("✗ vecuop1 = %02b (unexpected)", vecuop1);
                    end
                end else begin
                    $display("✗ tuop2 = %03b (not 010)", tuop2);
                end
            end else begin
                $display("✗ tuop1 = %03b (not 110)", tuop1);
            end
        end else begin
            $display("✗ ace_op1 = %07b (not tile instruction)", ace_op1);
        end
        
        $display("");
        $display("=== CONCLUSION ===");
        $display("The instruction 0x82216b000564fb is correctly decoded as tcos.tt.f32");
        $display("because it has vecuop1=01 (SFU) and vecuop2[5:2]=0010 (tcos).");
        $display("");
        $display("For tmul.ttr.f32, the instruction should have:");
        $display("  - vecuop1 = 10 (ALU operations)");
        $display("  - vecuop2[5:2] = 0001 (tmul operation)");
        $display("  - rsen = 1, immen = 0 (for .ttr variant)");
        
        $finish;
    end
    
endmodule
