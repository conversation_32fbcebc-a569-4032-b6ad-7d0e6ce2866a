#!/usr/bin/env python3
"""
Debug the instruction encoding issue between 0x82216b000564fb and tmul.ttr.f32
"""

def analyze_current_instruction():
    """Analyze the current problematic instruction"""
    instruction = 0x82216b000564fb
    
    print("CURRENT INSTRUCTION ANALYSIS:")
    print(f"Hex: 0x{instruction:016x}")
    print(f"Binary: {instruction:064b}")
    print()
    
    # Split into words
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print()
    
    # Extract fields according to the decoder logic
    # Word 1 fields
    ace_op1 = word1 & 0x7F
    rsen = (word1 >> 8) & 0x1
    immen = (word1 >> 9) & 0x1
    vecuop1 = (word1 >> 10) & 0x3
    tuop1 = (word1 >> 12) & 0x7
    
    # Word 2 fields  
    ace_op2 = word2 & 0x7F
    vecuop2_52 = (word2 >> 16) & 0xF  # bits 19:16 = vecuop2[5:2]
    vecuop2_10 = (word2 >> 16) & 0x3  # bits 17:16 = vecuop2[1:0]
    tuop2 = (word2 >> 12) & 0x7
    
    print("EXTRACTED FIELDS:")
    print(f"Word 1:")
    print(f"  ace_op1: {ace_op1:07b} = 0x{ace_op1:02x}")
    print(f"  rsen:    {rsen}")
    print(f"  immen:   {immen}")
    print(f"  vecuop1: {vecuop1:02b} = {vecuop1}")
    print(f"  tuop1:   {tuop1:03b} = {tuop1}")
    print(f"Word 2:")
    print(f"  ace_op2:     {ace_op2:07b} = 0x{ace_op2:02x}")
    print(f"  tuop2:       {tuop2:03b} = {tuop2}")
    print(f"  vecuop2[5:2]: {vecuop2_52:04b} = {vecuop2_52}")
    print(f"  vecuop2[1:0]: {vecuop2_10:02b} = {vecuop2_10}")
    print()
    
    # Decode according to current logic
    print("DECODER LOGIC PATH:")
    if ace_op1 == 0x7B:
        print("✓ ace_op1 = 1111011 (tile instruction)")
        if tuop1 == 0b110:
            print("✓ tuop1 = 110 (tuop_110)")
            if tuop2 == 0b010:
                print("✓ tuop2 = 010")
                if vecuop1 == 0b01:
                    print("✓ vecuop1 = 01 (SFU operations)")
                    print(f"✓ vecuop2[5:2] = {vecuop2_52} → ", end="")
                    
                    sfu_ops = {0: 'tsgmd', 1: 'tsin', 2: 'tcos', 3: 'texp2', 4: 'tlog2', 
                              6: 'trcp', 7: 'tsqrt', 8: 'trsqrt', 9: 'ttanh'}
                    
                    if vecuop2_52 in sfu_ops:
                        print(f"{sfu_ops[vecuop2_52]}")
                        data_type = "f32" if vecuop2_10 == 0 else ("bf16" if vecuop2_10 == 1 else "f16")
                        print(f"→ RESULT: {sfu_ops[vecuop2_52]}.tt.{data_type}")
                    else:
                        print("Unknown SFU operation")
                elif vecuop1 == 0b10:
                    print("✓ vecuop1 = 10 (ALU operations)")
                    alu_ops = {0: 'tadd', 1: 'tmul', 8: 'tmin', 9: 'tmax'}
                    if vecuop2_52 in alu_ops:
                        print(f"✓ vecuop2[5:2] = {vecuop2_52} → {alu_ops[vecuop2_52]}")
                        if rsen and not immen:
                            print(f"→ RESULT: {alu_ops[vecuop2_52]}.ttr.f32")
                        elif not rsen and not immen:
                            print(f"→ RESULT: {alu_ops[vecuop2_52]}.ttt.f32")
                        elif not rsen and immen:
                            print(f"→ RESULT: {alu_ops[vecuop2_52]}.tti.f32")
    
    return word1, word2

def generate_correct_tmul_ttr():
    """Generate the correct encoding for tmul.ttr.f32 T1,T4,a0"""
    print("\n" + "="*60)
    print("GENERATING CORRECT tmul.ttr.f32 T1,T4,a0 ENCODING:")
    print()
    
    # Based on encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0001/tmul.ttr.md
    
    # First word
    word1 = 0
    word1 |= 0b1111011        # ace_op[6:0] = 1111011
    word1 |= (0b01 << 7)      # bits 8:7 = '01' 
    word1 |= (0b1 << 8)       # rsen = 1 (for .ttr)
    word1 |= (0b0 << 9)       # immen = 0 (for .ttr)
    word1 |= (0b10 << 10)     # vecuop1[11:10] = '10' (ALU operations)
    word1 |= (0b110 << 12)    # tuop[14:12] = '110'
    word1 |= (10 << 15)       # rs2[19:15] = a0 = x10 = 10
    # bits 24:20 = '00000' (already 0)
    # reuse1[25] = 0 (already 0)
    # bit 26 = '0' (already 0)
    # neg1[27] = 0, neg2[28] = 0 (already 0)
    # vecuop2[1:0][30:29] = '00' for f32 (already 0)
    # bit 31 = '0' (already 0)
    
    # Second word
    word2 = 0
    word2 |= 0b1111011        # ace_op[6:0] = 1111011
    # bit 7 = '0' (already 0)
    word2 |= (0b0001 << 8)    # vecuop2[5:2][11:8] = '0001' (tmul)
    word2 |= (0b010 << 12)    # tuop[14:12] = '010'
    word2 |= (4 << 15)        # Ts1[22:15] = T4 = 4
    word2 |= (1 << 23)        # Td[30:23] = T1 = 1
    # bit 31 = '0' (already 0)
    
    instruction = (word2 << 32) | word1
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print(f"Complete: 0x{instruction:016x}")
    
    return instruction

def compare_instructions():
    """Compare current vs expected instruction"""
    print("\n" + "="*60)
    print("COMPARISON:")
    
    current = 0x82216b000564fb
    expected = generate_correct_tmul_ttr()
    
    print(f"\nCurrent:  0x{current:016x}")
    print(f"Expected: 0x{expected:016x}")
    
    if current == expected:
        print("✓ Instructions match!")
    else:
        print("✗ Instructions differ!")
        print("\nThe issue is that the hex value 0x82216b000564fb")
        print("is NOT the correct encoding for tmul.ttr.f32 T1,T4,a0")
        print("It's actually the correct encoding for tcos.tt.f32")

if __name__ == "__main__":
    analyze_current_instruction()
    generate_correct_tmul_ttr()
    compare_instructions()
