// Simple test for tacp.rrr.linear instruction using Verilog syntax
module simple_tacp_test;

    reg [63:0] test_instruction;
    reg [31:0] first_word, second_word;
    reg [6:0] ace_op;
    reg [2:0] tuop;
    reg [5:0] memuop;
    reg [1:0] lsuop;
    reg [1:0] detected_length; // 00=32bit, 01=64bit, 10=96bit, 11=128bit
    reg is_tile, length_correct, is_tacp_64bit;
    
    initial begin
        test_instruction = 64'h8e05017b380ee0fb;
        first_word = test_instruction[31:0];    // 0x380ee0fb
        second_word = test_instruction[63:32];  // 0x8e05017b
        
        $display("=== Testing tacp.rrr.linear Instruction Fix ===");
        $display("Instruction: 0x%016x", test_instruction);
        $display("First word:  0x%08x", first_word);
        $display("Second word: 0x%08x", second_word);
        $display("");
        
        // Extract fields
        ace_op = first_word[6:0];
        tuop = first_word[14:12];
        lsuop = first_word[11:10];
        memuop = first_word[30:25];
        
        $display("Field Analysis:");
        $display("  ace_op [6:0]   = %07b = %d", ace_op, ace_op);
        $display("  tuop [14:12]   = %03b = %d", tuop, tuop);
        $display("  lsuop [11:10]  = %02b = %d", lsuop, lsuop);
        $display("  memuop [30:25] = %06b = %d", memuop, memuop);
        $display("");
        
        // Check tile instruction
        is_tile = (ace_op == 7'b1111011);
        $display("Is tile instruction: %s", is_tile ? "YES" : "NO");
        
        if (is_tile) begin
            $display("TUOP Analysis:");
            $display("  tuop = %d", tuop);
            
            if (tuop == 3'b110) begin // tuop_110
                $display("  This is tuop_110 (multi-lane memory instructions)");
                $display("  memuop = %d (0x%02x)", memuop, memuop);
                $display("  lsuop = %d", lsuop);
                
                if (memuop == 6'b011100) begin // 0x1C = 28 decimal
                    $display("  ✓ This is a tacp operation (memuop = 0x1C)");
                    
                    // Apply the FIXED logic for length detection
                    case (lsuop)
                        2'b00: begin
                            detected_length = 2'b01; // 64-bit for linear operations
                            $display("  ✓ lsuop=00: Linear operations -> Should be 64-bit");
                        end
                        2'b01: begin
                            detected_length = 2'b01; // 64-bit for stride operations
                            $display("  ✓ lsuop=01: Stride operations -> Should be 64-bit");
                        end
                        2'b10: begin
                            detected_length = 2'b10; // 96-bit for index operations
                            $display("  ✓ lsuop=10: Index operations -> Should be 96-bit");
                        end
                        2'b11: begin
                            detected_length = 2'b11; // 128-bit for 4-lane operations
                            $display("  ✓ lsuop=11: 4-lane operations -> Should be 128-bit");
                        end
                    endcase
                    
                    is_tacp_64bit = (lsuop == 2'b00) || (lsuop == 2'b01);
                    
                    $display("");
                    $display("=== RESULTS ===");
                    
                    case (detected_length)
                        2'b00: $display("Detected length: 32-bit");
                        2'b01: $display("Detected length: 64-bit");
                        2'b10: $display("Detected length: 96-bit");
                        2'b11: $display("Detected length: 128-bit");
                    endcase
                    
                    // For this specific instruction (lsuop=00), it should be 64-bit
                    length_correct = (detected_length == 2'b01);
                    
                    if (length_correct) begin
                        $display("✓ SUCCESS: Length correctly detected as 64-bit");
                    end else begin
                        $display("✗ FAILED: Length incorrectly detected");
                    end
                    
                    if (lsuop == 2'b00) begin
                        $display("✓ SUCCESS: This should be tacp.rrr.linear instruction");
                    end else begin
                        $display("! NOTE: This is a different tacp variant (not linear)");
                    end
                    
                    $display("");
                    $display("=== CONCLUSION ===");
                    $display("Before fix: This would have been incorrectly detected as 128-bit");
                    $display("After fix:  Now correctly detected as %s", 
                             (detected_length == 2'b01) ? "64-bit tacp.rrr.linear" :
                             (detected_length == 2'b10) ? "96-bit tacp index operation" :
                             (detected_length == 2'b11) ? "128-bit tacp 4-lane operation" :
                             "32-bit (unexpected)");
                    
                end else begin
                    $display("  This is not a tacp operation (memuop != 0x1C)");
                end
                
            end else begin
                $display("  This is not tuop_110");
            end
        end else begin
            $display("✗ Not a tile instruction");
        end
    end

endmodule
