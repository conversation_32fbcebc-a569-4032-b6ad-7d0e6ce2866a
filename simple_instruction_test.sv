// Simple test for specific instruction 0x8003907b8200647b
module simple_instruction_test;

    reg [63:0] test_instruction;
    reg [31:0] word1, word2;
    reg [6:0] ace_op1, ace_op2;
    reg [2:0] tuop1, tuop2;
    reg [5:0] memuop;
    reg [1:0] lsuop;
    reg [7:0] td;
    reg [4:0] rs1, rs3;
    reg offseten, rmten;
    reg is_tile, is_64bit, is_share, is_mx48, is_trr;
    
    initial begin
        test_instruction = 64'h8003907b8200647b;
        word1 = test_instruction[31:0];   // 0x8200647b
        word2 = test_instruction[63:32];  // 0x8003907b
        
        $display("=== Testing Instruction 0x%016x ===", test_instruction);
        $display("Word1: 0x%08x", word1);
        $display("Word2: 0x%08x", word2);
        
        // Analyze bit fields
        ace_op1 = word1[6:0];
        tuop1 = word1[14:12];
        lsuop = word1[11:10];
        memuop = word1[30:25];
        rs3 = word1[24:20];
        
        ace_op2 = word2[6:0];
        tuop2 = word2[14:12];
        td = word2[30:23];
        rs1 = word2[19:15];
        offseten = word2[15];
        rmten = word2[20];
        
        $display("");
        $display("=== Bit Field Analysis ===");
        $display("First Word:");
        $display("  ACE_OP[6:0]   = %b (%h)", ace_op1, ace_op1);
        $display("  TUOP[14:12]   = %b (%d)", tuop1, tuop1);
        $display("  LSUOP[11:10]  = %b (%d)", lsuop, lsuop);
        $display("  MEMUOP[30:25] = %b (%d)", memuop, memuop);
        $display("  RS3[24:20]    = %b (%d)", rs3, rs3);
        
        $display("Second Word:");
        $display("  ACE_OP[6:0]   = %b (%h)", ace_op2, ace_op2);
        $display("  TUOP[14:12]   = %b (%d)", tuop2, tuop2);
        $display("  TD[30:23]     = %b (%d)", td, td);
        $display("  RS1[19:15]    = %b (%d)", rs1, rs1);
        $display("  OFFSETEN[15]  = %b (%d)", offseten, offseten);
        $display("  RMTEN[20]     = %b (%d)", rmten, rmten);
        
        // Instruction analysis
        is_tile = (ace_op1 == 7'b1111011);
        is_64bit = (tuop1 == 3'b110 && memuop == 6'b000001);
        is_share = (tuop2 == 3'b001);
        is_mx48 = (lsuop == 2'b01);
        is_trr = (offseten == 1'b1 && rmten == 1'b0);
        
        $display("");
        $display("=== Instruction Classification ===");
        $display("Is tile instruction:    %s", is_tile ? "YES" : "NO");
        $display("Is 64-bit block:        %s", is_64bit ? "YES" : "NO");
        $display("Is share memory:        %s", is_share ? "YES" : "NO");
        $display("Is mx48 variant:        %s", is_mx48 ? "YES" : "NO");
        $display("Is trr format:          %s", is_trr ? "YES" : "NO");
        
        // Construct instruction name
        $display("");
        $display("=== Instruction Construction ===");
        if (is_tile && is_64bit) begin
            if (is_share && is_mx48 && is_trr) begin
                $display("Instruction: tld.trr.blk.mx48.share");
            end else begin
                $display("Instruction: tld.unknown.variant");
            end
        end else begin
            $display("Instruction: unknown");
        end
        
        // Operand analysis
        $display("");
        $display("=== Operand Analysis ===");
        $display("TD  = %d (tile register T%d)", td, td);
        $display("RS1 = %d (register x%d)", rs1, rs1);
        $display("RS3 = %d (%s)", rs3, rs3 == 0 ? "zero register" : "register");
        
        if (rs3 == 0) begin
            $display("Operands: T%d, (x%d), zero", td, rs1);
        end else begin
            $display("Operands: T%d, (x%d), x%d", td, rs1, rs3);
        end
        
        // Final result
        $display("");
        $display("=== Final Analysis ===");
        $display("Expected: tld.trr.mx58.share T0, (t2), zero");
        if (is_tile && is_64bit && is_share && is_mx48 && is_trr) begin
            $display("Actual:   tld.trr.blk.mx48.share T%d, (x%d), zero", td, rs1);
            $display("");
            $display("Differences found:");
            $display("1. 'mx58' vs 'mx48' - mx58 might be a typo, should be mx48");
            $display("2. Missing 'blk' in expected - should include block specifier");
            $display("3. '(t2)' vs '(x7)' - register encoding difference:");
            $display("   - Expected t2 (tile register 2)");
            $display("   - Got x7 (general register 7)");
            $display("   This suggests the expected result uses incorrect register notation");
        end else begin
            $display("Actual: Could not decode properly");
        end
        
        $display("");
        $display("=== Conclusion ===");
        $display("The instruction is correctly decoded as:");
        $display("  tld.trr.blk.mx48.share T0, (x7), zero");
        $display("");
        $display("The expected result has these issues:");
        $display("1. 'mx58' should be 'mx48' (based on LSUOP=01)");
        $display("2. Missing 'blk' specifier");
        $display("3. '(t2)' should be '(x7)' - RS1=7 means general register x7");
    end

endmodule
