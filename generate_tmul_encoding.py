#!/usr/bin/env python3
"""
Generate the correct encoding for tmul.ttr.f32 T1,T4,a0 based on the documentation
"""

def generate_tmul_ttr_encoding():
    """Generate tmul.ttr.f32 encoding based on the documentation"""
    
    # Based on encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0001/tmul.ttr.md
    
    # First word (32 bits)
    word1 = 0
    
    # ACE_op[6:0] = 1111011
    word1 |= 0b1111011  # bits 6:0
    
    # bits 8:7 = '01' (from documentation)
    word1 |= (0b01 << 7)  # bits 8:7
    
    # vecuop1[11:10] = '10'
    word1 |= (0b10 << 10)  # bits 11:10
    
    # tmask[9] = 0 (assuming no mask)
    # bit 9 is already 0
    
    # tuop[14:12] = '110'
    word1 |= (0b110 << 12)  # bits 14:12
    
    # rs2[19:15] = register a0 = x10 = 10 (5 bits)
    word1 |= (10 << 15)  # bits 19:15
    
    # bits 24:20 = '00000' (from documentation)
    # already 0
    
    # reuse1[25] = 0 (assuming no reuse)
    # bit 25 is already 0
    
    # bit 26 = '0' (from documentation)
    # already 0
    
    # neg1[27] = 0 (assuming no negation)
    # neg2[28] = 0 (assuming no negation)
    # already 0
    
    # vecuop2[1:0][30:29] = '00' (f32 data type)
    # already 0
    
    # bit 31 = '0' (from documentation)
    # already 0
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    
    # Second word (32 bits)
    word2 = 0
    
    # ACE_op[6:0] = 1111011
    word2 |= 0b1111011  # bits 6:0
    
    # bit 7 = '0' (from documentation)
    # already 0
    
    # vecuop2[5:2][11:8] = '0001' (tmul operation)
    word2 |= (0b0001 << 8)  # bits 11:8
    
    # tuop[14:12] = '010'
    word2 |= (0b010 << 12)  # bits 14:12
    
    # Ts1[22:15] = T4 = 4 (8 bits)
    word2 |= (4 << 15)  # bits 22:15
    
    # Td[30:23] = T1 = 1 (8 bits)
    word2 |= (1 << 23)  # bits 30:23
    
    # bit 31 = '0' (from documentation)
    # already 0
    
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    
    # Combine into 64-bit instruction
    instruction = (word2 << 32) | word1
    print(f"Complete instruction: 0x{instruction:016x}")
    
    return instruction

def analyze_current_vs_expected():
    """Compare current problematic instruction with expected encoding"""
    
    current = 0x82216b000564fb
    expected = generate_tmul_ttr_encoding()
    
    print("\n" + "="*60)
    print("COMPARISON:")
    print(f"Current:  0x{current:016x}")
    print(f"Expected: 0x{expected:016x}")
    
    # Extract fields from both
    current_w1 = current & 0xFFFFFFFF
    current_w2 = (current >> 32) & 0xFFFFFFFF
    expected_w1 = expected & 0xFFFFFFFF
    expected_w2 = (expected >> 32) & 0xFFFFFFFF
    
    print(f"\nWord 1 comparison:")
    print(f"Current:  0x{current_w1:08x} = {current_w1:032b}")
    print(f"Expected: 0x{expected_w1:08x} = {expected_w1:032b}")
    
    print(f"\nWord 2 comparison:")
    print(f"Current:  0x{current_w2:08x} = {current_w2:032b}")
    print(f"Expected: 0x{expected_w2:08x} = {expected_w2:032b}")
    
    # Field-by-field comparison
    print(f"\nField comparison:")
    
    # Word 1 fields
    curr_ace_op = current_w1 & 0x7F
    exp_ace_op = expected_w1 & 0x7F
    print(f"ACE_OP:   Current={curr_ace_op:07b}, Expected={exp_ace_op:07b} {'✓' if curr_ace_op == exp_ace_op else '✗'}")
    
    curr_vecuop1 = (current_w1 >> 10) & 0x3
    exp_vecuop1 = (expected_w1 >> 10) & 0x3
    print(f"vecuop1:  Current={curr_vecuop1:02b}, Expected={exp_vecuop1:02b} {'✓' if curr_vecuop1 == exp_vecuop1 else '✗'}")
    
    curr_tuop1 = (current_w1 >> 12) & 0x7
    exp_tuop1 = (expected_w1 >> 12) & 0x7
    print(f"tuop1:    Current={curr_tuop1:03b}, Expected={exp_tuop1:03b} {'✓' if curr_tuop1 == exp_tuop1 else '✗'}")
    
    # Word 2 fields
    curr_tuop2 = (current_w2 >> 12) & 0x7
    exp_tuop2 = (expected_w2 >> 12) & 0x7
    print(f"tuop2:    Current={curr_tuop2:03b}, Expected={exp_tuop2:03b} {'✓' if curr_tuop2 == exp_tuop2 else '✗'}")
    
    curr_vecuop2_52 = (current_w2 >> 8) & 0xF
    exp_vecuop2_52 = (expected_w2 >> 8) & 0xF
    print(f"vecuop2[5:2]: Current={curr_vecuop2_52:04b}, Expected={exp_vecuop2_52:04b} {'✓' if curr_vecuop2_52 == exp_vecuop2_52 else '✗'}")

if __name__ == "__main__":
    print("Generating correct tmul.ttr.f32 T1,T4,a0 encoding:")
    print("="*60)
    analyze_current_vs_expected()
