#!/usr/bin/env python3
"""
Analyze and fix the instruction decoder issue for 0x82216b000564fb
"""

def analyze_problematic_instruction():
    """Analyze the instruction that's being decoded incorrectly"""
    
    instruction = 0x82216b000564fb
    print(f"Analyzing instruction: 0x{instruction:016x}")
    print(f"Expected: tmul.ttr.f32 T1,T4,a0")
    print(f"Current decoder output: tcos.tt.f32 t123, (x2)")
    print()
    
    # Extract as 64-bit instruction
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print()
    
    # Extract fields according to current decoder logic
    ace_op1 = word1 & 0x7F
    rsen = (word1 >> 8) & 0x1
    immen = (word1 >> 9) & 0x1
    vecuop1 = (word1 >> 10) & 0x3
    tuop1 = (word1 >> 12) & 0x7
    
    ace_op2 = word2 & 0x7F
    tuop2 = (word2 >> 12) & 0x7
    vecuop2_52 = (word2 >> 16) & 0xF  # bits 19:16 in word2
    
    print("Current field extraction:")
    print(f"  ace_op1: {ace_op1:07b} = 0x{ace_op1:02x}")
    print(f"  rsen: {rsen}")
    print(f"  immen: {immen}")
    print(f"  vecuop1: {vecuop1:02b} = {vecuop1}")
    print(f"  tuop1: {tuop1:03b} = {tuop1}")
    print(f"  ace_op2: {ace_op2:07b} = 0x{ace_op2:02x}")
    print(f"  tuop2: {tuop2:03b} = {tuop2}")
    print(f"  vecuop2[5:2]: {vecuop2_52:04b} = {vecuop2_52}")
    print()
    
    # Current decoder path
    print("Current decoder logic path:")
    print(f"1. ace_op1 = 0x{ace_op1:02x} → {'Tile instruction' if ace_op1 == 0x7B else 'Not tile'}")
    print(f"2. tuop1 = {tuop1} → {'tuop_110' if tuop1 == 6 else 'Other tuop'}")
    print(f"3. tuop2 = {tuop2} → {'tuop_010' if tuop2 == 2 else 'Other tuop'}")
    print(f"4. vecuop1 = {vecuop1} → {'SFU ops' if vecuop1 == 1 else 'ALU ops' if vecuop1 == 2 else 'Other'}")
    print(f"5. vecuop2[5:2] = {vecuop2_52} → {'tcos' if vecuop2_52 == 2 else 'tmul' if vecuop2_52 == 1 else 'Other'}")
    print()
    
    # The issue: decoder goes to SFU path instead of ALU path
    if tuop1 == 6 and tuop2 == 2 and vecuop1 == 1 and vecuop2_52 == 2:
        print("ISSUE IDENTIFIED:")
        print("The decoder correctly identifies this as a tuop_110 + tuop_010 instruction")
        print("But it goes to the SFU path (vecuop1=01) instead of ALU path (vecuop1=10)")
        print("vecuop2[5:2]=2 in SFU context means 'tcos', but in ALU context would mean something else")
        print()
        
        print("POSSIBLE CAUSES:")
        print("1. The instruction encoding is actually correct for tcos.tt.f32")
        print("2. The expected instruction tmul.ttr.f32 T1,T4,a0 has a different encoding")
        print("3. There's a bug in the field extraction logic")
        print("4. The instruction format documentation is incorrect")

def check_tmul_ttr_encoding():
    """Check what the correct encoding should be for tmul.ttr.f32"""
    
    print("\n" + "="*60)
    print("CHECKING CORRECT tmul.ttr.f32 ENCODING")
    print("="*60)
    
    # Based on encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0001/tmul.ttr.md
    print("According to tmul.ttr.md documentation:")
    print("First word should have:")
    print("  - ACE_op[6:0] = 1111011")
    print("  - bits[8:7] = 01")
    print("  - vecuop1[11:10] = 10  (NOT 01!)")
    print("  - tuop[14:12] = 110")
    print("  - rs2[19:15] = register value")
    print()
    print("Second word should have:")
    print("  - ACE_op[6:0] = 1111011")
    print("  - vecuop2[5:2][11:8] = 0001  (NOT 0010!)")
    print("  - tuop[14:12] = 010")
    print()
    
    # Generate correct encoding
    word1 = 0
    word1 |= 0b1111011        # ace_op[6:0]
    word1 |= (0b01 << 7)      # bits[8:7]
    word1 |= (0b1 << 8)       # rsen = 1 for .ttr
    word1 |= (0b0 << 9)       # immen = 0 for .ttr
    word1 |= (0b10 << 10)     # vecuop1[11:10] = 10 (ALU ops)
    word1 |= (0b110 << 12)    # tuop[14:12] = 110
    word1 |= (10 << 15)       # rs2[19:15] = a0 = x10
    
    word2 = 0
    word2 |= 0b1111011        # ace_op[6:0]
    word2 |= (0b0001 << 8)    # vecuop2[5:2][11:8] = 0001 (tmul)
    word2 |= (0b010 << 12)    # tuop[14:12] = 010
    word2 |= (4 << 15)        # Ts1[22:15] = T4
    word2 |= (1 << 23)        # Td[30:23] = T1
    
    correct_instruction = (word2 << 32) | word1
    
    print(f"Correct encoding should be: 0x{correct_instruction:016x}")
    print(f"Current problematic:       0x{0x82216b000564fb:016x}")
    print()
    
    if correct_instruction == 0x82216b000564fb:
        print("✓ The encodings match! The issue is in the decoder logic.")
    else:
        print("✗ The encodings differ! The hex value is not for tmul.ttr.f32")
        print()
        print("CONCLUSION:")
        print("The hex value 0x82216b000564fb is NOT the correct encoding for tmul.ttr.f32")
        print("It appears to be correctly decoded as tcos.tt.f32 based on its bit pattern")
        print("The issue is that the wrong hex value was provided as an example")

def identify_decoder_fix():
    """Identify what needs to be fixed in the decoder"""
    
    print("\n" + "="*60)
    print("DECODER FIX ANALYSIS")
    print("="*60)
    
    print("Based on the analysis, the issue is:")
    print()
    print("1. The instruction 0x82216b000564fb has:")
    print("   - tuop1 = 110, tuop2 = 010")
    print("   - vecuop1 = 01 (SFU operations)")
    print("   - vecuop2[5:2] = 0010 (tcos in SFU context)")
    print()
    print("2. This correctly decodes to tcos.tt.f32 according to the SFU encoding")
    print()
    print("3. For tmul.ttr.f32, the instruction should have:")
    print("   - tuop1 = 110, tuop2 = 010")
    print("   - vecuop1 = 10 (ALU operations)")
    print("   - vecuop2[5:2] = 0001 (tmul in ALU context)")
    print()
    print("RECOMMENDATION:")
    print("The decoder is working correctly. The issue is that the provided")
    print("hex value 0x82216b000564fb is not the correct encoding for tmul.ttr.f32")
    print()
    print("To fix this, either:")
    print("1. Provide the correct hex value for tmul.ttr.f32, OR")
    print("2. Verify that the instruction should indeed be tcos.tt.f32")

if __name__ == "__main__":
    analyze_problematic_instruction()
    check_tmul_ttr_encoding()
    identify_decoder_fix()
