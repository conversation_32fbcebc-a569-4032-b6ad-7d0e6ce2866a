#!/usr/bin/env python3
"""
Simple test for tacp.commit_group instruction decoding
"""

def test_tacp_commit_group():
    # Test instruction: 0x7b2200607b (64-bit)
    first_word = 0x2200607b
    second_word = 0x0000007b
    
    print("=== Testing tacp.commit_group Instruction ===")
    print(f"Test instruction: 0x{second_word:08x}{first_word:08x}")
    print(f"First word:  0x{first_word:08x}")
    print(f"Second word: 0x{second_word:08x}")
    print()
    
    # Analyze bit fields
    ace_op = first_word & 0x7F
    tuop = (first_word >> 12) & 0x7
    lsuop = (first_word >> 10) & 0x3
    memuop = (first_word >> 25) & 0x3F
    tuop_second = (second_word >> 12) & 0x7
    
    print("=== Field Analysis ===")
    print(f"ACE_OP[6:0]:      {ace_op:07b} (0x{ace_op:02x}) = {ace_op}")
    print(f"TUOP[14:12]:      {tuop:03b} ({tuop})")
    print(f"LSUOP[11:10]:     {lsuop:02b} ({lsuop})")
    print(f"MEMUOP[30:25]:    {memuop:06b} ({memuop})")
    print(f"Second TUOP[14:12]: {tuop_second:03b} ({tuop_second})")
    print()
    
    # Check conditions according to our fixed logic
    print("=== Condition Analysis ===")
    
    # Check if it's a tile instruction
    is_tile = (ace_op == 0x7B)
    print(f"Is tile instruction (ACE_OP == 0x7B): {is_tile}")
    
    # Check if it's tuop_110
    is_tuop_110 = (tuop == 6)
    print(f"Is tuop_110 (TUOP == 6): {is_tuop_110}")
    
    # Check if MEMUOP == 17 (0x11)
    is_memuop_17 = (memuop == 17)
    print(f"Is MEMUOP == 17 (0x11): {is_memuop_17} (MEMUOP = {memuop})")
    
    # Check if second word has tuop_000
    is_second_tuop_000 = (tuop_second == 0)
    print(f"Is second TUOP == 0: {is_second_tuop_000}")
    
    print()
    
    # Expected results
    expected_64bit = is_tile and is_tuop_110 and is_memuop_17
    expected_tacp_commit_group = expected_64bit and is_second_tuop_000
    
    print("=== Expected Results ===")
    print(f"Should be detected as 64-bit: {expected_64bit}")
    print(f"Should be tacp.commit_group: {expected_tacp_commit_group}")
    
    if expected_tacp_commit_group:
        print("✓ All conditions met for tacp.commit_group!")
    else:
        print("✗ Conditions not met")
        
    return expected_tacp_commit_group

if __name__ == "__main__":
    test_tacp_commit_group()
