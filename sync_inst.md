# Synchronization Operation
## Tile Synchronization
多 thread 间进行同步。blk 为 0 代表不进行当前 thread 的阻塞，仅通知其他 thread（arrive）。sync_id 代表同步序号。scope为0代表是TB，为1表示TBC同步。
`tsync.i.tb/tbc.sync/arrive sync_id`
@import "encode/tuop_101/ctrluop_000/rsen_0/tsync.i.md"
## Tile Wait
### twait.ls
cnt 代表 global/share memory 的 store 和 load 指令剩余多少数量后才能让 t_wait 指令提交。isShare用于选择是 global memory 还是 share memory。isStore选择是Load请求还是Store请求。**特殊的，tmma.mem类指令被认为是一个share memory的load指令，可使用twait.ls等待该指令执行完成。**
`twait.i.load/store.global/share cnt`
@import "encode/tuop_101/ctrluop_001/waitop_000/twait.i.ls.md"
`twait.r.load/store.global/share rs1`
@import "encode/tuop_101/ctrluop_001/waitop_001/twait.r.ls.md"
### twait.tacp_cg
tacp_cnt代表tacp指令中commit group的数量。
该指令会在 ACE Pipeline 执行，在tacp_cnt小于等于指令中的tacp_cnt时提交，允许后续指令执行。
`twait.r.tacp_cg rs1`
@import "encode/tuop_101/ctrluop_001/waitop_010/twait.r.tacp_cg.md"
### twait.rmtfence
指令会在 ACE Pipeline 执行，在rmtfence_cnt小于等于指令中的rmtfence_cnt时提交，允许后续指令执行。rs1是指定的rmtfence_cnt。
`twait.r.rmtfence rs1`
@import "encode/tuop_101/ctrluop_001/waitop_011/twait.r.rmtfence.md"
### twait
指令会在 ACE Pipeline 执行。若mem选项为0，tile core所有的指令执行完成后才可以提交；若mem选项为1，所有访存指令（包括所有的TACP指令）全部执行完成后才可以提交。
`twait.[mem]`
@import "encode/tuop_101/ctrluop_001/waitop_111/twait.md"
### tkill
指令会在ACE Pipeline和Tile Core中执行。执行该指令后将Tile Front End中未发射的指令全部清除，将仍未完成64/96bits Tile Core指令拼接的32bits指令数量写回GPR，最后将TFE设置为初始状态。
@import "encode/tuop_101/ctrluop_001/waitop_100/tkill.r.md"
### ace_bsync
[ace_bsync指令简单介绍](https://siorigin.feishu.cn/wiki/JV7Sw46hdirjkdkXVMpcGH2anPe)
该指令需等待ACE pipeline的所有指令都结束后才能提交，会阻塞后续的所有指令。可用于和上述twait/tkill指令配合阻止后续的CPU访存。
`ace_bsync sync_id`
@import "encode/tuop_111/ace_misc_en_1/miscop_000/ace_bsync.md"
### ace_nbsync
[ace_nbsync指令简单介绍](https://siorigin.feishu.cn/wiki/JV7Sw46hdirjkdkXVMpcGH2anPe#ISAod5TbSo9kbExKe61clbqOnRe)
该指令需等待ACE pipeline的所有指令都结束后才能提交，会阻塞后续的所有指令。
`ace_nbsync sync_id`
@import "encode/tuop_111/ace_misc_en_1/miscop_010/ace_nbsync.md"
## Tile Remote Fence
该指令将回收所有之前发出的的remote store访存指令，不阻塞后续指令。该指令commit时，保证当前Thread前序发送到rs1指定的chip_id上的remote store指令都已经完成。
`trmtfence.r rs1`
@import "encode/tuop_101/ctrluop_010/trmtfence.r.md"