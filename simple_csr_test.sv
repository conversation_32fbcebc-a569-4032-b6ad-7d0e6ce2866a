// Simple test for CSR instruction recognition
module simple_csr_test;

    reg [31:0] test_instruction;
    reg [6:0] ace_op;
    reg [2:0] tuop;
    reg [1:0] rw;
    reg is_tile, is_sync_wait;
    
    initial begin
        $display("=== Testing CSR Instructions as Sync/Wait ===");
        
        // Test tcsrr.r instruction (tuop=100, rw=01)
        test_instruction = 32'b01000000_00000000_01000000_01111011; // rw=01, tuop=100
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        rw = test_instruction[31:30];
        
        is_tile = (ace_op == 7'b1111011);
        is_sync_wait = is_tile && (tuop == 3'b100);
        
        $display("Testing tcsrr.r instruction: 0x%08x", test_instruction);
        $display("  ACE_OP: %b, TUOP: %b, RW: %b", ace_op, tuop, rw);
        $display("  Is tile instruction: %s", is_tile ? "YES" : "NO");
        $display("  Is sync/wait instruction: %s", is_sync_wait ? "YES" : "NO");
        if (is_sync_wait)
            $display("  Instruction type: tcsrr.r");
        
        // Test tcsrw.i instruction (tuop=100, rw=00)
        test_instruction = 32'b00000000_00000000_01000000_01111011; // rw=00, tuop=100
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        rw = test_instruction[31:30];
        
        is_tile = (ace_op == 7'b1111011);
        is_sync_wait = is_tile && (tuop == 3'b100);
        
        $display("\nTesting tcsrw.i instruction: 0x%08x", test_instruction);
        $display("  ACE_OP: %b, TUOP: %b, RW: %b", ace_op, tuop, rw);
        $display("  Is tile instruction: %s", is_tile ? "YES" : "NO");
        $display("  Is sync/wait instruction: %s", is_sync_wait ? "YES" : "NO");
        if (is_sync_wait)
            $display("  Instruction type: tcsrw.i");
        
        // Test tcsrw.r instruction (tuop=100, rw=10)
        test_instruction = 32'b10000000_00000000_01000000_01111011; // rw=10, tuop=100
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        rw = test_instruction[31:30];
        
        is_tile = (ace_op == 7'b1111011);
        is_sync_wait = is_tile && (tuop == 3'b100);
        
        $display("\nTesting tcsrw.r instruction: 0x%08x", test_instruction);
        $display("  ACE_OP: %b, TUOP: %b, RW: %b", ace_op, tuop, rw);
        $display("  Is tile instruction: %s", is_tile ? "YES" : "NO");
        $display("  Is sync/wait instruction: %s", is_sync_wait ? "YES" : "NO");
        if (is_sync_wait)
            $display("  Instruction type: tcsrw.r");
        
        // Test twait instruction for comparison
        test_instruction = 32'h7080507B;
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        
        is_tile = (ace_op == 7'b1111011);
        is_sync_wait = is_tile && (tuop == 3'b101); // sync operations
        
        $display("\nTesting twait instruction (for comparison): 0x%08x", test_instruction);
        $display("  ACE_OP: %b, TUOP: %b", ace_op, tuop);
        $display("  Is tile instruction: %s", is_tile ? "YES" : "NO");
        $display("  Is sync/wait instruction: %s", is_sync_wait ? "YES" : "NO");
        if (is_sync_wait)
            $display("  Instruction type: twait");
        
        // Test a regular tile instruction that should NOT be sync/wait
        test_instruction = 32'b00000000_00000000_00000010_01111011; // tuop=010 (vector operations)
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        
        is_tile = (ace_op == 7'b1111011);
        is_sync_wait = is_tile && ((tuop == 3'b100) || (tuop == 3'b101) || (tuop == 3'b111));
        
        $display("\nTesting regular tile instruction (should NOT be sync/wait): 0x%08x", test_instruction);
        $display("  ACE_OP: %b, TUOP: %b", ace_op, tuop);
        $display("  Is tile instruction: %s", is_tile ? "YES" : "NO");
        $display("  Is sync/wait instruction: %s", is_sync_wait ? "YES" : "NO");
        $display("  Expected: Should be tile but NOT sync/wait");
        
        $display("\n=== Test Complete ===");
        $display("\nSummary:");
        $display("- CSR instructions (tuop=100) should be recognized as sync/wait");
        $display("- Sync instructions (tuop=101) should be recognized as sync/wait");
        $display("- ACE instructions (tuop=111) should be recognized as sync/wait");
        $display("- Other tile instructions should NOT be recognized as sync/wait");
    end

endmodule
