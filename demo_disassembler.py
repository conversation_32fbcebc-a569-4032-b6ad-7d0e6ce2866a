#!/usr/bin/env python3
"""
Demo script for the Tile Extension ISA Disassembler
Demonstrates the capabilities of the disassembler with various instruction types
"""

from tile_disassembler import TileDisassembler


def demo_basic_usage():
    """Demonstrate basic disassembler usage"""
    print("=== Basic Disassembler Usage ===")
    
    disasm = TileDisassembler()
    
    # Example instructions with their binary encodings (using correct match values)
    examples = [
        ("0x407b", "32-bit CSR instruction (tcsrw.i)"),
        ("0x8000007b0000607b", "64-bit memory load (tld.trii.linear.u32.global)"),
        ("0xf260707b8000007b0000697b", "96-bit indexed load (tld.trvi.asp.index.u32.global)"),
        ("0xf260707bf260707b8000047b3800617b", "128-bit tile copy (tacp.rvv.asp2.dsttm)"),
        ("0x7b", "Just ACE_op field (auto-detects bit width)"),
    ]
    
    for hex_str, description in examples:
        result = disasm.disassemble_hex_string(hex_str)
        print(f"{hex_str:>18} -> {result if result else 'Unknown'}")
        print(f"{'':>20}   ({description})")
        print()


def demo_instruction_types():
    """Demonstrate different instruction types"""
    print("=== Different Instruction Types ===")
    
    disasm = TileDisassembler()
    
    # Get examples of different instruction types
    examples_by_type = {}
    
    for name, instr_def in disasm.instructions.items():
        category = None
        if name.startswith('tld') or name.startswith('tst'):
            category = 'Memory Operations'
        elif name.startswith('tmma'):
            category = 'Matrix Multiply'
        elif name.startswith('tcsrr') or name.startswith('tcsrw'):
            category = 'Control/Status Register'
        elif name.startswith('twait') or name.startswith('tsync'):
            category = 'Synchronization'
        elif name.startswith('ace_'):
            category = 'ACE Extensions'
        
        if category and category not in examples_by_type:
            examples_by_type[category] = (name, instr_def)
    
    for category, (name, instr_def) in examples_by_type.items():
        print(f"{category}:")
        result = disasm.disassemble_instruction(instr_def.match_value, instr_def.total_bits)
        print(f"  Example: {name}")
        print(f"  Binary:  0x{instr_def.match_value:016x}")
        print(f"  Assembly: {result}")
        print()


def demo_field_extraction():
    """Demonstrate field extraction capabilities"""
    print("=== Field Extraction Demo ===")
    
    disasm = TileDisassembler()
    
    # Use tld.trii.linear.u32.global as example
    instr_name = 'tld.trii.linear.u32.global'
    if instr_name in disasm.instructions:
        instr_def = disasm.instructions[instr_name]
        
        print(f"Instruction: {instr_name}")
        print(f"Total bits: {instr_def.total_bits}")
        print("Field layout:")
        
        for field in instr_def.fields:
            if field.name:
                field_type = "Fixed" if field.attr else "Variable"
                print(f"  {field.name:15} [{field.start_bit:2d}:{field.end_bit:2d}] {field.bits:2d}bits {field_type:8s} {field.attr}")
        
        print("\nField extraction examples:")
        base_value = instr_def.match_value
        
        # Show how different field values affect the instruction
        td_field = next((f for f in instr_def.fields if f.name == 'Td'), None)
        if td_field:
            print(f"  Changing Td field (bits {td_field.start_bit}-{td_field.end_bit}):")
            for td in [0, 5, 15, 31]:
                mask = (1 << td_field.bits) - 1
                modified = (base_value & ~(mask << td_field.start_bit)) | (td << td_field.start_bit)
                result = disasm.disassemble_instruction(modified, instr_def.total_bits)
                print(f"    Td={td:2d}: {result}")
        
        rs1_field = next((f for f in instr_def.fields if f.name == 'rs1'), None)
        if rs1_field:
            print(f"  Changing rs1 field (bits {rs1_field.start_bit}-{rs1_field.end_bit}):")
            for rs1 in [0, 1, 15, 31]:
                mask = (1 << rs1_field.bits) - 1
                modified = (base_value & ~(mask << rs1_field.start_bit)) | (rs1 << rs1_field.start_bit)
                result = disasm.disassemble_instruction(modified, instr_def.total_bits)
                print(f"    rs1={rs1:2d}: {result}")


def demo_statistics():
    """Show statistics about loaded instructions"""
    print("=== Disassembler Statistics ===")
    
    disasm = TileDisassembler()
    
    print(f"Total instructions loaded: {len(disasm.instructions)}")
    
    # Count by bit width
    bit_width_counts = {}
    for instr_def in disasm.instructions.values():
        width = instr_def.total_bits
        bit_width_counts[width] = bit_width_counts.get(width, 0) + 1
    
    print("Instructions by bit width:")
    for width, count in sorted(bit_width_counts.items()):
        print(f"  {width:2d}-bit: {count:3d} instructions")
    
    # Count by type
    type_counts = {}
    for name in disasm.instructions.keys():
        if name.startswith('tld') or name.startswith('tst'):
            category = 'memory'
        elif name.startswith('tmma'):
            category = 'mma'
        elif name.startswith('tcsrr') or name.startswith('tcsrw'):
            category = 'csr'
        elif name.startswith('twait') or name.startswith('tsync'):
            category = 'sync'
        elif name.startswith('ace_'):
            category = 'ace'
        else:
            category = 'other'
        
        type_counts[category] = type_counts.get(category, 0) + 1
    
    print("Instructions by type:")
    for category, count in sorted(type_counts.items()):
        print(f"  {category:8s}: {count:3d} instructions")


def interactive_demo():
    """Interactive demonstration"""
    print("=== Interactive Demo ===")
    print("Enter hex values to disassemble (or 'quit' to exit):")
    
    disasm = TileDisassembler()
    
    while True:
        try:
            user_input = input("Hex value: ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            result = disasm.disassemble_hex_string(user_input)
            if result:
                print(f"  -> {result}")
            else:
                print(f"  -> No matching instruction found")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"  -> Error: {e}")


def main():
    """Main demo function"""
    print("Tile Extension ISA Disassembler Demo")
    print("=" * 50)
    print()
    
    try:
        demo_basic_usage()
        print()
        
        demo_instruction_types()
        print()
        
        demo_field_extraction()
        print()
        
        demo_statistics()
        print()
        
        # Ask if user wants interactive mode
        response = input("Run interactive demo? (y/n): ").strip().lower()
        if response in ['y', 'yes']:
            interactive_demo()
        
        print("\nDemo completed!")
        
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
