tld.trii.linear.u32.global    , 1----------------0000000011110110000000----------11000-001111011, ./encode/tuop_000/memuop_000000/lsuop_00/offseten_0/rmten_0/tld.trii.linear.u32.global.md
tld.trii.linear.u32.global.remote, 1----------------0001-10011110110000000----------11000-001111011, ./encode/tuop_000/memuop_000000/lsuop_00/offseten_0/rmten_1/tld.trii.linear.u32.global.remote.md
tld.trir.linear.u32.global    , 1----------------0000000011110111000000----------11000-001111011, ./encode/tuop_000/memuop_000000/lsuop_00/offseten_1/rmten_0/tld.trir.linear.u32.global.md
tld.trir.linear.u32.global.remote, 1----------------0001-10011110111000000----------11000-001111011, ./encode/tuop_000/memuop_000000/lsuop_00/offseten_1/rmten_1/tld.trir.linear.u32.global.remote.md
tld.trri.stride.u32.global    , 1--------000-----0000000011110110000000----------11001-011111011, ./encode/tuop_000/memuop_000000/lsuop_01/offseten_0/tld.trri.stride.u32.global.md
tld.trrr.stride.u32.global    , 1--------000-----0000000011110111000000----------11001-011111011, ./encode/tuop_000/memuop_000000/lsuop_01/offseten_1/tld.trrr.stride.u32.global.md
tld.trvi.asp.index.u32.global , 11110010011000000111-----11110111--------000-----0000000011110110000000----------11010-101111011, ./encode/tuop_000/memuop_000000/lsuop_10/offseten_0/tld.trvi.asp.index.u32.global.md
tld.trvr.asp.index.u32.global , 11110010011000000111-----11110111--------000-----0000000011110111000000----------11010-101111011, ./encode/tuop_000/memuop_000000/lsuop_10/offseten_1/tld.trvr.asp.index.u32.global.md
tld.tri.blk.global            , 1----------------0000000011110110000001-----00000110000001111011, ./encode/tuop_000/memuop_000001/lsuop_00/offseten_0/rmten_0/tld.tri.blk.global.md
tld.tri.blk.global.remote     , 1----------------0001-10011110110000001-----00000110000001111011, ./encode/tuop_000/memuop_000001/lsuop_00/offseten_0/rmten_1/tld.tri.blk.global.remote.md
tld.trr.blk.global            , 1----------------0000000011110111000001-----00000110000001111011, ./encode/tuop_000/memuop_000001/lsuop_00/offseten_1/rmten_0/tld.trr.blk.global.md
tld.trr.blk.global.remote     , 1----------------0001-10011110111000001-----00000110000001111011, ./encode/tuop_000/memuop_000001/lsuop_00/offseten_1/rmten_1/tld.trr.blk.global.remote.md
tld.tri.blk.mx48.global       , 1----------------0000000011110110000001-----00000110010001111011, ./encode/tuop_000/memuop_000001/lsuop_01/offseten_0/rmten_0/tld.tri.blk.mx48.global.md
tld.tri.blk.mx48.global.remote, 1----------------0001-10011110110000001-----00000110010001111011, ./encode/tuop_000/memuop_000001/lsuop_01/offseten_0/rmten_1/tld.tri.blk.mx48.global.remote.md
tld.trr.blk.mx48.global       , 1----------------0000000011110111000001-----00000110010001111011, ./encode/tuop_000/memuop_000001/lsuop_01/offseten_1/rmten_0/tld.trr.blk.mx48.global.md
tld.trr.blk.mx48.global.remote, 1----------------0001-10011110111000001-----00000110010001111011, ./encode/tuop_000/memuop_000001/lsuop_01/offseten_1/rmten_1/tld.trr.blk.mx48.global.remote.md
tld.trr.blk.mx6.global        , 1----------------0000000011110110000001-----00000110100001111011, ./encode/tuop_000/memuop_000001/lsuop_10/offseten_0/rmten_0/tld.tri.blk.mx6.global.md
tld.trr.blk.mx6.global.remote , 1----------------0001-10011110110000001-----00000110100001111011, ./encode/tuop_000/memuop_000001/lsuop_10/offseten_0/rmten_1/tld.tri.blk.mx6.global.remote.md
tld.tri.blk.mx6.global        , 1----------------0000000011110111000001-----00000110100001111011, ./encode/tuop_000/memuop_000001/lsuop_10/offseten_1/rmten_0/tld.trr.blk.mx6.global.md
tld.tri.blk.mx6.global.remote , 1----------------0001-10011110111000001-----00000110100001111011, ./encode/tuop_000/memuop_000001/lsuop_10/offseten_1/rmten_1/tld.trr.blk.mx6.global.remote.md
tst.trii.linear.u32.global    , 1----------------0000000011110110001000----------11000-001111011, ./encode/tuop_000/memuop_001000/lsuop_00/offseten_0/rmten_0/tst.trii.linear.u32.global.md
tst.trii.linear.u32.global.remote, 1----------------0001--0011110110001000----------11000-001111011, ./encode/tuop_000/memuop_001000/lsuop_00/offseten_0/rmten_1/tst.trii.linear.u32.global.remote.md
tst.trir.linear.u32.global    , 1----------------0000000011110111001000----------11000-001111011, ./encode/tuop_000/memuop_001000/lsuop_00/offseten_1/rmten_0/tst.trir.linear.u32.global.md
tst.trir.linear.u32.global.remote, 1----------------0001--0011110111001000----------11000-001111011, ./encode/tuop_000/memuop_001000/lsuop_00/offseten_1/rmten_1/tst.trir.linear.u32.global.remote.md
tst.trri.stride.u32.global    , 1--------000-----0000000011110110001000----------11001-011111011, ./encode/tuop_000/memuop_001000/lsuop_01/offseten_0/tst.trri.stride.u32.global.md
tst.trrr.stride.u32.global    , 1--------000-----0000000011110111001000----------11001-011111011, ./encode/tuop_000/memuop_001000/lsuop_01/offseten_1/tst.trrr.stride.u32.global.md
tst.trvi.index.u32.global     , 11110010011000000111-----11110111--------000-----0000000011110110001000----------11010-101111011, ./encode/tuop_000/memuop_001000/lsuop_10/offseten_0/tst.trvi.asp.index.u32.global.md
tst.trvr.index.u32.global     , 11110010011000000111-----11110111--------000-----0000000011110111001000----------11010-101111011, ./encode/tuop_000/memuop_001000/lsuop_10/offseten_1/tst.trvr.asp.index.u32.global.md
tst.tri.blk.global            , 1----------------0000000011110110001001-----00000110000001111011, ./encode/tuop_000/memuop_001001/lsuop_00/offseten_0/rmten_0/tst.tri.blk.global.md
tst.tri.blk.global.remote     , 1----------------0001--0011110110001001-----00000110000001111011, ./encode/tuop_000/memuop_001001/lsuop_00/offseten_0/rmten_1/tst.tri.blk.global.remote.md
tst.trr.blk.global            , 1----------------0000000011110111001001-----00000110000001111011, ./encode/tuop_000/memuop_001001/lsuop_00/offseten_1/rmten_0/tst.trr.blk.global.md
tst.trr.blk.global.remote     , 1----------------0001--0011110111001001-----00000110000001111011, ./encode/tuop_000/memuop_001001/lsuop_00/offseten_1/rmten_1/tst.trr.blk.global.remote.md
tst.tri.blk.mx48.global       , 1----------------0000000011110110001001-----00000110010001111011, ./encode/tuop_000/memuop_001001/lsuop_01/offseten_0/rmten_0/tst.tri.blk.mx48.global.md
tst.tri.blk.mx48.global.remote, 1----------------0001--0011110110001001-----00000110010001111011, ./encode/tuop_000/memuop_001001/lsuop_01/offseten_0/rmten_1/tst.tri.blk.mx48.global.remote.md
tst.trr.blk.mx48.global       , 1----------------0000000011110111001001-----00000110010001111011, ./encode/tuop_000/memuop_001001/lsuop_01/offseten_1/rmten_0/tst.trr.blk.mx48.global.md
tst.trr.blk.mx48.global.remote, 1----------------0001--0011110111001001-----00000110010001111011, ./encode/tuop_000/memuop_001001/lsuop_01/offseten_1/rmten_1/tst.trr.blk.mx48.global.remote.md
tst.trr.blk.mx6.global        , 1----------------0000000011110110001001-----00000110100001111011, ./encode/tuop_000/memuop_001001/lsuop_10/offseten_0/rmten_0/tst.tri.blk.mx6.global.md
tst.trr.blk.mx6.global.remote , 1----------------0001--0011110110001001-----00000110100001111011, ./encode/tuop_000/memuop_001001/lsuop_10/offseten_0/rmten_1/tst.tri.blk.mx6.global.remote.md
tst.tri.blk.mx6.global        , 1----------------0000000011110111001001-----00000110100001111011, ./encode/tuop_000/memuop_001001/lsuop_10/offseten_1/rmten_0/tst.trr.blk.mx6.global.md
tst.tri.blk.mx6.global.remote , 1----------------0001--0011110111001001-----00000110100001111011, ./encode/tuop_000/memuop_001001/lsuop_10/offseten_1/rmten_1/tst.trr.blk.mx6.global.remote.md
tacp.commit_group             , 0000000000000000000000000111101100100010000000000110000001111011, ./encode/tuop_000/memuop_010001/lsuop_00/tacp.commit_group.md
tacp.rrr.linear               , 1000-----0-------000000101111011001110000000-----11000-011111011, ./encode/tuop_000/memuop_011100/tmap_00/tacp.rrr.linear.md
tacp.rrrr.linear.mbar         , 1000-----0-------0000001011110111011100----------11001-011111011, ./encode/tuop_000/memuop_011100/tmap_01/tacp.rrrr.linear.mbar.md
tacp.rvv.asp2.dsttm           , 11110010011000000111-----111101111110010011000000111-----11110111--0-----0-------000010001111011001110000000-----11000-101111011, ./encode/tuop_000/memuop_011100/tmap_10/srctm_0/dsttm_1/tacp.rvv.asp2.dsttm.md
tacp.vvr.asp2.srctm           , 11110010011000000111-----111101111110010011000000111-----11110110--0-----0-------000001101111011001110000000-----11000-101111011, ./encode/tuop_000/memuop_011100/tmap_10/srctm_1/dsttm_0/tacp.vvr.asp2.srctm.md
tacp.rvrv.asp2.mbar.dsttm     , 11110010011000000111-----111101111110010011000000111-----11110111--0-----0-------0000100011110111011100----------11001-101111011, ./encode/tuop_000/memuop_011100/tmap_11/srctm_0/dsttm_1/tacp.rvrv.asp2.mbar.dsttm.md
tacp.vvrr.asp2.mbar.srctm     , 11110010011000000111-----111101111110010011000000111-----11110110--0-----0-------0000011011110111011100----------11001-101111011, ./encode/tuop_000/memuop_011100/tmap_11/srctm_1/dsttm_0/tacp.vvrr.asp2.mbar.srctm.md
tld.rrr.atom.atomop.global    , 1000-----0-------00000001111101101xxxxx00000-----110000011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_0/rmten_0/tld.rrr.atom.atomop.global.md
tld.rrr.atom.atomop.global.remote.unorder, 1000-----0-------00010101111101101xxxxx00000-----110000011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_0/tld.rrr.atom.atomop.global.remote.unorder.md
tld.rrr.atom.atomop.global.remote, 1000-----0-------00011101111101101xxxxx00000-----110000011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_1/tld.rrr.atom.atomop.global.remote.md
tld.rrrr.atom.casb32.global   , 1000-----000-----0000000111110111111111----------110000011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_1/rmten_0/tld.rrrr.atom.casb32.global.md
tld.rrrr.atom.casb32.global.remote.unorder, 1000-----000-----0001010111110111111111----------110000011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_0/tld.rrrr.atom.casb32.global.remote.unorder.md
tld.rrrr.atom.casb32.global.remote, 1000-----000-----0001110111110111111111----------110000011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_1/tld.rrrr.atom.casb32.global.remote.md
tst.rr.atom.atomop.global     , 1000000000-------00000000111101101xxxxx00000-----110010011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_01/rmten_0/tst.rr.atom.atomop.global.md
tst.rr.atom.atomop.global.remote.unorder, 1000000000-------00010-00111101101xxxxx00000-----110010011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_01/rmten_1/order_0/tst.rr.atom.atomop.global.remote.unorder.md
tst.rr.atom.atomop.global.remote, 1000000000-------00011-00111101101xxxxx00000-----110010011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_01/rmten_1/order_1/tst.rr.atom.atomop.global.remote.md
tst.tr.atom.atomop.global     , 1--------000-----00000000111101101xxxxx0000000000110100001111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_10/rmten_0/tst.tr.atom.atomop.global.md
tst.tr.atom.atomop.global.remote.unorder, 1--------000-----00010-00111101101xxxxx0000000000110100001111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_10/rmten_1/order_0/tst.tr.atom.atomop.global.remote.unorder.md
tst.tr.atom.atomop.global.remote, 1--------000-----00011-00111101101xxxxx0000000000110100001111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_10/rmten_1/order_1/tst.tr.atom.atomop.global.remote.md
tst.rr.atom.atomop.global.broadcast, 100000000000-----00000000111101101xxxxx00000-----110110011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_11/rmten_0/tst.rr.atom.atomop.global.broadcast.md
tst.rr.atom.atomop.global.remote.broadcast.unorder, 100000000000-----00010-00111101101xxxxx00000-----110110011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_11/rmten_1/order_0/tst.rr.atom.atomop.global.remote.broadcast.unorder.md
tst.rr.atom.atomop.global.remote.broadcast, 100000000000-----00011-00111101101xxxxx00000-----110110011111011, ./encode/tuop_000/memuop_1xxxxx/lsuop_11/rmten_1/order_1/tst.rr.atom.atomop.global.remote.broadcast.md
tld.trii.linear.u32.share     , 1----------------0010000011110110000000----------11000-001111011, ./encode/tuop_001/memuop_000000/lsuop_00/offseten_0/rmten_0/tld.trii.linear.u32.share.md
tld.trii.linear.u32.share.remote, 1----------------0011-10011110110000000----------11000-001111011, ./encode/tuop_001/memuop_000000/lsuop_00/offseten_0/rmten_1/tld.trii.linear.u32.share.remote.md
tld.trir.linear.u32.share     , 1----------------0010000011110111000000----------11000-001111011, ./encode/tuop_001/memuop_000000/lsuop_00/offseten_1/rmten_0/tld.trir.linear.u32.share.md
tld.trir.linear.u32.share.remote, 1----------------0011-10011110111000000----------11000-001111011, ./encode/tuop_001/memuop_000000/lsuop_00/offseten_1/rmten_1/tld.trir.linear.u32.share.remote.md
tld.trri.stride.u32.share     , 1--------000-----0010000011110110000000----------11001-011111011, ./encode/tuop_001/memuop_000000/lsuop_01/offseten_0/tld.trri.stride.u32.share.md
tld.trrr.stride.u32.share     , 1--------000-----0010000011110111000000----------11001-011111011, ./encode/tuop_001/memuop_000000/lsuop_01/offseten_1/tld.trrr.stride.u32.share.md
tld.trvi.asp.index.u32.share  , 11110010011000000111-----11110111--------000-----0010000011110110000000----------11010-101111011, ./encode/tuop_001/memuop_000000/lsuop_10/offseten_0/tld.trvi.asp.index.u32.share.md
tld.trvr.asp.index.u32.share  , 11110010011000000111-----11110111--------000-----0010000011110111000000----------11010-101111011, ./encode/tuop_001/memuop_000000/lsuop_10/offseten_1/tld.trvr.asp.index.u32.share.md
tld.tri.blk.share             , 1----------------0010000011110110000001-----00000110000001111011, ./encode/tuop_001/memuop_000001/lsuop_00/offseten_0/rmten_0/tld.tri.blk.share.md
tld.tri.blk.share.remote      , 1----------------0011-10011110110000001-----00000110000001111011, ./encode/tuop_001/memuop_000001/lsuop_00/offseten_0/rmten_1/tld.tri.blk.share.remote.md
tld.trr.blk.share             , 1----------------0010000011110111000001-----00000110000001111011, ./encode/tuop_001/memuop_000001/lsuop_00/offseten_1/rmten_0/tld.trr.blk.share.md
tld.trr.blk.share.remote      , 1----------------0011-10011110111000001-----00000110000001111011, ./encode/tuop_001/memuop_000001/lsuop_00/offseten_1/rmten_1/tld.trr.blk.share.remote.md
tld.tri.blk.mx48.share        , 1----------------0010000011110110000001-----00000110010001111011, ./encode/tuop_001/memuop_000001/lsuop_01/offseten_0/rmten_0/tld.tri.blk.mx48.share.md
tld.tri.blk.mx48.share.remote , 1----------------0011-10011110110000001-----00000110010001111011, ./encode/tuop_001/memuop_000001/lsuop_01/offseten_0/rmten_1/tld.tri.blk.mx48.share.remote.md
tld.trr.blk.mx48.share        , 1----------------0010000011110111000001-----00000110010001111011, ./encode/tuop_001/memuop_000001/lsuop_01/offseten_1/rmten_0/tld.trr.blk.mx48.share.md
tld.trr.blk.mx48.share.remote , 1----------------0011-10011110111000001-----00000110010001111011, ./encode/tuop_001/memuop_000001/lsuop_01/offseten_1/rmten_1/tld.trr.blk.mx48.share.remote.md
tld.tri.blk.mx6.share         , 1----------------0010000011110110000001-----00000110100001111011, ./encode/tuop_001/memuop_000001/lsuop_10/offseten_0/rmten_0/tld.tri.blk.mx6.share.md
tld.tri.blk.mx6.share.remote  , 1----------------0011-10011110110000001-----00000110100001111011, ./encode/tuop_001/memuop_000001/lsuop_10/offseten_0/rmten_1/tld.tri.blk.mx6.share.remote.md
tld.trr.blk.mx6.share         , 1----------------0010000011110111000001-----00000110100001111011, ./encode/tuop_001/memuop_000001/lsuop_10/offseten_1/rmten_0/tld.trr.blk.mx6.share.md
tld.trr.blk.mx6.share.remote  , 1----------------0011-10011110111000001-----00000110100001111011, ./encode/tuop_001/memuop_000001/lsuop_10/offseten_1/rmten_1/tld.trr.blk.mx6.share.remote.md
tst.trii.linear.u32.share     , 1----------------0010000011110110001000----------11000-001111011, ./encode/tuop_001/memuop_001000/lsuop_00/offseten_0/rmten_0/tst.trii.linear.u32.share.md
tst.trii.linear.u32.share.remote, 1----------------0011--0011110110001000----------11000-001111011, ./encode/tuop_001/memuop_001000/lsuop_00/offseten_0/rmten_1/tst.trii.linear.u32.share.remote.md
tst.trir.linear.u32.share     , 1----------------0010000011110111001000----------11000-001111011, ./encode/tuop_001/memuop_001000/lsuop_00/offseten_1/rmten_0/tst.trir.linear.u32.share.md
tst.trir.linear.u32.share.remote, 1----------------0011--0011110111001000----------11000-001111011, ./encode/tuop_001/memuop_001000/lsuop_00/offseten_1/rmten_1/tst.trir.linear.u32.share.remote.md
tst.trri.stride.u32.share     , 1--------000-----0010000011110110001000----------11001-011111011, ./encode/tuop_001/memuop_001000/lsuop_01/offseten_0/tst.trri.stride.u32.share.md
tst.trrr.stride.u32.share     , 1--------000-----0010000011110111001000----------11001-011111011, ./encode/tuop_001/memuop_001000/lsuop_01/offseten_1/tst.trrr.stride.u32.share.md
tst.trvi.index.u32.share      , 11110010011000000111-----11110111--------000-----0010000011110110001000----------11010-101111011, ./encode/tuop_001/memuop_001000/lsuop_10/offseten_0/tst.trvi.asp.index.u32.share.md
tst.trvr.index.u32.share      , 11110010011000000111-----11110111--------000-----0010000011110111001000----------11010-101111011, ./encode/tuop_001/memuop_001000/lsuop_10/offseten_1/tst.trvr.asp.index.u32.share.md
tst.tri.blk.share             , 1----------------0010000011110110001001-----00000110000001111011, ./encode/tuop_001/memuop_001001/lsuop_00/offseten_0/rmten_0/tst.tri.blk.share.md
tst.tri.blk.share.remote      , 1----------------0011--0011110110001001-----00000110000001111011, ./encode/tuop_001/memuop_001001/lsuop_00/offseten_0/rmten_1/tst.tri.blk.share.remote.md
tst.trr.blk.share             , 1----------------0010000011110111001001-----00000110000001111011, ./encode/tuop_001/memuop_001001/lsuop_00/offseten_1/rmten_0/tst.trr.blk.share.md
tst.trr.blk.share.remote      , 1----------------0011--0011110111001001-----00000110000001111011, ./encode/tuop_001/memuop_001001/lsuop_00/offseten_1/rmten_1/tst.trr.blk.share.remote.md
tst.tri.blk.mx48.share        , 1----------------0010000011110110001001-----00000110010001111011, ./encode/tuop_001/memuop_001001/lsuop_01/offseten_0/rmten_0/tst.tri.blk.mx48.share.md
tst.tri.blk.mx48.share.remote , 1----------------0011--0011110110001001-----00000110010001111011, ./encode/tuop_001/memuop_001001/lsuop_01/offseten_0/rmten_1/tst.tri.blk.mx48.share.remote.md
tst.trr.blk.mx48.share        , 1----------------0010000011110111001001-----00000110010001111011, ./encode/tuop_001/memuop_001001/lsuop_01/offseten_1/rmten_0/tst.trr.blk.mx48.share.md
tst.trr.blk.mx48.share.remote , 1----------------0011--0011110111001001-----00000110010001111011, ./encode/tuop_001/memuop_001001/lsuop_01/offseten_1/rmten_1/tst.trr.blk.mx48.share.remote.md
tst.trr.blk.mx6.share         , 1----------------0010000011110110001001-----00000110100001111011, ./encode/tuop_001/memuop_001001/lsuop_10/offseten_0/rmten_0/tst.tri.blk.mx6.share.md
tst.trr.blk.mx6.share.remote  , 1----------------0011--0011110110001001-----00000110100001111011, ./encode/tuop_001/memuop_001001/lsuop_10/offseten_0/rmten_1/tst.tri.blk.mx6.share.remote.md
tst.tri.blk.mx6.share         , 1----------------0010000011110111001001-----00000110100001111011, ./encode/tuop_001/memuop_001001/lsuop_10/offseten_1/rmten_0/tst.trr.blk.mx6.share.md
tst.tri.blk.mx6.share.remote  , 1----------------0011--0011110111001001-----00000110100001111011, ./encode/tuop_001/memuop_001001/lsuop_10/offseten_1/rmten_1/tst.trr.blk.mx6.share.remote.md
tmbar.rr.init                 , 100000000000-----001000001111011001000000000-----110010011111011, ./encode/tuop_001/memuop_010000/rmten_0/tmbar.rr.init.md
tmbar.rr.init.remote.unorder  , 100000000000-----00110-001111011001000000000-----110010011111011, ./encode/tuop_001/memuop_010000/rmten_1/order_0/tmbar.rr.init.remote.unorder.md
tmbar.rr.init.remote          , 100000000000-----00111-001111011001000000000-----110010011111011, ./encode/tuop_001/memuop_010000/rmten_1/order_1/tmbar.rr.init.remote.md
tmbar.rr.inc_tx_cnt           , 100000000000-----001000001111011001000100000-----110010011111011, ./encode/tuop_001/memuop_010001/rmten_0/tmbar.rr.inc_tx_cnt.md
tmbar.rr.inc_tx_cnt.remote.unorder, 100000000000-----00110-001111011001000100000-----110010011111011, ./encode/tuop_001/memuop_010001/rmten_1/order_0/tmbar.rr.inc_tx_cnt.remote.unorder.md
tmbar.rr.inc_tx_cnt.remote    , 100000000000-----00111-001111011001000100000-----110010011111011, ./encode/tuop_001/memuop_010001/rmten_1/order_1/tmbar.rr.inc_tx_cnt.remote.md
tmbar.r.arrive                , 100000000000-----00100000111101100100100000000000110010001111011, ./encode/tuop_001/memuop_010010/return_0/rmten_0/tmbar.r.arrive.md
tmbar.r.arrive.remote.unorder , 100000000000-----00110-00111101100100100000000000110010001111011, ./encode/tuop_001/memuop_010010/return_0/rmten_1/order_0/tmbar.r.arrive.remote.unorder.md
tmbar.r.arrive.remote         , 100000000000-----00111-00111101100100100000000000110010001111011, ./encode/tuop_001/memuop_010010/return_0/rmten_1/order_1/tmbar.r.arrive.remote.md
tmbar.rr.arrive               , 1000-----000-----00100001111101100100100000000000110000001111011, ./encode/tuop_001/memuop_010010/return_1/rmten_0/tmbar.rr.arrive.md
tmbar.rr.arrive.remote.unorder, 1000-----000-----00110101111101100100100000000000110000001111011, ./encode/tuop_001/memuop_010010/return_1/rmten_1/order_0/tmbar.rr.arrive.remote.unorder.md
tmbar.rr.arrive.remote        , 1000-----000-----00111101111101100100100000000000110000001111011, ./encode/tuop_001/memuop_010010/return_1/rmten_1/order_1/tmbar.rr.arrive.remote.md
tmbar.rrr.test_wait           , 1000-----000-----001000011111011001001100000-----110000011111011, ./encode/tuop_001/memuop_010011/rmten_0/tmbar.rrr.test_wait.md
tmbar.rrr.test_wait.remote.unorder, 1000-----000-----001101011111011001001100000-----110000011111011, ./encode/tuop_001/memuop_010011/rmten_1/order_0/tmbar.rrr.test_wait.remote.unorder.md
tmbar.rrr.test_wait.remote    , 1000-----000-----001111011111011001001100000-----110000011111011, ./encode/tuop_001/memuop_010011/rmten_1/order_1/tmbar.rrr.test_wait.remote.md
tmbar.rr.dec_tx_cnt           , 100000000000-----001000001111011001010100000-----110010011111011, ./encode/tuop_001/memuop_010101/rmten_0/tmbar.rr.dec_tx_cnt.md
tmbar.rr.dec_tx_cnt.remote.unorder, 100000000000-----00110-001111011001010100000-----110010011111011, ./encode/tuop_001/memuop_010101/rmten_1/order_0/tmbar.rr.dec_tx_cnt.remote.unorder.md
tmbar.rr.dec_tx_cnt.remote    , 100000000000-----00111-001111011001010100000-----110010011111011, ./encode/tuop_001/memuop_010101/rmten_1/order_1/tmbar.rr.dec_tx_cnt.remote.md
tld.rrr.atom.atomop.share     , 1000-----0-------00100001111101101xxxxx00000-----110000011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_0/rmten_0/tld.rrr.atom.atomop.share.md
tld.rrr.atom.atomop.share.remote.unorder, 1000-----0-------00110101111101101xxxxx00000-----110000011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_0/tld.rrr.atom.atomop.share.remote.unorder.md
tld.rrr.atom.atomop.share.remote, 1000-----0-------00111101111101101xxxxx00000-----110000011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_0/rmten_1/order_1/tld.rrr.atom.atomop.share.remote.md
tld.rrrr.atom.casb32.share    , 1000-----000-----0010000111110111111111----------110000011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_0/tld.rrrr.atom.casb32.share.md
tld.rrrr.atom.casb32.share.remote.unorder, 1000-----000-----0011010111110111111111----------110000011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_0/tld.rrrr.atom.casb32.share.remote.unorder.md
tld.rrrr.atom.casb32.share.remote, 1000-----000-----0011110111110111111111----------110000011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_00/casen_1/rmten_1/order_1/tld.rrrr.atom.casb32.share.remote.md
tst.rr.atom.atomop.share      , 1000000000-------00100000111101101xxxxx00000-----110010011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_01/rmten_0/tst.rr.atom.atomop.share.md
tst.rr.atom.atomop.share.remote.unorder, 1000000000-------00110-00111101101xxxxx00000-----110010011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_01/rmten_1/order_0/tst.rr.atom.atomop.share.remote.unorder.md
tst.rr.atom.atomop.share.remote, 1000000000-------00111-00111101101xxxxx00000-----110010011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_01/rmten_1/order_1/tst.rr.atom.atomop.share.remote.md
tst.tr.atom.atomop.share      , 1--------000-----00100000111101101xxxxx----------110100001111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_10/rmten_0/tst.tr.atom.atomop.share.md
tst.tr.atom.atomop.share.remote.unorder, 1--------000-----00110-00111101101xxxxx----------110100001111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_10/rmten_1/order_0/tst.tr.atom.atomop.share.remote.unorder.md
tst.tr.atom.atomop.share.remote, 1--------000-----00111-00111101101xxxxx----------110100001111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_10/rmten_1/order_1/tst.tr.atom.atomop.share.remote.md
tst.rr.atom.atomop.share.broadcast, 100000000000-----00100000111101101xxxxx00000-----110110011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_11/rmten_0/tst.rr.atom.atomop.share.broadcast.md
tst.rr.atom.atomop.share.remote.broadcast.unorder, 100000000000-----00110-00111101101xxxxx00000-----110110011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_11/rmten_1/order_0/tst.rr.atom.atomop.share.remote.broadcast.unorder.md
tst.rr.atom.atomop.share.remote.broadcast, 100000000000-----00111-00111101101xxxxx00000-----110110011111011, ./encode/tuop_001/memuop_1xxxxx/lsuop_11/rmten_1/order_1/tst.rr.atom.atomop.share.remote.broadcast.md
tcvt.tt                       , 0----------------010----011110110--0-0--0-----000110000001111011, ./encode/tuop_010/vecuop1_00/rsen_0/vecen_0/tcvt.tt.md
tcvt.tt.vec                   , 0----------------010----011110110--0-0--0---001--110000001111011, ./encode/tuop_010/vecuop1_00/rsen_0/vecen_1/tcvt.tt.vec.md
tsfuop.tt.fp32                , 0----------------010----01111011000000-0000000000110001001111011, ./encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_00/tsfuop.tt.f32.md
tsfuop.tt.bf16                , 0----------------010----01111011001000-0000000000110001001111011, ./encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_01/tsfuop.tt.bf16.md
tsfuop.tt.f16                 , 0----------------010----01111011010000-0000000000110001001111011, ./encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_10/tsfuop.tt.f16.md
ttrans.tt                     , 0----------------010----01111011011000-0000000000110-01001111011, ./encode/tuop_010/vecuop1_01/rsen_0/aspen_0/vecuop2[1_0]_11/ttrans.tt.md
tadd.ttt                      , 0----------------0100000011110110------00--------110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_0000/tadd.ttt.md
tmul.ttt                      , 0----------------0100001011110110------00--------110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_0001/tmul.ttt.md
tmin.ttt                      , 0----------------0101000011110110--00--00--------110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_1000/tmin.ttt.md
tmax.ttt                      , 0----------------0101001011110110--00--00--------110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_0/vecuop2[5_2]_1001/tmax.ttt.md
tadd.tti                      , 0----------------0100000011110110----0-01000-----110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_0000/tadd.tti.md
tmul.tti                      , 0----------------0100001011110110----0-01000-----110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_0001/tmul.tti.md
tmin.tti                      , 0----------------0101000011110110--000-01000-----110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_1000/tmin.tti.md
tmax.tti                      , 0----------------0101001011110110--000-01000-----110-10001111011, ./encode/tuop_010/vecuop1_10/rsen_0/immen_1/vecuop2[5_2]_1001/tmax.tti.md
tadd.ttr                      , 0----------------0100000011110110----0-00000-----110-10011111011, ./encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0000/tadd.ttr.md
tmul.ttr                      , 0----------------0100001011110110----0-00000-----110-10011111011, ./encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0001/tmul.ttr.md
tmin.ttr                      , 0----------------0101000011110110--000-00000-----110-10011111011, ./encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_1000/tmin.ttr.md
tmax.ttr                      , 0----------------0101001011110110--000-00000-----110-10011111011, ./encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_1001/tmax.ttr.md
tfma.ttii                     , 0000-0001000-----0100000011110110----------------0100000111110110----0-01000-----110-11001111011, ./encode/tuop_010/vecuop1_11/imm2en_1/imm3en_1/tfma.ttii.md
tfma.ttir                     , 1000-0000000-----0100000011110110----------------0100000111110110----0-01000-----110-11001111011, ./encode/tuop_010/vecuop1_11/imm2en_1/rs3en_1/tfma.ttir.md
tfma.ttit                     , 0000-0-00--------0100000011110110----------------0100000111110110----0-01000-----110-11001111011, ./encode/tuop_010/vecuop1_11/imm2en_1/rs3imm3en_0/tfma.ttit.md
tfma.ttri                     , 0000-0001000-----0100000011110110----------------0100000111110110----0-00000-----110-11011111011, ./encode/tuop_010/vecuop1_11/rs2en_1/imm3en_1/tfma.ttri.md
tfma.ttrr                     , 1000-0000000-----0100000011110110----------------0100000111110110----0-00000-----110-11011111011, ./encode/tuop_010/vecuop1_11/rs2en_1/rs3en_1/tfma.ttrr.md
tfma.ttrt                     , 0000-0-00--------0100000011110110----------------0100000111110110----0-00000-----110-11011111011, ./encode/tuop_010/vecuop1_11/rs2en_1/rs3imm3en_0/tfma.ttrt.md
tfma.ttti                     , 0000-0001000-----0100000011110110----------------0100000111110110------00--------110-11001111011, ./encode/tuop_010/vecuop1_11/rs2imm2en_0/imm3en_1/tfma.ttti.md
tfma.tttr                     , 1000-0000000-----0100000011110110----------------0100000111110110------00--------110-11001111011, ./encode/tuop_010/vecuop1_11/rs2imm2en_0/rs3en_1/tfma.tttr.md
tfma.tttt                     , 0000-0-00--------0100000011110110----------------0100000111110110------00--------110-11001111011, ./encode/tuop_010/vecuop1_11/rs2imm2en_0/rs3imm3en_0/tfma.tttt.md
tmma.ttt                      , 0----------------011----011110110-----0----------110---001111011, ./encode/tuop_011/memmata_0/memmatb_0/vecen_0/tmma.ttt.md
tmva.ttt                      , 0----------------01100--011110110-----1----------110---001111011, ./encode/tuop_011/memmata_0/memmatb_0/vecen_1/aspen_0/tmva.ttt.md
tmma.ttr                      , 0----------------011----011110110-----000000-----110---011111011, ./encode/tuop_011/memmata_0/memmatb_1/vecen_0/tmma.ttr.md
tmva.ttr                      , 0----------------01100--011110110-----1--000-----110---011111011, ./encode/tuop_011/memmata_0/memmatb_1/vecen_1/aspen_0/tmva.ttr.md
tmma.trt                      , 1--------000-----011----011110110-----000--------110---001111011, ./encode/tuop_011/memmata_1/memmatb_0/tmma.trt.md
tmma.trr                      , 1--------000-----011----011110110-----000000-----110---011111011, ./encode/tuop_011/memmata_1/memmatb_1/tmma.trr.md
tcsrw.i                       ,                                 00---------------100000001111011, ./encode/tuop_100/rw_00/tcsrw.i.md
tcsrr.r                       ,                                 01----------00000100-----1111011, ./encode/tuop_100/rw_01/tcsrr.r.md
tcsrw.r                       ,                                 10---------------100000001111011, ./encode/tuop_100/rw_10/tcsrw.r.md
tsync.i                       ,                                 0-0-00000000-----101-----1111011, ./encode/tuop_101/ctrluop_000/rsen_0/tsync.i.md
twait.i.ls                    ,                                 0000--001--------101000001111011, ./encode/tuop_101/ctrluop_001/waitop_000/twait.i.ls.md
twait.r.ls                    ,                                 0001--001000-----101000001111011, ./encode/tuop_101/ctrluop_001/waitop_001/twait.r.ls.md
twait.r.tacp_cg               ,                                 001000001^^^-----101000001111011, ./encode/tuop_101/ctrluop_001/waitop_010/twait.r.tacp_cg.md
twait.r.rmtfence              ,                                 001100001^^^-----101000001111011, ./encode/tuop_101/ctrluop_001/waitop_011/twait.r.rmtfence.md
tkill.r                       ,                                 01000000100000000101-----1111011, ./encode/tuop_101/ctrluop_001/waitop_100/tkill.r.md
twait                         ,                                 01110-00100000000101000001111011, ./encode/tuop_101/ctrluop_001/waitop_111/twait.md
trmtfence.r                   ,                                 000000010000-----101-----1111011, ./encode/tuop_101/ctrluop_010/trmtfence.r.md
tmv.rtr                       , 00000010011000000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_000/tmv.rtr.md
tmv.trr                       , 000010100110-0000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_001/tmv.trr.md
tmv.ttrr                      , 000100-----------111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_010/tmv.ttrr.md
tmv.vtr.e8                    , 00100010000000000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_100/func6_100000/tmv.vtr.e8.md
tmv.vtr.e16                   , 00100010010100000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_100/func6_100101/tmv.vtr.e16.md
tmv.vtr.e32                   , 00100010011000000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_100/func6_100110/tmv.vtr.e32.md
tmv.tvr.e8                    , 001010100000-0000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_101/func6_100000/tmv.tvr.e8.md
tmv.tvr.e16                   , 001010100101-0000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_101/func6_100101/tmv.tvr.e16.md
tmv.tvr.e32                   , 001010100110-0000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_101/func6_100110/tmv.tvr.e32.md
tmv.tir                       , 0011100000---0000111-----11110110--------000-----110000011111011, ./encode/tuop_111/ace_misc_en_0/mvop_111/tmv.tir.md
ace_bsync                     ,                                 100000000000-----111000001111011, ./encode/tuop_111/ace_misc_en_1/miscop_000/ace_bsync.md
ace_nbsync                    ,                                 101000000000-----111000001111011, ./encode/tuop_111/ace_misc_en_1/miscop_010/ace_nbsync.md
asp_float_in                  ,                                 11000000000000000111-----1111011, ./encode/tuop_111/ace_misc_en_1/miscop_100/taspin.float.md
taspin.vec                    ,                                 11110010011000000111-----1111011, ./encode/tuop_111/ace_misc_en_1/miscop_111/taspin.vec.md
ace_mem_high_rs1_0_rd_0_rs4_0 ,                                 0----------------00----001111011, ./ace_encode/tuop_00x/rs1en_0/rden_0/rs4en_0/ace_mem_high_rs1_0_rd_0_rs4_0.md
ace_mem_high_rs1_0_rd_0_rs4_1 ,                                 0----------------00----101111011, ./ace_encode/tuop_00x/rs1en_0/rden_0/rs4en_1/ace_mem_high_rs1_0_rd_0_rs4_1.md
ace_mem_high_rs1_0_rd_1       ,                                 0----------------00-----11111011, ./ace_encode/tuop_00x/rs1en_0/rden_1/ace_mem_high_rs1_0_rd_1.md
ace_mem_high_rs1_1_rd_0_rs4_0 ,                                 1----------------00----001111011, ./ace_encode/tuop_00x/rs1en_1/rden_0/rs4en_0/ace_mem_high_rs1_1_rd_0_rs4_0.md
ace_mem_high_rs1_1_rd_0_rs4_1 ,                                 1----------------00----101111011, ./ace_encode/tuop_00x/rs1en_1/rden_0/rs4en_1/ace_mem_high_rs1_1_rd_0_rs4_1.md
ace_mem_high_rs1_1_rd_1       ,                                 1----------------00-----11111011, ./ace_encode/tuop_00x/rs1en_1/rden_1/ace_mem_high_rs1_1_rd_1.md
ace_alu_high_rs3_0_inst3_0    ,                                 0----------------010----01111011, ./ace_encode/tuop_010/rs3en_0/inst3en_0/ace_alu_high_rs3_0_inst3_0.md
ace_alu_high_rs3_0_inst3_1    ,                                 0----------------010----11111011, ./ace_encode/tuop_010/rs3en_0/inst3en_1/ace_alu_high_rs3_0_inst3_1.md
ace_alu_high_rs3_1            ,                                 1--------000-----010----01111011, ./ace_encode/tuop_010/rs3en_1/ace_alu_high_rs3_1.md
ace_mma_high_rs1_0            ,                                 0----------------011----01111011, ./ace_encode/tuop_011/rs1en_0/ace_mma_high_rs1_0.md
ace_mma_high_rs1_1            ,                                 1--------000-----011----01111011, ./ace_encode/tuop_011/rs1en_1/ace_mma_high_rs1_1.md
ace_low_rs2_0_rs3_0           ,                                 0----------------110----01111011, ./ace_encode/tuop_110/rs2en_0/rs3en_0/ace_low_rs2_0_rs3_0.md
ace_low_rs2_0_rs3_1           ,                                 1----------------110----01111011, ./ace_encode/tuop_110/rs2en_0/rs3en_1/ace_low_rs2_0_rs3_1.md
ace_low_rs2_1_rs3_0           ,                                 0----------------110----11111011, ./ace_encode/tuop_110/rs2en_1/rs3en_0/ace_low_rs2_1_rs3_0.md
ace_low_rs2_1_rs3_1           ,                                 1----------------110----11111011, ./ace_encode/tuop_110/rs2en_1/rs3en_1/ace_low_rs2_1_rs3_1.md
tmv_rt_high                   ,                                 000000100110-0000111-----1111011, ./ace_encode/tuop_111/ace_misc_en_0/mvop_000/tmv_rt_high.md
tmv_tr_high                   ,                                 000010100110-0000111-----1111011, ./ace_encode/tuop_111/ace_misc_en_0/mvop_001/tmv_tr_high.md
tmv_tt_high                   ,                                 000100-----------111-----1111011, ./ace_encode/tuop_111/ace_misc_en_0/mvop_010/tmv_tt_high.md
tmv_vt_high                   ,                                 001000100110-0000111-----1111011, ./ace_encode/tuop_111/ace_misc_en_0/mvop_100/tmv_vt_high.md
tmv_tv_high                   ,                                 001010100110-0000111-----1111011, ./ace_encode/tuop_111/ace_misc_en_0/mvop_101/tmv_tv_high.md
tmv_ti_high                   ,                                 0011100000---0000111-----1111011, ./ace_encode/tuop_111/ace_misc_en_0/mvop_111/tmv_ti_high.md
asp_vec_in                    ,                                 11110000000000000111-----1111011, ./ace_encode/tuop_111/ace_misc_en_1/miscop_111/asp_vec_in.md
