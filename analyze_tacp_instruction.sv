// Analyze tacp instruction fields
module analyze_tacp_instruction;

    reg [63:0] instruction;
    reg [31:0] first_word, second_word;
    reg [6:0] ace_op;
    reg [2:0] tuop;
    reg [5:0] memuop;
    reg [1:0] lsuop;
    
    initial begin
        
        instruction = 64'h8e05017b380ee0fb;
        first_word = instruction[31:0];    // 0x380ee0fb
        second_word = instruction[63:32];  // 0x8e05017b
        
        $display("=== Analyzing tacp.rrr.linear instruction ===");
        $display("Full instruction: 0x%016x", instruction);
        $display("First word:       0x%08x", first_word);
        $display("Second word:      0x%08x", second_word);
        $display("");
        
        // Extract fields from first word
        ace_op = first_word[6:0];
        tuop = first_word[14:12];
        lsuop = first_word[11:10];
        memuop = first_word[30:25];
        
        $display("Field Analysis (First Word):");
        $display("  ace_op [6:0]    = %07b = %d (should be 123 for tile)", ace_op, ace_op);
        $display("  tuop [14:12]    = %03b = %d", tuop, tuop);
        $display("  lsuop [11:10]   = %02b = %d", lsuop, lsuop);
        $display("  memuop [30:25]  = %06b = %d", memuop, memuop);
        $display("");
        
        // Analysis
        if (ace_op == 7'b1111011) begin
            $display("✓ Correct ACE_OP for tile instruction");
            
            $display("Analysis:");
            $display("  - tuop = %d", tuop);
            if (tuop == 3'b000) begin
                $display("  - This is tuop_000 (memory operations)");
                $display("  - memuop = %d (0x%02x)", memuop, memuop);
                
                if (memuop == 6'b011100) begin // 28 in decimal, 0x1C in hex
                    $display("  - memuop = 0x1C (28) = tacp operations");
                    $display("  - lsuop = %d", lsuop);
                    
                    case (lsuop)
                        2'b00: $display("  - lsuop = 00: Linear operations (should be 64-bit)");
                        2'b01: $display("  - lsuop = 01: Stride operations (should be 64-bit)");  
                        2'b10: $display("  - lsuop = 10: Index operations (should be 96-bit)");
                        2'b11: $display("  - lsuop = 11: Other operations (need more analysis)");
                    endcase
                    
                    if (lsuop == 2'b00) begin
                        $display("");
                        $display("CONCLUSION:");
                        $display("  This should be a 64-bit tacp.rrr.linear instruction");
                        $display("  Current decoder probably incorrectly identifies it as 128-bit");
                        $display("  because it only looks at memuop=0x1C and assumes all tacp are 128-bit");
                    end
                    
                end else begin
                    $display("  - memuop != 0x1C, not a tacp operation");
                end
            end else begin
                $display("  - tuop != 000, not a memory operation");
            end
        end else begin
            $display("✗ Incorrect ACE_OP, not a tile instruction");
        end
    end

endmodule
