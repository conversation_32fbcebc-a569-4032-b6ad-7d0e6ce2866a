#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_0000023c7bdc94d0 .scope module, "analyze_tacp_instruction" "analyze_tacp_instruction" 2 2;
 .timescale 0 0;
v0000023c7bbeef20_0 .var "ace_op", 6 0;
v0000023c7bdc9660_0 .var "first_word", 31 0;
v0000023c7bbe89b0_0 .var "instruction", 63 0;
v0000023c7bda3380_0 .var "lsuop", 1 0;
v0000023c7bbead40_0 .var "memuop", 5 0;
v0000023c7bdc6340_0 .var "second_word", 31 0;
v0000023c7bdc63e0_0 .var "tuop", 2 0;
    .scope S_0000023c7bdc94d0;
T_0 ;
    %pushi/vec4 2382692731, 0, 32;
    %concati/vec4 940499195, 0, 32;
    %store/vec4 v0000023c7bbe89b0_0, 0, 64;
    %load/vec4 v0000023c7bbe89b0_0;
    %parti/s 32, 0, 2;
    %store/vec4 v0000023c7bdc9660_0, 0, 32;
    %load/vec4 v0000023c7bbe89b0_0;
    %parti/s 32, 32, 7;
    %store/vec4 v0000023c7bdc6340_0, 0, 32;
    %vpi_call 2 17 "$display", "=== Analyzing tacp.rrr.linear instruction ===" {0 0 0};
    %vpi_call 2 18 "$display", "Full instruction: 0x%016x", v0000023c7bbe89b0_0 {0 0 0};
    %vpi_call 2 19 "$display", "First word:       0x%08x", v0000023c7bdc9660_0 {0 0 0};
    %vpi_call 2 20 "$display", "Second word:      0x%08x", v0000023c7bdc6340_0 {0 0 0};
    %vpi_call 2 21 "$display", "\000" {0 0 0};
    %load/vec4 v0000023c7bdc9660_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000023c7bbeef20_0, 0, 7;
    %load/vec4 v0000023c7bdc9660_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000023c7bdc63e0_0, 0, 3;
    %load/vec4 v0000023c7bdc9660_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0000023c7bda3380_0, 0, 2;
    %load/vec4 v0000023c7bdc9660_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0000023c7bbead40_0, 0, 6;
    %vpi_call 2 29 "$display", "Field Analysis (First Word):" {0 0 0};
    %vpi_call 2 30 "$display", "  ace_op [6:0]    = %07b = %d (should be 123 for tile)", v0000023c7bbeef20_0, v0000023c7bbeef20_0 {0 0 0};
    %vpi_call 2 31 "$display", "  tuop [14:12]    = %03b = %d", v0000023c7bdc63e0_0, v0000023c7bdc63e0_0 {0 0 0};
    %vpi_call 2 32 "$display", "  lsuop [11:10]   = %02b = %d", v0000023c7bda3380_0, v0000023c7bda3380_0 {0 0 0};
    %vpi_call 2 33 "$display", "  memuop [30:25]  = %06b = %d", v0000023c7bbead40_0, v0000023c7bbead40_0 {0 0 0};
    %vpi_call 2 34 "$display", "\000" {0 0 0};
    %load/vec4 v0000023c7bbeef20_0;
    %cmpi/e 123, 0, 7;
    %jmp/0xz  T_0.0, 4;
    %vpi_call 2 38 "$display", "\342\234\223 Correct ACE_OP for tile instruction" {0 0 0};
    %vpi_call 2 40 "$display", "Analysis:" {0 0 0};
    %vpi_call 2 41 "$display", "  - tuop = %d", v0000023c7bdc63e0_0 {0 0 0};
    %load/vec4 v0000023c7bdc63e0_0;
    %cmpi/e 0, 0, 3;
    %jmp/0xz  T_0.2, 4;
    %vpi_call 2 43 "$display", "  - This is tuop_000 (memory operations)" {0 0 0};
    %vpi_call 2 44 "$display", "  - memuop = %d (0x%02x)", v0000023c7bbead40_0, v0000023c7bbead40_0 {0 0 0};
    %load/vec4 v0000023c7bbead40_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_0.4, 4;
    %vpi_call 2 47 "$display", "  - memuop = 0x1C (28) = tacp operations" {0 0 0};
    %vpi_call 2 48 "$display", "  - lsuop = %d", v0000023c7bda3380_0 {0 0 0};
    %load/vec4 v0000023c7bda3380_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_0.6, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.7, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.9, 6;
    %jmp T_0.10;
T_0.6 ;
    %vpi_call 2 51 "$display", "  - lsuop = 00: Linear operations (should be 64-bit)" {0 0 0};
    %jmp T_0.10;
T_0.7 ;
    %vpi_call 2 52 "$display", "  - lsuop = 01: Stride operations (should be 64-bit)" {0 0 0};
    %jmp T_0.10;
T_0.8 ;
    %vpi_call 2 53 "$display", "  - lsuop = 10: Index operations (should be 96-bit)" {0 0 0};
    %jmp T_0.10;
T_0.9 ;
    %vpi_call 2 54 "$display", "  - lsuop = 11: Other operations (need more analysis)" {0 0 0};
    %jmp T_0.10;
T_0.10 ;
    %pop/vec4 1;
    %load/vec4 v0000023c7bda3380_0;
    %cmpi/e 0, 0, 2;
    %jmp/0xz  T_0.11, 4;
    %vpi_call 2 58 "$display", "\000" {0 0 0};
    %vpi_call 2 59 "$display", "CONCLUSION:" {0 0 0};
    %vpi_call 2 60 "$display", "  This should be a 64-bit tacp.rrr.linear instruction" {0 0 0};
    %vpi_call 2 61 "$display", "  Current decoder probably incorrectly identifies it as 128-bit" {0 0 0};
    %vpi_call 2 62 "$display", "  because it only looks at memuop=0x1C and assumes all tacp are 128-bit" {0 0 0};
T_0.11 ;
    %jmp T_0.5;
T_0.4 ;
    %vpi_call 2 66 "$display", "  - memuop != 0x1C, not a tacp operation" {0 0 0};
T_0.5 ;
    %jmp T_0.3;
T_0.2 ;
    %vpi_call 2 69 "$display", "  - tuop != 000, not a memory operation" {0 0 0};
T_0.3 ;
    %jmp T_0.1;
T_0.0 ;
    %vpi_call 2 72 "$display", "\342\234\227 Incorrect ACE_OP, not a tile instruction" {0 0 0};
T_0.1 ;
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "analyze_tacp_instruction.sv";
