#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_0000028f244dab20 .scope module, "simple_instruction_test" "simple_instruction_test" 2 2;
 .timescale 0 0;
v0000028f244e0cf0_0 .var "ace_op1", 6 0;
v0000028f245daf30_0 .var "ace_op2", 6 0;
v0000028f244f3c80_0 .var "is_64bit", 0 0;
v0000028f244e3d10_0 .var "is_mx48", 0 0;
v0000028f245d9290_0 .var "is_share", 0 0;
v0000028f244b3380_0 .var "is_tile", 0 0;
v0000028f244f6480_0 .var "is_trr", 0 0;
v0000028f244d67e0_0 .var "lsuop", 1 0;
v0000028f244d6880_0 .var "memuop", 5 0;
v0000028f244e2e60_0 .var "offseten", 0 0;
v0000028f244e2f00_0 .var "rmten", 0 0;
v0000028f244e2fa0_0 .var "rs1", 4 0;
v0000028f244e21c0_0 .var "rs3", 4 0;
v0000028f244e2260_0 .var "td", 7 0;
v0000028f244e2300_0 .var "test_instruction", 63 0;
v0000028f244e23a0_0 .var "tuop1", 2 0;
v0000028f244e2440_0 .var "tuop2", 2 0;
v0000028f24551370_0 .var "word1", 31 0;
v0000028f24551550_0 .var "word2", 31 0;
    .scope S_0000028f244dab20;
T_0 ;
    %pushi/vec4 2147717243, 0, 32;
    %concati/vec4 2181063803, 0, 32;
    %store/vec4 v0000028f244e2300_0, 0, 64;
    %load/vec4 v0000028f244e2300_0;
    %parti/s 32, 0, 2;
    %store/vec4 v0000028f24551370_0, 0, 32;
    %load/vec4 v0000028f244e2300_0;
    %parti/s 32, 32, 7;
    %store/vec4 v0000028f24551550_0, 0, 32;
    %vpi_call 2 20 "$display", "=== Testing Instruction 0x%016x ===", v0000028f244e2300_0 {0 0 0};
    %vpi_call 2 21 "$display", "Word1: 0x%08x", v0000028f24551370_0 {0 0 0};
    %vpi_call 2 22 "$display", "Word2: 0x%08x", v0000028f24551550_0 {0 0 0};
    %load/vec4 v0000028f24551370_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000028f244e0cf0_0, 0, 7;
    %load/vec4 v0000028f24551370_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000028f244e23a0_0, 0, 3;
    %load/vec4 v0000028f24551370_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0000028f244d67e0_0, 0, 2;
    %load/vec4 v0000028f24551370_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0000028f244d6880_0, 0, 6;
    %load/vec4 v0000028f24551370_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0000028f244e21c0_0, 0, 5;
    %load/vec4 v0000028f24551550_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000028f245daf30_0, 0, 7;
    %load/vec4 v0000028f24551550_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000028f244e2440_0, 0, 3;
    %load/vec4 v0000028f24551550_0;
    %parti/s 8, 23, 6;
    %store/vec4 v0000028f244e2260_0, 0, 8;
    %load/vec4 v0000028f24551550_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0000028f244e2fa0_0, 0, 5;
    %load/vec4 v0000028f24551550_0;
    %parti/s 1, 15, 5;
    %store/vec4 v0000028f244e2e60_0, 0, 1;
    %load/vec4 v0000028f24551550_0;
    %parti/s 1, 20, 6;
    %store/vec4 v0000028f244e2f00_0, 0, 1;
    %vpi_call 2 38 "$display", "\000" {0 0 0};
    %vpi_call 2 39 "$display", "=== Bit Field Analysis ===" {0 0 0};
    %vpi_call 2 40 "$display", "First Word:" {0 0 0};
    %vpi_call 2 41 "$display", "  ACE_OP[6:0]   = %b (%h)", v0000028f244e0cf0_0, v0000028f244e0cf0_0 {0 0 0};
    %vpi_call 2 42 "$display", "  TUOP[14:12]   = %b (%d)", v0000028f244e23a0_0, v0000028f244e23a0_0 {0 0 0};
    %vpi_call 2 43 "$display", "  LSUOP[11:10]  = %b (%d)", v0000028f244d67e0_0, v0000028f244d67e0_0 {0 0 0};
    %vpi_call 2 44 "$display", "  MEMUOP[30:25] = %b (%d)", v0000028f244d6880_0, v0000028f244d6880_0 {0 0 0};
    %vpi_call 2 45 "$display", "  RS3[24:20]    = %b (%d)", v0000028f244e21c0_0, v0000028f244e21c0_0 {0 0 0};
    %vpi_call 2 47 "$display", "Second Word:" {0 0 0};
    %vpi_call 2 48 "$display", "  ACE_OP[6:0]   = %b (%h)", v0000028f245daf30_0, v0000028f245daf30_0 {0 0 0};
    %vpi_call 2 49 "$display", "  TUOP[14:12]   = %b (%d)", v0000028f244e2440_0, v0000028f244e2440_0 {0 0 0};
    %vpi_call 2 50 "$display", "  TD[30:23]     = %b (%d)", v0000028f244e2260_0, v0000028f244e2260_0 {0 0 0};
    %vpi_call 2 51 "$display", "  RS1[19:15]    = %b (%d)", v0000028f244e2fa0_0, v0000028f244e2fa0_0 {0 0 0};
    %vpi_call 2 52 "$display", "  OFFSETEN[15]  = %b (%d)", v0000028f244e2e60_0, v0000028f244e2e60_0 {0 0 0};
    %vpi_call 2 53 "$display", "  RMTEN[20]     = %b (%d)", v0000028f244e2f00_0, v0000028f244e2f00_0 {0 0 0};
    %load/vec4 v0000028f244e0cf0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000028f244b3380_0, 0, 1;
    %load/vec4 v0000028f244e23a0_0;
    %cmpi/e 6, 0, 3;
    %flag_get/vec4 4;
    %jmp/0 T_0.0, 4;
    %load/vec4 v0000028f244d6880_0;
    %pushi/vec4 1, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.0;
    %store/vec4 v0000028f244f3c80_0, 0, 1;
    %load/vec4 v0000028f244e2440_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000028f245d9290_0, 0, 1;
    %load/vec4 v0000028f244d67e0_0;
    %pushi/vec4 1, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000028f244e3d10_0, 0, 1;
    %load/vec4 v0000028f244e2e60_0;
    %cmpi/e 1, 0, 1;
    %flag_get/vec4 4;
    %jmp/0 T_0.1, 4;
    %load/vec4 v0000028f244e2f00_0;
    %pushi/vec4 0, 0, 1;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.1;
    %store/vec4 v0000028f244f6480_0, 0, 1;
    %vpi_call 2 62 "$display", "\000" {0 0 0};
    %vpi_call 2 63 "$display", "=== Instruction Classification ===" {0 0 0};
    %load/vec4 v0000028f244b3380_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.3, 8;
T_0.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.3, 8;
 ; End of false expr.
    %blend;
T_0.3;
    %vpi_call 2 64 "$display", "Is tile instruction:    %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000028f244f3c80_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.4, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.5, 8;
T_0.4 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.5, 8;
 ; End of false expr.
    %blend;
T_0.5;
    %vpi_call 2 65 "$display", "Is 64-bit block:        %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000028f245d9290_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.6, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.7, 8;
T_0.6 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.7, 8;
 ; End of false expr.
    %blend;
T_0.7;
    %vpi_call 2 66 "$display", "Is share memory:        %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000028f244e3d10_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.8, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.9, 8;
T_0.8 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.9, 8;
 ; End of false expr.
    %blend;
T_0.9;
    %vpi_call 2 67 "$display", "Is mx48 variant:        %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000028f244f6480_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.11, 8;
T_0.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.11, 8;
 ; End of false expr.
    %blend;
T_0.11;
    %vpi_call 2 68 "$display", "Is trr format:          %s", S<0,vec4,u24> {1 0 0};
    %vpi_call 2 71 "$display", "\000" {0 0 0};
    %vpi_call 2 72 "$display", "=== Instruction Construction ===" {0 0 0};
    %load/vec4 v0000028f244b3380_0;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.14, 9;
    %load/vec4 v0000028f244f3c80_0;
    %and;
T_0.14;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.12, 8;
    %load/vec4 v0000028f245d9290_0;
    %flag_set/vec4 10;
    %flag_get/vec4 10;
    %jmp/0 T_0.18, 10;
    %load/vec4 v0000028f244e3d10_0;
    %and;
T_0.18;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.17, 9;
    %load/vec4 v0000028f244f6480_0;
    %and;
T_0.17;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.15, 8;
    %vpi_call 2 75 "$display", "Instruction: tld.trr.blk.mx48.share" {0 0 0};
    %jmp T_0.16;
T_0.15 ;
    %vpi_call 2 77 "$display", "Instruction: tld.unknown.variant" {0 0 0};
T_0.16 ;
    %jmp T_0.13;
T_0.12 ;
    %vpi_call 2 80 "$display", "Instruction: unknown" {0 0 0};
T_0.13 ;
    %vpi_call 2 84 "$display", "\000" {0 0 0};
    %vpi_call 2 85 "$display", "=== Operand Analysis ===" {0 0 0};
    %vpi_call 2 86 "$display", "TD  = %d (tile register T%d)", v0000028f244e2260_0, v0000028f244e2260_0 {0 0 0};
    %vpi_call 2 87 "$display", "RS1 = %d (register x%d)", v0000028f244e2fa0_0, v0000028f244e2fa0_0 {0 0 0};
    %load/vec4 v0000028f244e21c0_0;
    %pad/u 32;
    %cmpi/e 0, 0, 32;
    %flag_mov 8, 4;
    %jmp/0 T_0.19, 8;
    %pushi/vec4 2053468783, 0, 32; draw_string_vec4
    %pushi/vec4 544367975, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1769174117, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 114, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.20, 8;
T_0.19 ; End of true expr.
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 7497063, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1769174117, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 114, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.20, 8;
 ; End of false expr.
    %blend;
T_0.20;
    %vpi_call 2 88 "$display", "RS3 = %d (%s)", v0000028f244e21c0_0, S<0,vec4,u104> {1 0 0};
    %load/vec4 v0000028f244e21c0_0;
    %pad/u 32;
    %cmpi/e 0, 0, 32;
    %jmp/0xz  T_0.21, 4;
    %vpi_call 2 91 "$display", "Operands: T%d, (x%d), zero", v0000028f244e2260_0, v0000028f244e2fa0_0 {0 0 0};
    %jmp T_0.22;
T_0.21 ;
    %vpi_call 2 93 "$display", "Operands: T%d, (x%d), x%d", v0000028f244e2260_0, v0000028f244e2fa0_0, v0000028f244e21c0_0 {0 0 0};
T_0.22 ;
    %vpi_call 2 97 "$display", "\000" {0 0 0};
    %vpi_call 2 98 "$display", "=== Final Analysis ===" {0 0 0};
    %vpi_call 2 99 "$display", "Expected: tld.trr.mx58.share T0, (t2), zero" {0 0 0};
    %load/vec4 v0000028f244b3380_0;
    %flag_set/vec4 12;
    %flag_get/vec4 12;
    %jmp/0 T_0.28, 12;
    %load/vec4 v0000028f244f3c80_0;
    %and;
T_0.28;
    %flag_set/vec4 11;
    %flag_get/vec4 11;
    %jmp/0 T_0.27, 11;
    %load/vec4 v0000028f245d9290_0;
    %and;
T_0.27;
    %flag_set/vec4 10;
    %flag_get/vec4 10;
    %jmp/0 T_0.26, 10;
    %load/vec4 v0000028f244e3d10_0;
    %and;
T_0.26;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.25, 9;
    %load/vec4 v0000028f244f6480_0;
    %and;
T_0.25;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.23, 8;
    %vpi_call 2 101 "$display", "Actual:   tld.trr.blk.mx48.share T%d, (x%d), zero", v0000028f244e2260_0, v0000028f244e2fa0_0 {0 0 0};
    %vpi_call 2 102 "$display", "\000" {0 0 0};
    %vpi_call 2 103 "$display", "Differences found:" {0 0 0};
    %vpi_call 2 104 "$display", "1. 'mx58' vs 'mx48' - mx58 might be a typo, should be mx48" {0 0 0};
    %vpi_call 2 105 "$display", "2. Missing 'blk' in expected - should include block specifier" {0 0 0};
    %vpi_call 2 106 "$display", "3. '(t2)' vs '(x7)' - register encoding difference:" {0 0 0};
    %vpi_call 2 107 "$display", "   - Expected t2 (tile register 2)" {0 0 0};
    %vpi_call 2 108 "$display", "   - Got x7 (general register 7)" {0 0 0};
    %vpi_call 2 109 "$display", "   This suggests the expected result uses incorrect register notation" {0 0 0};
    %jmp T_0.24;
T_0.23 ;
    %vpi_call 2 111 "$display", "Actual: Could not decode properly" {0 0 0};
T_0.24 ;
    %vpi_call 2 114 "$display", "\000" {0 0 0};
    %vpi_call 2 115 "$display", "=== Conclusion ===" {0 0 0};
    %vpi_call 2 116 "$display", "The instruction is correctly decoded as:" {0 0 0};
    %vpi_call 2 117 "$display", "  tld.trr.blk.mx48.share T0, (x7), zero" {0 0 0};
    %vpi_call 2 118 "$display", "\000" {0 0 0};
    %vpi_call 2 119 "$display", "The expected result has these issues:" {0 0 0};
    %vpi_call 2 120 "$display", "1. 'mx58' should be 'mx48' (based on LSUOP=01)" {0 0 0};
    %vpi_call 2 121 "$display", "2. Missing 'blk' specifier" {0 0 0};
    %vpi_call 2 122 "$display", "3. '(t2)' should be '(x7)' - RS1=7 means general register x7" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "simple_instruction_test.sv";
