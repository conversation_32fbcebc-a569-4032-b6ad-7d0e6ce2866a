// Minimal test for twait instruction
module minimal_test;

    function automatic string test_extract_instruction_name(
        input logic [127:0] instruction_data
    );
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [2:0] ctrluop;
        logic [2:0] waitop;
        logic isMem;

        ace_op = instruction_data[6:0];

        if (ace_op != 7'b1111011) begin
            return "unknown_non_tile";
        end

        tuop = instruction_data[14:12];

        case (tuop)
            3'b101: begin // Sync operations (tuop_101)
                ctrluop = instruction_data[25:23];
                waitop = instruction_data[30:28];
                
                case (ctrluop)
                    3'b001: begin // twait operations
                        case (waitop)
                            3'b111: begin // twait (basic)
                                isMem = instruction_data[26];
                                if (isMem)
                                    return "twait.mem";
                                else
                                    return "twait";
                            end
                            default: return "unknown_waitop";
                        endcase
                    end
                    default: return "unknown_ctrluop";
                endcase
            end
            default: return "unknown_tuop";
        endcase

        return "unknown";
    endfunction

    initial begin
        logic [31:0] test_instruction;
        logic [127:0] instruction_data;
        string result;

        $display("=== Minimal twait test ===");
        
        // Test the specific instruction: 0x7080507B
        test_instruction = 32'h7080507B;
        instruction_data = {96'h0, test_instruction};
        
        $display("Testing instruction: 0x%08x", test_instruction);
        
        result = test_extract_instruction_name(instruction_data);
        $display("Result: %s", result);
        
        $display("=== Test Complete ===");
    end

endmodule
