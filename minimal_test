#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x5574c5a9a790 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x5574c5a72190 .scope module, "minimal_test" "minimal_test" 3 2;
 .timescale 0 0;
S_0x5574c5a72320 .scope begin, "$unm_blk_5" "$unm_blk_5" 3 48, 3 48 0, S_0x5574c5a72190;
 .timescale 0 0;
v0x5574c5a73750_0 .var "instruction_data", 127 0;
v0x5574c5a73bb0_0 .var/str "result";
v0x5574c5a73fa0_0 .var "test_instruction", 31 0;
S_0x5574c5ac3d40 .scope autofunction.str, "test_extract_instruction_name" "test_extract_instruction_name" 3 4, 3 4 0, S_0x5574c5a72190;
 .timescale 0 0;
v0x5574c5ac3f40_0 .var "ace_op", 6 0;
v0x5574c5ac4020_0 .var "ctrluop", 2 0;
v0x5574c5ac4100_0 .var "instruction_data", 127 0;
v0x5574c5ac41f0_0 .var "isMem", 0 0;
; Variable test_extract_instruction_name is string return value of scope S_0x5574c5ac3d40
v0x5574c5ac43c0_0 .var "tuop", 2 0;
v0x5574c5ac44a0_0 .var "waitop", 2 0;
TD_minimal_test.test_extract_instruction_name ;
    %load/vec4 v0x5574c5ac4100_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x5574c5ac3f40_0, 0, 7;
    %load/vec4 v0x5574c5ac3f40_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_0.0, 4;
    %pushi/str "unknown_non_tile";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
T_0.0 ;
    %load/vec4 v0x5574c5ac4100_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x5574c5ac43c0_0, 0, 3;
    %load/vec4 v0x5574c5ac43c0_0;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_0.2, 6;
    %pushi/str "unknown_tuop";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
    %jmp T_0.4;
T_0.2 ;
    %load/vec4 v0x5574c5ac4100_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x5574c5ac4020_0, 0, 3;
    %load/vec4 v0x5574c5ac4100_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x5574c5ac44a0_0, 0, 3;
    %load/vec4 v0x5574c5ac4020_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_0.5, 6;
    %pushi/str "unknown_ctrluop";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
    %jmp T_0.7;
T_0.5 ;
    %load/vec4 v0x5574c5ac44a0_0;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %pushi/str "unknown_waitop";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
    %jmp T_0.10;
T_0.8 ;
    %load/vec4 v0x5574c5ac4100_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0x5574c5ac41f0_0, 0, 1;
    %load/vec4 v0x5574c5ac41f0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.11, 8;
    %pushi/str "twait.mem";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
    %jmp T_0.12;
T_0.11 ;
    %pushi/str "twait";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
T_0.12 ;
    %jmp T_0.10;
T_0.10 ;
    %pop/vec4 1;
    %jmp T_0.7;
T_0.7 ;
    %pop/vec4 1;
    %jmp T_0.4;
T_0.4 ;
    %pop/vec4 1;
    %pushi/str "unknown";
    %ret/str 0; Assign to test_extract_instruction_name
    %disable S_0x5574c5ac3d40;
    %end;
    .scope S_0x5574c5a72190;
T_1 ;
    %fork t_1, S_0x5574c5a72320;
    %jmp t_0;
    .scope S_0x5574c5a72320;
t_1 ;
    %vpi_call/w 3 53 "$display", "=== Minimal twait test ===" {0 0 0};
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v0x5574c5a73fa0_0, 0, 32;
    %pushi/vec4 0, 0, 96;
    %load/vec4 v0x5574c5a73fa0_0;
    %concat/vec4; draw_concat_vec4
    %store/vec4 v0x5574c5a73750_0, 0, 128;
    %vpi_call/w 3 59 "$display", "Testing instruction: 0x%08x", v0x5574c5a73fa0_0 {0 0 0};
    %alloc S_0x5574c5ac3d40;
    %load/vec4 v0x5574c5a73750_0;
    %store/vec4 v0x5574c5ac4100_0, 0, 128;
    %callf/str TD_minimal_test.test_extract_instruction_name, S_0x5574c5ac3d40;
    %free S_0x5574c5ac3d40;
    %store/str v0x5574c5a73bb0_0;
    %vpi_call/w 3 62 "$display", "Result: %s", v0x5574c5a73bb0_0 {0 0 0};
    %vpi_call/w 3 64 "$display", "=== Test Complete ===" {0 0 0};
    %end;
    .scope S_0x5574c5a72190;
t_0 %join;
    %end;
    .thread T_1;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "-";
    "minimal_test.sv";
