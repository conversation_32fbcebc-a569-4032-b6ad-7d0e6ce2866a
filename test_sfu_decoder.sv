// Test SystemVerilog SFU instruction decoder
// This file tests the enhanced SFU instruction decoding

module test_sfu_decoder;
    import tile_decoder_pkg::*;
    
    initial begin
        // Test the specific instruction: 0x4237b0000627b
        logic [127:0] test_instruction;
        instr_length_e length;
        string result;
        
        // Set up the 64-bit instruction: 0x0004237b0000627b
        // Word 1 (bits 31:0):  0x0000627b
        // Word 2 (bits 63:32): 0x0004237b
        test_instruction[31:0] = 32'h0000627b;
        test_instruction[63:32] = 32'h0004237b;
        test_instruction[127:64] = 64'h0; // Clear upper bits
        
        length = INSTR_64BIT;
        
        $display("=== Testing SFU Instruction Decoder ===");
        $display("Input instruction: 0x%016x", test_instruction[63:0]);
        $display("Word 1: 0x%08x", test_instruction[31:0]);
        $display("Word 2: 0x%08x", test_instruction[63:32]);
        $display("");
        
        // Test instruction length detection
        instr_length_e detected_length = get_instruction_length(test_instruction[31:0]);
        $display("Detected length: %0d-bit (expected: %0d-bit)", 
                get_instruction_bits(detected_length), 
                get_instruction_bits(length));
        
        // Test instruction name extraction
        string instr_name = extract_instruction_name(test_instruction, length);
        $display("Instruction name: %s", instr_name);
        
        // Test full disassembly
        result = disassemble_instruction(test_instruction, length);
        $display("Full disassembly: %s", result);
        $display("");
        
        // Verify expected result
        if (instr_name == "texp2.tt.f32") begin
            $display("✓ SUCCESS: Instruction correctly identified as texp2.tt.f32");
        end else begin
            $display("✗ FAILURE: Expected 'texp2.tt.f32', got '%s'", instr_name);
        end
        
        if (result == "texp2.tt.f32 T0, T8") begin
            $display("✓ SUCCESS: Full disassembly matches expected result");
        end else begin
            $display("✗ FAILURE: Expected 'texp2.tt.f32 T0, T8', got '%s'", result);
        end
        
        // Test with other SFU instructions to verify the decoder works for different opcodes
        $display("");
        $display("=== Testing Other SFU Operations ===");
        
        // Test tsin.tt.f32 (vecuop2[5:2] = 1)
        test_instruction[31:0] = 32'h0000627b;
        test_instruction[63:32] = 32'h0004217b;  // 0x0004217b0000627b
        result = disassemble_instruction(test_instruction, length);
        $display("tsin test: %s", result);
        
        // Test tlog2.tt.f32 (vecuop2[5:2] = 4) 
        test_instruction[63:32] = 32'h0004247b;  // 0x0004247b0000627b
        result = disassemble_instruction(test_instruction, length);
        $display("tlog2 test: %s", result);
        
        // Test tsqrt.tt.f32 (vecuop2[5:2] = 7)
        test_instruction[63:32] = 32'h0004277b;  // 0x0004277b0000627b
        result = disassemble_instruction(test_instruction, length);
        $display("tsqrt test: %s", result);
        
        // Test ttanh.tt.f32 (vecuop2[5:2] = 9)
        test_instruction[63:32] = 32'h0004297b;  // 0x0004297b0000627b
        result = disassemble_instruction(test_instruction, length);
        $display("ttanh test: %s", result);
        
        $display("");
        $display("=== Test Complete ===");
        
        $finish;
    end
    
endmodule
