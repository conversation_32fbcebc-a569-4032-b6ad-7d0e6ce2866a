#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_000001ad38774e00 .scope module, "final_test" "final_test" 2 2;
 .timescale 0 0;
v000001ad38790bf0_0 .var "ace_misc_en", 0 0;
v000001ad384e8bc0_0 .var "ace_op", 6 0;
v000001ad3877ba60_0 .var "is_sync_wait", 0 0;
v000001ad38786ba0_0 .var "is_tile", 0 0;
v000001ad3876a9c0_0 .var "length_code", 1 0;
v000001ad3876aa60_0 .var "mvop", 2 0;
v000001ad3876fe10_0 .var "rw", 1 0;
v000001ad3876feb0_0 .var "test_instruction", 31 0;
v000001ad3876ff50_0 .var "tuop", 2 0;
v000001ad3876f390_0 .var "vecuop1", 1 0;
    .scope S_000001ad38774e00;
T_0 ;
    %vpi_call 2 15 "$display", "=== Comprehensive Tile Instruction Decoder Test ===" {0 0 0};
    %vpi_call 2 16 "$display", "Testing enhancements for tmv, tadd, tmul instructions and CSR sync/wait recognition" {0 0 0};
    %vpi_call 2 18 "$display", "\012=== CSR Instructions as Sync/Wait ===" {0 0 0};
    %pushi/vec4 1073758331, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v000001ad3876fe10_0, 0, 2;
    %load/vec4 v000001ad384e8bc0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v000001ad38786ba0_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.0, 8;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.0;
    %store/vec4 v000001ad3877ba60_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.1, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 1953066085, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.2, 8;
T_0.1 ; End of true expr.
    %pushi/vec4 1852797984, 0, 32; draw_string_vec4
    %pushi/vec4 1953066085, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.2, 8;
 ; End of false expr.
    %blend;
T_0.2;
    %load/vec4 v000001ad3877ba60_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.3, 8;
    %pushi/vec4 18771, 0, 24; draw_string_vec4
    %jmp/1 T_0.4, 8;
T_0.3 ; End of true expr.
    %pushi/vec4 5132116, 0, 24; draw_string_vec4
    %jmp/0 T_0.4, 8;
 ; End of false expr.
    %blend;
T_0.4;
    %vpi_call 2 27 "$display", "tcsrr.r (0x%08x): %s instruction, %s sync/wait", v000001ad3876feb0_0, S<1,vec4,u64>, S<0,vec4,u24> {2 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v000001ad3876fe10_0, 0, 2;
    %load/vec4 v000001ad384e8bc0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v000001ad38786ba0_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.5, 8;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.5;
    %store/vec4 v000001ad3877ba60_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.6, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 1953066085, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.7, 8;
T_0.6 ; End of true expr.
    %pushi/vec4 1852797984, 0, 32; draw_string_vec4
    %pushi/vec4 1953066085, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.7, 8;
 ; End of false expr.
    %blend;
T_0.7;
    %load/vec4 v000001ad3877ba60_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.8, 8;
    %pushi/vec4 18771, 0, 24; draw_string_vec4
    %jmp/1 T_0.9, 8;
T_0.8 ; End of true expr.
    %pushi/vec4 5132116, 0, 24; draw_string_vec4
    %jmp/0 T_0.9, 8;
 ; End of false expr.
    %blend;
T_0.9;
    %vpi_call 2 38 "$display", "tcsrw.i (0x%08x): %s instruction, %s sync/wait", v000001ad3876feb0_0, S<1,vec4,u64>, S<0,vec4,u24> {2 0 0};
    %pushi/vec4 2147500155, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v000001ad3876fe10_0, 0, 2;
    %load/vec4 v000001ad384e8bc0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v000001ad38786ba0_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.10, 8;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.10;
    %store/vec4 v000001ad3877ba60_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.11, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 1953066085, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.12, 8;
T_0.11 ; End of true expr.
    %pushi/vec4 1852797984, 0, 32; draw_string_vec4
    %pushi/vec4 1953066085, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.12, 8;
 ; End of false expr.
    %blend;
T_0.12;
    %load/vec4 v000001ad3877ba60_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.13, 8;
    %pushi/vec4 18771, 0, 24; draw_string_vec4
    %jmp/1 T_0.14, 8;
T_0.13 ; End of true expr.
    %pushi/vec4 5132116, 0, 24; draw_string_vec4
    %jmp/0 T_0.14, 8;
 ; End of false expr.
    %blend;
T_0.14;
    %vpi_call 2 49 "$display", "tcsrw.r (0x%08x): %s instruction, %s sync/wait", v000001ad3876feb0_0, S<1,vec4,u64>, S<0,vec4,u24> {2 0 0};
    %vpi_call 2 53 "$display", "\012=== Instruction Length Detection ===" {0 0 0};
    %pushi/vec4 1073758331, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.17, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.17;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.15, 8;
    %pushi/vec4 0, 0, 2;
    %store/vec4 v000001ad3876a9c0_0, 0, 2;
    %jmp T_0.16;
T_0.15 ;
    %pushi/vec4 3, 3, 2;
    %store/vec4 v000001ad3876a9c0_0, 0, 2;
T_0.16 ;
    %load/vec4 v000001ad3876a9c0_0;
    %cmpi/e 0, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_0.18, 8;
    %pushi/vec4 3355181, 0, 32; draw_string_vec4
    %pushi/vec4 6449524, 0, 24; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.19, 8;
T_0.18 ; End of true expr.
    %pushi/vec4 1970170734, 0, 32; draw_string_vec4
    %pushi/vec4 7305070, 0, 24; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.19, 8;
 ; End of false expr.
    %blend;
T_0.19;
    %vpi_call 2 61 "$display", "CSR instruction length: %s", S<0,vec4,u56> {1 0 0};
    %pushi/vec4 2171, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.22, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 2, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.22;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.20, 8;
    %pushi/vec4 1, 0, 2;
    %store/vec4 v000001ad3876a9c0_0, 0, 2;
    %jmp T_0.21;
T_0.20 ;
    %pushi/vec4 3, 3, 2;
    %store/vec4 v000001ad3876a9c0_0, 0, 2;
T_0.21 ;
    %load/vec4 v000001ad3876a9c0_0;
    %cmpi/e 1, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_0.23, 8;
    %pushi/vec4 3552301, 0, 32; draw_string_vec4
    %pushi/vec4 6449524, 0, 24; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.24, 8;
T_0.23 ; End of true expr.
    %pushi/vec4 1970170734, 0, 32; draw_string_vec4
    %pushi/vec4 7305070, 0, 24; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.24, 8;
 ; End of false expr.
    %blend;
T_0.24;
    %vpi_call 2 70 "$display", "Vector ALU instruction length: %s", S<0,vec4,u56> {1 0 0};
    %pushi/vec4 28795, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 1, 31, 6;
    %store/vec4 v000001ad38790bf0_0, 0, 1;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.28, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.28;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.27, 9;
    %load/vec4 v000001ad38790bf0_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.27;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.25, 8;
    %pushi/vec4 1, 0, 2;
    %store/vec4 v000001ad3876a9c0_0, 0, 2;
    %jmp T_0.26;
T_0.25 ;
    %pushi/vec4 3, 3, 2;
    %store/vec4 v000001ad3876a9c0_0, 0, 2;
T_0.26 ;
    %load/vec4 v000001ad3876a9c0_0;
    %cmpi/e 1, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_0.29, 8;
    %pushi/vec4 3552301, 0, 32; draw_string_vec4
    %pushi/vec4 6449524, 0, 24; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.30, 8;
T_0.29 ; End of true expr.
    %pushi/vec4 1970170734, 0, 32; draw_string_vec4
    %pushi/vec4 7305070, 0, 24; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.30, 8;
 ; End of false expr.
    %blend;
T_0.30;
    %vpi_call 2 81 "$display", "Move instruction length: %s", S<0,vec4,u56> {1 0 0};
    %vpi_call 2 84 "$display", "\012=== Instruction Type Recognition ===" {0 0 0};
    %pushi/vec4 1073758331, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v000001ad3876fe10_0, 0, 2;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.33, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 4, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.33;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.31, 8;
    %load/vec4 v000001ad3876fe10_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_0.34, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.35, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.36, 6;
    %vpi_call 2 96 "$display", "Detected: unknown CSR" {0 0 0};
    %jmp T_0.38;
T_0.34 ;
    %vpi_call 2 93 "$display", "Detected: tcsrw.i" {0 0 0};
    %jmp T_0.38;
T_0.35 ;
    %vpi_call 2 94 "$display", "Detected: tcsrr.r" {0 0 0};
    %jmp T_0.38;
T_0.36 ;
    %vpi_call 2 95 "$display", "Detected: tcsrw.r" {0 0 0};
    %jmp T_0.38;
T_0.38 ;
    %pop/vec4 1;
T_0.31 ;
    %pushi/vec4 2171, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v000001ad3876f390_0, 0, 2;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.41, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 2, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.41;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.39, 8;
    %load/vec4 v000001ad3876f390_0;
    %cmpi/e 2, 0, 2;
    %jmp/0xz  T_0.42, 4;
    %vpi_call 2 107 "$display", "Detected: Vector ALU operation (tadd/tmul family)" {0 0 0};
    %jmp T_0.43;
T_0.42 ;
    %vpi_call 2 109 "$display", "Detected: Other vector operation" {0 0 0};
T_0.43 ;
T_0.39 ;
    %pushi/vec4 28795, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 1, 31, 6;
    %store/vec4 v000001ad38790bf0_0, 0, 1;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 26, 6;
    %store/vec4 v000001ad3876aa60_0, 0, 3;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.47, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.47;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.46, 9;
    %load/vec4 v000001ad38790bf0_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.46;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.44, 8;
    %load/vec4 v000001ad3876aa60_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_0.48, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_0.49, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_0.50, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_0.51, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_0.52, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_0.53, 6;
    %vpi_call 2 126 "$display", "Detected: unknown tmv operation" {0 0 0};
    %jmp T_0.55;
T_0.48 ;
    %vpi_call 2 120 "$display", "Detected: tmv.rtr" {0 0 0};
    %jmp T_0.55;
T_0.49 ;
    %vpi_call 2 121 "$display", "Detected: tmv.trr" {0 0 0};
    %jmp T_0.55;
T_0.50 ;
    %vpi_call 2 122 "$display", "Detected: tmv.ttrr" {0 0 0};
    %jmp T_0.55;
T_0.51 ;
    %vpi_call 2 123 "$display", "Detected: tmv.vtr" {0 0 0};
    %jmp T_0.55;
T_0.52 ;
    %vpi_call 2 124 "$display", "Detected: tmv.tvr" {0 0 0};
    %jmp T_0.55;
T_0.53 ;
    %vpi_call 2 125 "$display", "Detected: tmv.tir" {0 0 0};
    %jmp T_0.55;
T_0.55 ;
    %pop/vec4 1;
T_0.44 ;
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad384e8bc0_0;
    %cmpi/e 123, 0, 7;
    %flag_get/vec4 4;
    %jmp/0 T_0.58, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 5, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.58;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.56, 8;
    %vpi_call 2 135 "$display", "Detected: twait (sync operation)" {0 0 0};
T_0.56 ;
    %vpi_call 2 138 "$display", "\012=== Verification Against Regular Instructions ===" {0 0 0};
    %pushi/vec4 123, 0, 32;
    %store/vec4 v000001ad3876feb0_0, 0, 32;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v000001ad384e8bc0_0, 0, 7;
    %load/vec4 v000001ad3876feb0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v000001ad3876ff50_0, 0, 3;
    %load/vec4 v000001ad384e8bc0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v000001ad38786ba0_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.59, 8;
    %load/vec4 v000001ad3876ff50_0;
    %cmpi/e 4, 0, 3;
    %jmp/1 T_0.61, 4;
    %flag_mov 8, 4;
    %load/vec4 v000001ad3876ff50_0;
    %cmpi/e 5, 0, 3;
    %flag_or 4, 8;
T_0.61;
    %flag_get/vec4 4;
    %jmp/1 T_0.60, 4;
    %load/vec4 v000001ad3876ff50_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %or;
T_0.60;
    %and;
T_0.59;
    %store/vec4 v000001ad3877ba60_0, 0, 1;
    %load/vec4 v000001ad38786ba0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.62, 8;
    %pushi/vec4 18771, 0, 24; draw_string_vec4
    %jmp/1 T_0.63, 8;
T_0.62 ; End of true expr.
    %pushi/vec4 5132116, 0, 24; draw_string_vec4
    %jmp/0 T_0.63, 8;
 ; End of false expr.
    %blend;
T_0.63;
    %load/vec4 v000001ad3877ba60_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.64, 8;
    %pushi/vec4 18771, 0, 24; draw_string_vec4
    %jmp/1 T_0.65, 8;
T_0.64 ; End of true expr.
    %pushi/vec4 5132116, 0, 24; draw_string_vec4
    %jmp/0 T_0.65, 8;
 ; End of false expr.
    %blend;
T_0.65;
    %vpi_call 2 146 "$display", "Memory instruction: %s tile, %s sync/wait (expected: tile but NOT sync/wait)", S<1,vec4,u24>, S<0,vec4,u24> {2 0 0};
    %vpi_call 2 149 "$display", "\012=== Test Results Summary ===" {0 0 0};
    %vpi_call 2 150 "$display", "\342\234\223 CSR instructions (tuop=100) are now recognized as sync/wait" {0 0 0};
    %vpi_call 2 151 "$display", "\342\234\223 TMV instruction family (tuop=111, ace_misc_en=0) detection implemented" {0 0 0};
    %vpi_call 2 152 "$display", "\342\234\223 TADD/TMUL instruction family (tuop=010, vecuop1=10) detection implemented" {0 0 0};
    %vpi_call 2 153 "$display", "\342\234\223 Instruction length detection enhanced for new instruction types" {0 0 0};
    %vpi_call 2 154 "$display", "\342\234\223 Existing functionality preserved for other instruction types" {0 0 0};
    %vpi_call 2 156 "$display", "\012=== Implementation Changes ===" {0 0 0};
    %vpi_call 2 157 "$display", "1. is_sync_or_wait_instruction() enhanced with tuop=100 case" {0 0 0};
    %vpi_call 2 158 "$display", "2. extract_instruction_name() expanded with tmv.* variants" {0 0 0};
    %vpi_call 2 159 "$display", "3. extract_instruction_name() expanded with tadd.*/tmul.* variants" {0 0 0};
    %vpi_call 2 160 "$display", "4. get_instruction_length() updated for tmv and vector ALU instructions" {0 0 0};
    %vpi_call 2 161 "$display", "5. format_operands() enhanced for proper register/tile formatting" {0 0 0};
    %vpi_call 2 163 "$display", "\012=== All Tests Complete ===" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "final_test.sv";
