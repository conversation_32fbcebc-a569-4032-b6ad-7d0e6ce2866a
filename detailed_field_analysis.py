#!/usr/bin/env python3
"""
Detailed analysis of instruction 0x802e107b0000607b to understand correct field extraction
"""

def analyze_instruction_bits():
    instruction = 0x802e107b0000607b
    
    # Split into 32-bit words
    first_word = instruction & 0xFFFFFFFF        # Lower 32 bits: 0x0000607b  
    second_word = (instruction >> 32) & 0xFFFFFFFF  # Upper 32 bits: 0x802e107b
    
    print(f"Instruction: 0x{instruction:016x}")
    print(f"First word:  0x{first_word:08x} = {first_word:032b}")
    print(f"Second word: 0x{second_word:08x} = {second_word:032b}")
    print()
    
    # Show bit positions
    print("Bit positions in second word (0x802e107b):")
    print("31 30 29 28 27 26 25 24 23 22 21 20 19 18 17 16 15 14 13 12 11 10 09 08 07 06 05 04 03 02 01 00")
    print("1  0  0  0  0  0  0  0  0  0  1  0  1  1  1  0  0  0  0  1  0  0  0  0  0  1  1  1  1  0  1  1")
    print()
    
    # Extract specific fields from second word
    td_field = (second_word >> 24) & 0xFF  # bits [31:24]
    rs1_field = (second_word >> 15) & 0x1F  # bits [19:15]
    offseten = (second_word >> 15) & 1     # bit [15]
    rmten = (second_word >> 20) & 1        # bit [20]
    tuop_second = (second_word >> 12) & 7  # bits [14:12]
    
    print("Field extraction from second word:")
    print(f"Td (bits 31:24):   {td_field:08b} = {td_field} (0x{td_field:02x})")
    print(f"Rs1 (bits 19:15):  {rs1_field:05b} = {rs1_field} (0x{rs1_field:02x})")
    print(f"OFFSETEN (bit 15): {offseten}")
    print(f"RMTEN (bit 20):    {rmten}")
    print(f"TUOP (bits 14:12): {tuop_second:03b} = {tuop_second}")
    print()
    
    # Let me manually extract Rs1 field more carefully
    # Second word = 0x802e107b = binary: 10000000001011100001000001111011
    # bits [19:15] should be bits 19,18,17,16,15
    # Counting from right (bit 0), these are positions 15,16,17,18,19
    
    bit_19 = (second_word >> 19) & 1
    bit_18 = (second_word >> 18) & 1  
    bit_17 = (second_word >> 17) & 1
    bit_16 = (second_word >> 16) & 1
    bit_15 = (second_word >> 15) & 1
    
    rs1_manual = (bit_19 << 4) | (bit_18 << 3) | (bit_17 << 2) | (bit_16 << 1) | bit_15
    
    print("Manual Rs1 extraction:")
    print(f"bit 19: {bit_19}")
    print(f"bit 18: {bit_18}")
    print(f"bit 17: {bit_17}")
    print(f"bit 16: {bit_16}")
    print(f"bit 15: {bit_15}")
    print(f"Rs1 = {rs1_manual} = {rs1_manual:05b}")
    print()
    
    # Now let me check if we get the expected values
    print("Expected vs actual:")
    print(f"Expected Td: 6, Actual: {td_field}")
    print(f"Expected Rs1: 3, Actual: {rs1_manual}")
    print()
    
    # The problem might be that the expected values are wrong, or the bit layout is different
    # Let me check what values would give us T6, (t3)
    print("What bit patterns would give T6, (t3)?")
    print(f"For Td=6: need bits [31:24] = {6:08b} = 0x{6:02x}")
    print(f"For Rs1=3: need bits [19:15] = {3:05b} = 0x{3:02x}")
    print()
    
    # Build the expected second word
    expected_second = (6 << 24) | (3 << 15) | (1 << 12)  # Td=6, Rs1=3, tuop=1
    print(f"Expected second word with Td=6, Rs1=3: 0x{expected_second:08x}")
    print(f"Actual second word: 0x{second_word:08x}")
    print()
    
    # Check if maybe I need to look at a different instruction encoding
    # Let me analyze what instruction would actually give us 0x802e107b0000607b
    print("=== REVERSE ENGINEERING ===")
    print("Given actual values from 0x802e107b:")
    print(f"Td = {td_field} (0x{td_field:02x})")  
    print(f"Rs1 = {rs1_manual}")
    print(f"This would format as: T{td_field}, (t{rs1_manual}), 0, 0")


if __name__ == "__main__":
    analyze_instruction_bits()
