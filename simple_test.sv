// Simple test for twait instruction
import tile_decoder_pkg::*;

module simple_test;

    initial begin
        logic [31:0] test_instruction;
        instr_collector_t collector;
        string disasm_result;
        logic [2:0] waitop;

        $display("=== Simple twait test ===");
        
        // Test the specific instruction: 0x7080507B
        test_instruction = 32'h7080507B;
        $display("Testing instruction: 0x%08x", test_instruction);
        
        // Check waitop field
        waitop = test_instruction[30:28];
        $display("waitop [30:28]: %b (%d)", waitop, waitop);
        
        // Check if it's recognized as a tile instruction
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("Is tile instruction: YES");
            
            // Initialize collector
            collector = tile_decoder_pkg::init_collector(test_instruction);
            $display("Expected length: %0d", collector.expected_length);
            $display("Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                // Try to get instruction name only
                disasm_result = tile_decoder_pkg::extract_instruction_name(
                    collector.instruction_data, collector.expected_length);
                $display("Instruction name: %s", disasm_result);
            end
        end else begin
            $display("Not recognized as tile instruction");
        end
        
        $display("=== Test Complete ===");
    end

endmodule
