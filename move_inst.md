# Move and ASP_store Operation
## Move Instructions
| Syntax | Description |
| - | - |
| tuop | 111代表move或asp指令，110代表低32bit指令|
| mvop | 000：代表tile寄存器向标量寄存器move；001：标量寄存器向tile寄存器move；010: tile寄存器向tile寄存器move; 100: tile寄存器向向量寄存器move; 101：向量寄存器向tile寄存器move; 111:特殊值立即数向tile寄存器move |
| bc | 写到tile寄存器时是否需要扩展为1024Byte写入；仅在move到tile寄存器时可生效 |
| Ts1 | 8192-bit tile寄存器编号，指定src tile reg。 |
| rs2 | 64-bit整形标量寄存器编号，tile寄存器的index，可按32B或128Byte维度索引，粒度按照操作的元素宽度设计。rs2在bc=1时无效，此时是写整个tile reg，index无法指定tile reg内的起始位置。在操作128B时该值大于等于8是未定义行为；在操作32B时，该值大于等于32是未定义行为。 |
| rs3 | 64-bit整形标量寄存器编号，tile寄存器的index，可按32B或128Byte维度索引，粒度按照操作的元素宽度设计。rs2在bc=1时无效，此时是写整个tile reg，index无法指定tile reg内的起始位置。在操作128B时该值大于等于8是未定义行为；在操作32B时，该值大于等于32是未定义行为。 |
| Td | 8192-bit tile寄存器编号，指定dst tile reg。 |
| vs1 | 1024-bit向量寄存器编号，src vec reg。 |
| vd | 1024-bit向量寄存器编号，dst vec reg。 |
| rs1 | 64-bit整形标量寄存器编号，src integer reg。 |
| rd | 64-bit整形标量寄存器编号，dst integer reg。 |
| imm | special num编号，可查下表 |
| dtype | 立即数输入时的类型，00代表fp32，01代表bf16，10代表fp16 |
### Tile Tile to Vector
- 该指令仅使用RV向量寄存器作为目的寄存器
- 该指令将rs2指定index的128Byte的Tile寄存器的数据move到vd指定的向量寄存器
**下列三类指令的e32/e16/e8和功能无关，仅为适配ASP通路设置为不同指令**
`tmv.vtr.e32 vd, Ts1, rs2`
该指令仅能在vset(i)vl(i)设置SEW=32, vl=32, lmul=1时使用，用于和32bits的元素进行交互。
@import "encode/tuop_111/ace_misc_en_0/mvop_100/func6_100110/tmv.vtr.e32.md"
`tmv.vtr.e16 vd, Ts1, rs2`
该指令仅能在vset(i)vl(i)设置SEW=16, vl=64, lmul=1时使用，用于和16bits的元素进行交互。
@import "encode/tuop_111/ace_misc_en_0/mvop_100/func6_100101/tmv.vtr.e16.md"
`tmv.vtr.e8 vd, Ts1, rs2`
该指令仅能在vset(i)vl(i)设置SEW=8, vl=128, lmul=1时使用，用于和8bits的元素进行交互。
@import "encode/tuop_111/ace_misc_en_0/mvop_100/func6_100000/tmv.vtr.e8.md"
### Tile Vector to Tile
- 该指令仅使用Tile寄存器作为目的寄存器
- 该指令将vs1指定的向量寄存器的数据move到rs2指定index的128Byte的Tile寄存器
- broadcast有效时，忽视rs2的内容，将vector的数据广播写到完整的1024B的Tile寄存器中
**下列三类指令的e32/e16/e8和功能无关，仅为适配ASP通路设置为不同指令**
`tmv.tvr.e32.[broadcast] Td, vs1, rs2`
该指令仅能在vset(i)vl(i)设置SEW=32, vl=32, lmul=1时使用，用于和32bits的元素进行交互。
@import "encode/tuop_111/ace_misc_en_0/mvop_101/func6_100110/tmv.tvr.e32.md"
`tmv.tvr.e16.[broadcast] Td, vs1, rs2`
该指令仅能在vset(i)vl(i)设置SEW=16, vl=64, lmul=1时使用，用于和16bits的元素进行交互。
@import "encode/tuop_111/ace_misc_en_0/mvop_101/func6_100101/tmv.tvr.e16.md"
`tmv.tvr.e8.[broadcast] Td, vs1, rs2`
该指令仅能在vset(i)vl(i)设置SEW=8, vl=128, lmul=1时使用，用于和8bits的元素进行交互。
@import "encode/tuop_111/ace_misc_en_0/mvop_101/func6_100000/tmv.tvr.e8.md"
### Tile Tile to Scalar
- 该指令仅使用RV整型标量寄存器，标量浮点运算需在执行该指令后move到浮点标量寄存器
- 该指令将rs2指定index的32Byte的low 32bits写入到标量寄存器中，该标量寄存器的high 32bits填0
`tmv.rtr rd, Ts1, rs2`
@import "encode/tuop_111/ace_misc_en_0/mvop_000/tmv.rtr.md"
### Tile Scalar to Tile
- 该指令仅使用RV整型标量寄存器，标量浮点寄存器的内容需先move到整型标量寄存器后，再在执行该指令move到tile寄存器
- 该指令将rs1标量寄存器的值中的low 32bits扩展到32Byte写入到tile reg中rs2指定index的32Byte中
- broadcast有效时，忽视rs2的内容，将vector的数据广播写到完整的1024B的Tile寄存器中
`tmv.trr.[broadcast] Td, rs1, rs2`
@import "encode/tuop_111/ace_misc_en_0/mvop_001/tmv.trr.md"
### Tile To Tile
- 该指令仅使用Tile寄存器作为目的寄存器
- 该指令将编号为Ts1的Tile寄存器中的rs3指定index的32/128Byte写入到编号为Td的rs2指定index的32/128Byte中
- size仅支持为0和2，分别代表32Byte和128Byte
- 支持Ts1和Td为同一个Tile Reg
`tmv.ttrr.u32 td, ts1, rs2, rs3`
`tmv.ttrr.u128 td, ts1, rs2, rs3`
@import "encode/tuop_111/ace_misc_en_0/mvop_010/tmv.ttrr.md"
### Tile Immediate to Tile
该指令将special num传入指定的tile reg中
- 该指令仅使用立即数
- 该指令将根据下表的32/16bits扩展到32Byte值写入到编号为Td的rs2指定index的32Byte中
- broadcast有效时，忽视rs2的内容，将立即数的数据广播写到完整的1024B的Tile寄存器中
@import "table/special_num.csv"
`tmv.tir.[broadcast].f32/bf16/f16 Td, imm, rs2`
@import "encode/tuop_111/ace_misc_en_0/mvop_111/tmv.tir.md"