#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x55bbeadfcd10 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x55bbeadcd250 .scope package, "tile_decoder_pkg" "tile_decoder_pkg" 3 5;
 .timescale 0 0;
P_0x55bbeadcd3e0 .param/l "TILE_ACE_OP" 0 3 16, C4<1111011>;
enum0x55bbead4ef80 .enum4 (2)
   "INSTR_32BIT" 2'b00,
   "INSTR_64BIT" 2'b01,
   "INSTR_96BIT" 2'b10,
   "INSTR_128BIT" 2'b11
 ;
S_0x55bbeadd29f0 .scope autofunction.vec4.s134, "add_word_to_collector" "add_word_to_collector" 3 133, 3 133 0, S_0x55bbeadcd250;
 .timescale 0 0;
; Variable add_word_to_collector is vec4 return value of scope S_0x55bbeadd29f0
v0x55bbeae3a0a0_0 .var "collector", 133 0;
v0x55bbeae3a180_0 .var "new_collector", 133 0;
v0x55bbeae3a240_0 .var "word", 31 0;
TD_tile_decoder_pkg.add_word_to_collector ;
    %load/vec4 v0x55bbeae3a0a0_0;
    %store/vec4 v0x55bbeae3a180_0, 0, 134;
    %load/vec4 v0x55bbeae3a0a0_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %load/vec4 v0x55bbeae3a0a0_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmpi/u 4, 0, 32;
    %flag_get/vec4 5;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %load/vec4 v0x55bbeae3a0a0_0;
    %parti/u 2, 4, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.2, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.3, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.4, 6;
    %jmp T_0.5;
T_0.2 ;
    %load/vec4 v0x55bbeae3a240_0;
    %ix/load 4, 38, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 32;
    %jmp T_0.5;
T_0.3 ;
    %load/vec4 v0x55bbeae3a240_0;
    %ix/load 4, 70, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 32;
    %jmp T_0.5;
T_0.4 ;
    %load/vec4 v0x55bbeae3a240_0;
    %ix/load 4, 102, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 32;
    %jmp T_0.5;
T_0.5 ;
    %pop/vec4 1;
    %load/vec4 v0x55bbeae3a0a0_0;
    %parti/u 2, 4, 32;
    %addi 1, 0, 2;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 2;
    %load/vec4 v0x55bbeae3a0a0_0;
    %parti/u 2, 2, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.6, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.7, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 1;
    %jmp T_0.10;
T_0.6 ;
    %pushi/vec4 2, 0, 32;
    %load/vec4 v0x55bbeae3a180_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 1;
    %jmp T_0.10;
T_0.7 ;
    %pushi/vec4 3, 0, 32;
    %load/vec4 v0x55bbeae3a180_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 1;
    %jmp T_0.10;
T_0.8 ;
    %pushi/vec4 4, 0, 32;
    %load/vec4 v0x55bbeae3a180_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae3a180_0, 4, 1;
    %jmp T_0.10;
T_0.10 ;
    %pop/vec4 1;
T_0.0 ;
    %load/vec4 v0x55bbeae3a180_0;
    %ret/vec4 0, 0, 134;  Assign to add_word_to_collector (store_vec4_to_lval)
    %disable S_0x55bbeadd29f0;
    %end;
S_0x55bbeae3a320 .scope autofunction.str, "disassemble_instruction" "disassemble_instruction" 3 561, 3 561 0, S_0x55bbeadcd250;
 .timescale 0 0;
; Variable disassemble_instruction is string return value of scope S_0x55bbeae3a320
v0x55bbeae3a5e0_0 .var/str "instr_name";
v0x55bbeae3a6a0_0 .var "instruction_data", 127 0;
v0x55bbeae3a760_0 .var "length", 1 0;
v0x55bbeae3a840_0 .var/str "operands";
v0x55bbeae3a950_0 .var/str "result";
TD_tile_decoder_pkg.disassemble_instruction ;
    %alloc S_0x55bbeae3adb0;
    %load/vec4 v0x55bbeae3a6a0_0;
    %load/vec4 v0x55bbeae3a760_0;
    %store/vec4 v0x55bbeae3d910_0, 0, 2;
    %store/vec4 v0x55bbeae3d820_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.extract_instruction_name, S_0x55bbeae3adb0;
    %free S_0x55bbeae3adb0;
    %store/str v0x55bbeae3a5e0_0;
    %alloc S_0x55bbeae3df40;
    %load/vec4 v0x55bbeae3a6a0_0;
    %load/vec4 v0x55bbeae3a760_0;
    %load/str v0x55bbeae3a5e0_0;
    %store/str v0x55bbeae3fba0_0;
    %store/vec4 v0x55bbeae3fd20_0, 0, 2;
    %store/vec4 v0x55bbeae3fc60_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.format_operands, S_0x55bbeae3df40;
    %free S_0x55bbeae3df40;
    %store/str v0x55bbeae3a840_0;
    %load/str v0x55bbeae3a840_0;
    %pushi/str "";
    %cmp/str;
    %flag_inv 4;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.11, 8;
    %vpi_call/w 3 571 "$sformat", v0x55bbeae3a950_0, "%s %s", v0x55bbeae3a5e0_0, v0x55bbeae3a840_0 {0 0 0};
    %jmp T_1.12;
T_1.11 ;
    %load/str v0x55bbeae3a5e0_0;
    %store/str v0x55bbeae3a950_0;
T_1.12 ;
    %load/str v0x55bbeae3a950_0;
    %ret/str 0; Assign to disassemble_instruction
    %disable S_0x55bbeae3a320;
    %end;
S_0x55bbeae3aa10 .scope autofunction.vec4.s7, "extract_ace_op" "extract_ace_op" 3 28, 3 28 0, S_0x55bbeadcd250;
 .timescale 0 0;
; Variable extract_ace_op is vec4 return value of scope S_0x55bbeae3aa10
v0x55bbeae3acd0_0 .var "word", 31 0;
TD_tile_decoder_pkg.extract_ace_op ;
    %load/vec4 v0x55bbeae3acd0_0;
    %parti/s 7, 0, 2;
    %ret/vec4 0, 0, 7;  Assign to extract_ace_op (store_vec4_to_lval)
    %disable S_0x55bbeae3aa10;
    %end;
S_0x55bbeae3adb0 .scope autofunction.str, "extract_instruction_name" "extract_instruction_name" 3 174, 3 174 0, S_0x55bbeadcd250;
 .timescale 0 0;
v0x55bbeae3d660_0 .var "ace_op", 6 0;
; Variable extract_instruction_name is string return value of scope S_0x55bbeae3adb0
v0x55bbeae3d820_0 .var "instruction_data", 127 0;
v0x55bbeae3d910_0 .var "length", 1 0;
v0x55bbeae3d9f0_0 .var "lsuop", 1 0;
v0x55bbeae3db20_0 .var "memuop", 5 0;
v0x55bbeae3dc00_0 .var "miscop", 2 0;
v0x55bbeae3dce0_0 .var "offseten", 0 0;
v0x55bbeae3dda0_0 .var "rmten", 0 0;
v0x55bbeae3de60_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.extract_instruction_name ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x55bbeae3d660_0, 0, 7;
    %load/vec4 v0x55bbeae3d660_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_3.13, 4;
    %pushi/str "unknown_non_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.13 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55bbeae3de60_0, 0, 3;
    %load/vec4 v0x55bbeae3de60_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.15, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_3.16, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_3.17, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_3.18, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_3.19, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_3.20, 6;
    %pushi/str "unknown_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.22;
T_3.15 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55bbeae3db20_0, 0, 6;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55bbeae3d9f0_0, 0, 2;
    %load/vec4 v0x55bbeae3db20_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 6;
    %cmp/u;
    %jmp/1 T_3.23, 6;
    %dup/vec4;
    %pushi/vec4 8, 0, 6;
    %cmp/u;
    %jmp/1 T_3.24, 6;
    %dup/vec4;
    %pushi/vec4 28, 0, 6;
    %cmp/u;
    %jmp/1 T_3.25, 6;
    %jmp T_3.26;
T_3.23 ;
    %load/vec4 v0x55bbeae3d9f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.27, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.28, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.29, 6;
    %jmp T_3.30;
T_3.27 ;
    %load/vec4 v0x55bbeae3d910_0;
    %cmpi/e 1, 0, 2;
    %jmp/0xz  T_3.31, 4;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 47, 7;
    %store/vec4 v0x55bbeae3dce0_0, 0, 1;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 52, 7;
    %store/vec4 v0x55bbeae3dda0_0, 0, 1;
    %load/vec4 v0x55bbeae3dce0_0;
    %nor/r;
    %load/vec4 v0x55bbeae3dda0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.33, 8;
    %pushi/str "tld.trii.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.34;
T_3.33 ;
    %load/vec4 v0x55bbeae3dce0_0;
    %nor/r;
    %load/vec4 v0x55bbeae3dda0_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.35, 8;
    %pushi/str "tld.trii.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.36;
T_3.35 ;
    %load/vec4 v0x55bbeae3dce0_0;
    %load/vec4 v0x55bbeae3dda0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.37, 8;
    %pushi/str "tld.trir.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.38;
T_3.37 ;
    %pushi/str "tld.trir.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.38 ;
T_3.36 ;
T_3.34 ;
T_3.31 ;
    %jmp T_3.30;
T_3.28 ;
    %pushi/str "tld.trri.stride.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.30;
T_3.29 ;
    %load/vec4 v0x55bbeae3d910_0;
    %cmpi/e 2, 0, 2;
    %jmp/0xz  T_3.39, 4;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 79, 8;
    %store/vec4 v0x55bbeae3dce0_0, 0, 1;
    %load/vec4 v0x55bbeae3dce0_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.41, 8;
    %pushi/str "tld.trvi.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.42;
T_3.41 ;
    %pushi/str "tld.trvr.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.42 ;
T_3.39 ;
    %jmp T_3.30;
T_3.30 ;
    %pop/vec4 1;
    %jmp T_3.26;
T_3.24 ;
    %pushi/str "tst.trvi.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.26;
T_3.25 ;
    %load/vec4 v0x55bbeae3d910_0;
    %cmpi/e 3, 0, 2;
    %jmp/0xz  T_3.43, 4;
    %fork t_1, S_0x55bbeae3af90;
    %jmp t_0;
    .scope S_0x55bbeae3af90;
t_1 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 73, 8;
    %store/vec4 v0x55bbeae3b270_0, 0, 1;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 74, 8;
    %store/vec4 v0x55bbeae3b190_0, 0, 1;
    %load/vec4 v0x55bbeae3b270_0;
    %nor/r;
    %load/vec4 v0x55bbeae3b190_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.45, 8;
    %pushi/str "tacp.rvv.asp2.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.46;
T_3.45 ;
    %load/vec4 v0x55bbeae3b270_0;
    %load/vec4 v0x55bbeae3b190_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.47, 8;
    %pushi/str "tacp.vvr.asp2.srctm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.48;
T_3.47 ;
    %pushi/str "tacp.rvrv.asp2.mbar.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.48 ;
T_3.46 ;
    %end;
    .scope S_0x55bbeae3adb0;
t_0 %join;
T_3.43 ;
    %jmp T_3.26;
T_3.26 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.16 ;
    %pushi/str "tmma.ttt";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.22;
T_3.17 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55bbeae3db20_0, 0, 6;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55bbeae3d9f0_0, 0, 2;
    %load/vec4 v0x55bbeae3d910_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.49, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.50, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_3.51, 6;
    %pushi/str "ace_low";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.53;
T_3.49 ;
    %fork t_3, S_0x55bbeae3b330;
    %jmp t_2;
    .scope S_0x55bbeae3b330;
t_3 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55bbeae3b530_0, 0, 3;
    %load/vec4 v0x55bbeae3db20_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_3.54, 4;
    %load/vec4 v0x55bbeae3b530_0;
    %cmpi/e 1, 0, 3;
    %jmp/0xz  T_3.56, 4;
    %load/vec4 v0x55bbeae3d9f0_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.58, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.59, 6;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.60, 6;
    %pushi/str "unknown_blk_lsuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.62;
T_3.58 ;
    %pushi/str "tld.trr.blk.mx48.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.62;
T_3.59 ;
    %pushi/str "tld.trr.blk.mx6.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.62;
T_3.60 ;
    %pushi/str "tld.trr.blk.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.62;
T_3.62 ;
    %pop/vec4 1;
    %jmp T_3.57;
T_3.56 ;
    %pushi/str "unknown_blk_second_tuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.57 ;
    %jmp T_3.55;
T_3.54 ;
    %load/vec4 v0x55bbeae3db20_0;
    %cmpi/e 0, 0, 6;
    %jmp/0xz  T_3.63, 4;
    %load/vec4 v0x55bbeae3b530_0;
    %cmpi/e 0, 0, 3;
    %jmp/0xz  T_3.65, 4;
    %load/vec4 v0x55bbeae3d9f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.67, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.68, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_3.69, 6;
    %pushi/str "unknown_std_lsuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.71;
T_3.67 ;
    %pushi/str "tld.trii.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.71;
T_3.68 ;
    %pushi/str "tld.trri.stride.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.71;
T_3.69 ;
    %pushi/str "tld.other.64bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.71;
T_3.71 ;
    %pop/vec4 1;
    %jmp T_3.66;
T_3.65 ;
    %pushi/str "unknown_std_second_tuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.66 ;
    %jmp T_3.64;
T_3.63 ;
    %pushi/str "ace_low";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.64 ;
T_3.55 ;
    %end;
    .scope S_0x55bbeae3adb0;
t_2 %join;
    %jmp T_3.53;
T_3.50 ;
    %load/vec4 v0x55bbeae3db20_0;
    %pushi/vec4 0, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55bbeae3d9f0_0;
    %pushi/vec4 2, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.72, 8;
    %fork t_5, S_0x55bbeae3b610;
    %jmp t_4;
    .scope S_0x55bbeae3b610;
t_5 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55bbeae3bae0_0, 0, 3;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 76, 8;
    %store/vec4 v0x55bbeae3bbe0_0, 0, 3;
    %load/vec4 v0x55bbeae3bae0_0;
    %pushi/vec4 0, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55bbeae3bbe0_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.74, 8;
    %fork t_7, S_0x55bbeae3b820;
    %jmp t_6;
    .scope S_0x55bbeae3b820;
t_7 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 9, 5;
    %store/vec4 v0x55bbeae3ba00_0, 0, 1;
    %load/vec4 v0x55bbeae3ba00_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.76, 8;
    %pushi/str "tld.trvi.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.77;
T_3.76 ;
    %pushi/str "tld.trvr.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.77 ;
    %end;
    .scope S_0x55bbeae3b610;
t_6 %join;
    %jmp T_3.75;
T_3.74 ;
    %pushi/str "unknown_96bit_tuop_pattern";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.75 ;
    %end;
    .scope S_0x55bbeae3adb0;
t_4 %join;
    %jmp T_3.73;
T_3.72 ;
    %pushi/str "unknown_96bit_memuop_lsuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.73 ;
    %jmp T_3.53;
T_3.51 ;
    %load/vec4 v0x55bbeae3db20_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_3.78, 4;
    %fork t_9, S_0x55bbeae3bcc0;
    %jmp t_8;
    .scope S_0x55bbeae3bcc0;
t_9 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55bbeae3c340_0, 0, 3;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 76, 8;
    %store/vec4 v0x55bbeae3c420_0, 0, 3;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 108, 8;
    %store/vec4 v0x55bbeae3c240_0, 0, 3;
    %load/vec4 v0x55bbeae3c340_0;
    %pushi/vec4 0, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55bbeae3c420_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x55bbeae3c240_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.80, 8;
    %fork t_11, S_0x55bbeae3bea0;
    %jmp t_10;
    .scope S_0x55bbeae3bea0;
t_11 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 73, 8;
    %store/vec4 v0x55bbeae3c180_0, 0, 1;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 74, 8;
    %store/vec4 v0x55bbeae3c0a0_0, 0, 1;
    %load/vec4 v0x55bbeae3c180_0;
    %nor/r;
    %load/vec4 v0x55bbeae3c0a0_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.82, 8;
    %pushi/str "tacp.rvv.asp2.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.83;
T_3.82 ;
    %load/vec4 v0x55bbeae3c180_0;
    %load/vec4 v0x55bbeae3c0a0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.84, 8;
    %pushi/str "tacp.vvr.asp2.srctm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.85;
T_3.84 ;
    %pushi/str "tacp.rvrv.asp2.mbar.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.85 ;
T_3.83 ;
    %end;
    .scope S_0x55bbeae3bcc0;
t_10 %join;
    %jmp T_3.81;
T_3.80 ;
    %pushi/str "unknown_128bit_tuop_pattern";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.81 ;
    %end;
    .scope S_0x55bbeae3adb0;
t_8 %join;
    %jmp T_3.79;
T_3.78 ;
    %pushi/str "unknown_128bit_memuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.79 ;
    %jmp T_3.53;
T_3.53 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.18 ;
    %fork t_13, S_0x55bbeae3c510;
    %jmp t_12;
    .scope S_0x55bbeae3c510;
t_13 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x55bbeae3c740_0, 0, 2;
    %load/vec4 v0x55bbeae3c740_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.86, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.87, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.88, 6;
    %pushi/str "unknown_csr";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.90;
T_3.86 ;
    %pushi/str "tcsrw.i";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.90;
T_3.87 ;
    %pushi/str "tcsrr.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.90;
T_3.88 ;
    %pushi/str "tcsrw.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.90;
T_3.90 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55bbeae3adb0;
t_12 %join;
    %jmp T_3.22;
T_3.19 ;
    %fork t_15, S_0x55bbeae3c840;
    %jmp t_14;
    .scope S_0x55bbeae3c840;
t_15 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x55bbeae3d480_0, 0, 3;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x55bbeae3d580_0, 0, 3;
    %load/vec4 v0x55bbeae3d480_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.91, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_3.92, 6;
    %pushi/str "unknown_ctrluop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.94;
T_3.91 ;
    %pushi/str "tsync.i";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.94;
T_3.92 ;
    %load/vec4 v0x55bbeae3d580_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.95, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_3.96, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_3.97, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_3.98, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_3.99, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_3.100, 6;
    %pushi/str "unknown_waitop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.102;
T_3.95 ;
    %fork t_17, S_0x55bbeae3ca20;
    %jmp t_16;
    .scope S_0x55bbeae3ca20;
t_17 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0x55bbeae3cc20_0, 0, 1;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 27, 6;
    %store/vec4 v0x55bbeae3cd20_0, 0, 1;
    %load/vec4 v0x55bbeae3cc20_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.103, 8;
    %load/vec4 v0x55bbeae3cd20_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.105, 8;
    %pushi/str "twait.i.store.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.106;
T_3.105 ;
    %pushi/str "twait.i.load.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.106 ;
    %jmp T_3.104;
T_3.103 ;
    %load/vec4 v0x55bbeae3cd20_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.107, 8;
    %pushi/str "twait.i.store.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.108;
T_3.107 ;
    %pushi/str "twait.i.load.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.108 ;
T_3.104 ;
    %end;
    .scope S_0x55bbeae3c840;
t_16 %join;
    %jmp T_3.102;
T_3.96 ;
    %fork t_19, S_0x55bbeae3ce00;
    %jmp t_18;
    .scope S_0x55bbeae3ce00;
t_19 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0x55bbeae3d000_0, 0, 1;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 27, 6;
    %store/vec4 v0x55bbeae3d0e0_0, 0, 1;
    %load/vec4 v0x55bbeae3d000_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.109, 8;
    %load/vec4 v0x55bbeae3d0e0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.111, 8;
    %pushi/str "twait.r.store.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.112;
T_3.111 ;
    %pushi/str "twait.r.load.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.112 ;
    %jmp T_3.110;
T_3.109 ;
    %load/vec4 v0x55bbeae3d0e0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.113, 8;
    %pushi/str "twait.r.store.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.114;
T_3.113 ;
    %pushi/str "twait.r.load.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.114 ;
T_3.110 ;
    %end;
    .scope S_0x55bbeae3c840;
t_18 %join;
    %jmp T_3.102;
T_3.97 ;
    %pushi/str "twait.r.tacp_cg";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.102;
T_3.98 ;
    %pushi/str "twait.r.rmtfence";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.102;
T_3.99 ;
    %pushi/str "tkill.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.102;
T_3.100 ;
    %fork t_21, S_0x55bbeae3d1c0;
    %jmp t_20;
    .scope S_0x55bbeae3d1c0;
t_21 ;
    %load/vec4 v0x55bbeae3d820_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0x55bbeae3d3a0_0, 0, 1;
    %load/vec4 v0x55bbeae3d3a0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.115, 8;
    %pushi/str "twait.mem";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.116;
T_3.115 ;
    %pushi/str "twait";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
T_3.116 ;
    %end;
    .scope S_0x55bbeae3c840;
t_20 %join;
    %jmp T_3.102;
T_3.102 ;
    %pop/vec4 1;
    %jmp T_3.94;
T_3.94 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55bbeae3adb0;
t_14 %join;
    %jmp T_3.22;
T_3.20 ;
    %pushi/str "ace_mem_high";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %jmp T_3.22;
T_3.22 ;
    %pop/vec4 1;
    %pushi/str "unknown";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55bbeae3adb0;
    %end;
S_0x55bbeae3af90 .scope autobegin, "$unm_blk_19" "$unm_blk_19" 3 230, 3 230 0, S_0x55bbeae3adb0;
 .timescale 0 0;
v0x55bbeae3b190_0 .var "dsttm", 0 0;
v0x55bbeae3b270_0 .var "srctm", 0 0;
S_0x55bbeae3b330 .scope autobegin, "$unm_blk_21" "$unm_blk_21" 3 253, 3 253 0, S_0x55bbeae3adb0;
 .timescale 0 0;
v0x55bbeae3b530_0 .var "second_tuop", 2 0;
S_0x55bbeae3b610 .scope autobegin, "$unm_blk_30" "$unm_blk_30" 3 286, 3 286 0, S_0x55bbeae3adb0;
 .timescale 0 0;
v0x55bbeae3bae0_0 .var "second_tuop", 2 0;
v0x55bbeae3bbe0_0 .var "third_tuop", 2 0;
S_0x55bbeae3b820 .scope autobegin, "$unm_blk_31" "$unm_blk_31" 3 290, 3 290 0, S_0x55bbeae3b610;
 .timescale 0 0;
v0x55bbeae3ba00_0 .var "offseten", 0 0;
S_0x55bbeae3bcc0 .scope autobegin, "$unm_blk_35" "$unm_blk_35" 3 306, 3 306 0, S_0x55bbeae3adb0;
 .timescale 0 0;
v0x55bbeae3c240_0 .var "fourth_tuop", 2 0;
v0x55bbeae3c340_0 .var "second_tuop", 2 0;
v0x55bbeae3c420_0 .var "third_tuop", 2 0;
S_0x55bbeae3bea0 .scope autobegin, "$unm_blk_36" "$unm_blk_36" 3 311, 3 311 0, S_0x55bbeae3bcc0;
 .timescale 0 0;
v0x55bbeae3c0a0_0 .var "dsttm", 0 0;
v0x55bbeae3c180_0 .var "srctm", 0 0;
S_0x55bbeae3c510 .scope autobegin, "$unm_blk_39" "$unm_blk_39" 3 333, 3 333 0, S_0x55bbeae3adb0;
 .timescale 0 0;
v0x55bbeae3c740_0 .var "rw", 1 0;
S_0x55bbeae3c840 .scope autobegin, "$unm_blk_40" "$unm_blk_40" 3 343, 3 343 0, S_0x55bbeae3adb0;
 .timescale 0 0;
v0x55bbeae3d480_0 .var "ctrluop", 2 0;
v0x55bbeae3d580_0 .var "waitop", 2 0;
S_0x55bbeae3ca20 .scope autobegin, "$unm_blk_43" "$unm_blk_43" 3 354, 3 354 0, S_0x55bbeae3c840;
 .timescale 0 0;
v0x55bbeae3cc20_0 .var "isShare", 0 0;
v0x55bbeae3cd20_0 .var "isStore", 0 0;
S_0x55bbeae3ce00 .scope autobegin, "$unm_blk_46" "$unm_blk_46" 3 369, 3 369 0, S_0x55bbeae3c840;
 .timescale 0 0;
v0x55bbeae3d000_0 .var "isShare", 0 0;
v0x55bbeae3d0e0_0 .var "isStore", 0 0;
S_0x55bbeae3d1c0 .scope autobegin, "$unm_blk_49" "$unm_blk_49" 3 387, 3 387 0, S_0x55bbeae3c840;
 .timescale 0 0;
v0x55bbeae3d3a0_0 .var "isMem", 0 0;
S_0x55bbeae3df40 .scope autofunction.str, "format_operands" "format_operands" 3 410, 3 410 0, S_0x55bbeadcd250;
 .timescale 0 0;
; Variable format_operands is string return value of scope S_0x55bbeae3df40
v0x55bbeae3fba0_0 .var/str "instr_name";
v0x55bbeae3fc60_0 .var "instruction_data", 127 0;
v0x55bbeae3fd20_0 .var "length", 1 0;
v0x55bbeae3fe00_0 .var/str "operands";
v0x55bbeae3ff10_0 .var "rs1", 4 0;
v0x55bbeae3fff0_0 .var "rs2", 4 0;
v0x55bbeae400d0_0 .var "td", 7 0;
TD_tile_decoder_pkg.format_operands ;
    %pushi/str "";
    %store/str v0x55bbeae3fe00_0;
    %load/vec4 v0x55bbeae3fd20_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.117, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.118, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.119, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.120, 6;
    %jmp T_4.121;
T_4.117 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.122, 8;
T_4.122 ;
    %jmp T_4.121;
T_4.118 ;
    %fork t_23, S_0x55bbeae3e120;
    %jmp t_22;
    .scope S_0x55bbeae3e120;
t_23 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55bbeae3e420_0, 0, 3;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55bbeae3e320_0, 0, 6;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55bbeae3e500_0, 0, 3;
    %load/vec4 v0x55bbeae3e420_0;
    %pushi/vec4 6, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55bbeae3e320_0;
    %pushi/vec4 1, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x55bbeae3e500_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.124, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x55bbeae400d0_0, 0, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55bbeae3ff10_0, 0, 5;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x55bbeae3fff0_0, 0, 5;
    %jmp T_4.125;
T_4.124 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tld";
    %cmp/str;
    %flag_get/vec4 4;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "trr";
    %cmp/str;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.126, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x55bbeae400d0_0, 0, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55bbeae3ff10_0, 0, 5;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x55bbeae3fff0_0, 0, 5;
    %jmp T_4.127;
T_4.126 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 32, 7;
    %store/vec4 v0x55bbeae400d0_0, 0, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 48, 7;
    %store/vec4 v0x55bbeae3ff10_0, 0, 5;
T_4.127 ;
T_4.125 ;
    %end;
    .scope S_0x55bbeae3df40;
t_22 %join;
    %jmp T_4.121;
T_4.119 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 48, 7;
    %store/vec4 v0x55bbeae400d0_0, 0, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 43, 7;
    %store/vec4 v0x55bbeae3ff10_0, 0, 5;
    %jmp T_4.121;
T_4.120 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55bbeae3ff10_0, 0, 5;
    %jmp T_4.121;
T_4.121 ;
    %pop/vec4 1;
    %load/vec4 v0x55bbeae3fd20_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.128, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.129, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.130, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.131, 6;
    %jmp T_4.132;
T_4.128 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.133, 8;
    %fork t_25, S_0x55bbeae3e5c0;
    %jmp t_24;
    .scope S_0x55bbeae3e5c0;
t_25 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x55bbeae3ea40_0, 0, 2;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 9, 20, 6;
    %store/vec4 v0x55bbeae3e7c0_0, 0, 9;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 7, 4;
    %store/vec4 v0x55bbeae3e8a0_0, 0, 5;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55bbeae3e980_0, 0, 5;
    %load/vec4 v0x55bbeae3ea40_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.135, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.136, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.137, 6;
    %pushi/str "unknown";
    %store/str v0x55bbeae3fe00_0;
    %jmp T_4.139;
T_4.135 ;
    %vpi_call/w 3 479 "$sformat", v0x55bbeae3fe00_0, "0x%0x", v0x55bbeae3e980_0 {0 0 0};
    %jmp T_4.139;
T_4.136 ;
    %vpi_call/w 3 482 "$sformat", v0x55bbeae3fe00_0, "x%0d", v0x55bbeae3e8a0_0 {0 0 0};
    %jmp T_4.139;
T_4.137 ;
    %vpi_call/w 3 485 "$sformat", v0x55bbeae3fe00_0, "x%0d", v0x55bbeae3e980_0 {0 0 0};
    %jmp T_4.139;
T_4.139 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55bbeae3df40;
t_24 %join;
    %jmp T_4.134;
T_4.133 ;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.mem";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.140, 9;
    %pushi/str "";
    %store/str v0x55bbeae3fe00_0;
    %jmp T_4.141;
T_4.140 ;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.i.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.i.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.i.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.i.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.142, 9;
    %fork t_27, S_0x55bbeae3eb20;
    %jmp t_26;
    .scope S_0x55bbeae3eb20;
t_27 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 15, 5;
    %store/vec4 v0x55bbeae3ed30_0, 0, 8;
    %vpi_call/w 3 496 "$sformat", v0x55bbeae3fe00_0, "%0d", v0x55bbeae3ed30_0 {0 0 0};
    %end;
    .scope S_0x55bbeae3df40;
t_26 %join;
    %jmp T_4.143;
T_4.142 ;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.r.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.r.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.r.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.r.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.r.tacp_cg";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "twait.r.rmtfence";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.144, 9;
    %fork t_29, S_0x55bbeae3ee10;
    %jmp t_28;
    .scope S_0x55bbeae3ee10;
t_29 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55bbeae3eff0_0, 0, 5;
    %vpi_call/w 3 502 "$sformat", v0x55bbeae3fe00_0, "x%0d", v0x55bbeae3eff0_0 {0 0 0};
    %end;
    .scope S_0x55bbeae3df40;
t_28 %join;
    %jmp T_4.145;
T_4.144 ;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "tsync.i";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.146, 8;
    %fork t_31, S_0x55bbeae3f0f0;
    %jmp t_30;
    .scope S_0x55bbeae3f0f0;
t_31 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55bbeae3f320_0, 0, 5;
    %vpi_call/w 3 506 "$sformat", v0x55bbeae3fe00_0, "%0d", v0x55bbeae3f320_0 {0 0 0};
    %end;
    .scope S_0x55bbeae3df40;
t_30 %join;
    %jmp T_4.147;
T_4.146 ;
    %load/str v0x55bbeae3fba0_0;
    %pushi/str "tkill.r";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.148, 8;
    %fork t_33, S_0x55bbeae3f420;
    %jmp t_32;
    .scope S_0x55bbeae3f420;
t_33 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55bbeae3f600_0, 0, 5;
    %vpi_call/w 3 510 "$sformat", v0x55bbeae3fe00_0, "x%0d", v0x55bbeae3f600_0 {0 0 0};
    %end;
    .scope S_0x55bbeae3df40;
t_32 %join;
    %jmp T_4.149;
T_4.148 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "ace";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.150, 8;
    %pushi/str "0, 0, 0, 0, 0";
    %store/str v0x55bbeae3fe00_0;
    %jmp T_4.151;
T_4.150 ;
    %pushi/str "0";
    %store/str v0x55bbeae3fe00_0;
T_4.151 ;
T_4.149 ;
T_4.147 ;
T_4.145 ;
T_4.143 ;
T_4.141 ;
T_4.134 ;
    %jmp T_4.132;
T_4.129 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 2, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tld";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 2, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tst";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.152, 9;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "trr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.154, 8;
    %vpi_call/w 3 524 "$sformat", v0x55bbeae3fe00_0, "t%0d, (x%0d), x%0d", v0x55bbeae400d0_0, v0x55bbeae3ff10_0, v0x55bbeae3fff0_0 {0 0 0};
    %jmp T_4.155;
T_4.154 ;
    %vpi_call/w 3 527 "$sformat", v0x55bbeae3fe00_0, "t%0d, (x%0d)", v0x55bbeae400d0_0, v0x55bbeae3ff10_0 {0 0 0};
T_4.155 ;
    %jmp T_4.153;
T_4.152 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tmma";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.156, 8;
    %fork t_35, S_0x55bbeae3f700;
    %jmp t_34;
    .scope S_0x55bbeae3f700;
t_35 ;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 40, 7;
    %store/vec4 v0x55bbeae3f8e0_0, 0, 8;
    %load/vec4 v0x55bbeae3fc60_0;
    %parti/s 8, 48, 7;
    %store/vec4 v0x55bbeae3f9e0_0, 0, 8;
    %vpi_call/w 3 534 "$sformat", v0x55bbeae3fe00_0, "t%0d, t%0d, t%0d", v0x55bbeae400d0_0, v0x55bbeae3f8e0_0, v0x55bbeae3f9e0_0 {0 0 0};
    %end;
    .scope S_0x55bbeae3df40;
t_34 %join;
    %jmp T_4.157;
T_4.156 ;
    %vpi_call/w 3 537 "$sformat", v0x55bbeae3fe00_0, "t%0d, (x%0d)", v0x55bbeae400d0_0, v0x55bbeae3ff10_0 {0 0 0};
T_4.157 ;
T_4.153 ;
    %jmp T_4.132;
T_4.130 ;
    %vpi_call/w 3 543 "$sformat", v0x55bbeae3fe00_0, "t%0d, (x%0d)", v0x55bbeae400d0_0, v0x55bbeae3ff10_0 {0 0 0};
    %jmp T_4.132;
T_4.131 ;
    %load/str v0x55bbeae3fba0_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tacp";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.158, 8;
    %vpi_call/w 3 549 "$sformat", v0x55bbeae3fe00_0, "0, 0, x%0d, 0, 0, 0, 0", v0x55bbeae3ff10_0 {0 0 0};
    %jmp T_4.159;
T_4.158 ;
    %pushi/str "0, 0, 0, 0";
    %store/str v0x55bbeae3fe00_0;
T_4.159 ;
    %jmp T_4.132;
T_4.132 ;
    %pop/vec4 1;
    %load/str v0x55bbeae3fe00_0;
    %ret/str 0; Assign to format_operands
    %disable S_0x55bbeae3df40;
    %end;
S_0x55bbeae3e120 .scope autobegin, "$unm_blk_51" "$unm_blk_51" 3 429, 3 429 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3e320_0 .var "memuop_field", 5 0;
v0x55bbeae3e420_0 .var "tuop_first", 2 0;
v0x55bbeae3e500_0 .var "tuop_second", 2 0;
S_0x55bbeae3e5c0 .scope autobegin, "$unm_blk_58" "$unm_blk_58" 3 470, 3 470 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3e7c0_0 .var "csr_addr", 8 0;
v0x55bbeae3e8a0_0 .var "rd", 4 0;
v0x55bbeae3e980_0 .var "rs1_or_imm", 4 0;
v0x55bbeae3ea40_0 .var "rw", 1 0;
S_0x55bbeae3eb20 .scope autobegin, "$unm_blk_63" "$unm_blk_63" 3 493, 3 493 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3ed30_0 .var "cnt", 7 0;
S_0x55bbeae3ee10 .scope autobegin, "$unm_blk_64" "$unm_blk_64" 3 499, 3 499 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3eff0_0 .var "rs1", 4 0;
S_0x55bbeae3f0f0 .scope autobegin, "$unm_blk_65" "$unm_blk_65" 3 503, 3 503 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3f320_0 .var "sync_id", 4 0;
S_0x55bbeae3f420 .scope autobegin, "$unm_blk_66" "$unm_blk_66" 3 507, 3 507 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3f600_0 .var "rs1", 4 0;
S_0x55bbeae3f700 .scope autobegin, "$unm_blk_73" "$unm_blk_73" 3 529, 3 529 0, S_0x55bbeae3df40;
 .timescale 0 0;
v0x55bbeae3f8e0_0 .var "ts1", 7 0;
v0x55bbeae3f9e0_0 .var "ts2", 7 0;
S_0x55bbeae401b0 .scope autofunction.vec2.u32, "get_instruction_bits" "get_instruction_bits" 3 163, 3 163 0, S_0x55bbeadcd250;
 .timescale 0 0;
; Variable get_instruction_bits is bool return value of scope S_0x55bbeae401b0
v0x55bbeae40490_0 .var "length", 1 0;
TD_tile_decoder_pkg.get_instruction_bits ;
    %load/vec4 v0x55bbeae40490_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_5.160, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_5.161, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_5.162, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_5.163, 6;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55bbeae401b0;
    %jmp T_5.165;
T_5.160 ;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55bbeae401b0;
    %jmp T_5.165;
T_5.161 ;
    %pushi/vec4 64, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55bbeae401b0;
    %jmp T_5.165;
T_5.162 ;
    %pushi/vec4 96, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55bbeae401b0;
    %jmp T_5.165;
T_5.163 ;
    %pushi/vec4 128, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55bbeae401b0;
    %jmp T_5.165;
T_5.165 ;
    %pop/vec4 1;
    %end;
S_0x55bbeae40570 .scope autofunction.vec4.s2, "get_instruction_length" "get_instruction_length" 3 40, 3 40 0, S_0x55bbeadcd250;
 .timescale 0 0;
v0x55bbeae40750_0 .var "ace_op", 6 0;
v0x55bbeae40850_0 .var "first_word", 31 0;
; Variable get_instruction_length is vec4 return value of scope S_0x55bbeae40570
v0x55bbeae409f0_0 .var "lsuop", 1 0;
v0x55bbeae40ad0_0 .var "memuop", 5 0;
v0x55bbeae40c00_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.get_instruction_length ;
    %alloc S_0x55bbeae3aa10;
    %load/vec4 v0x55bbeae40850_0;
    %store/vec4 v0x55bbeae3acd0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x55bbeae3aa10;
    %free S_0x55bbeae3aa10;
    %store/vec4 v0x55bbeae40750_0, 0, 7;
    %load/vec4 v0x55bbeae40750_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_6.166, 4;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
T_6.166 ;
    %load/vec4 v0x55bbeae40850_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55bbeae40c00_0, 0, 3;
    %load/vec4 v0x55bbeae40c00_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_6.168, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_6.169, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_6.170, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_6.171, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_6.172, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_6.173, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_6.174, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_6.175, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.168 ;
    %load/vec4 v0x55bbeae40850_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55bbeae40ad0_0, 0, 6;
    %load/vec4 v0x55bbeae40850_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55bbeae409f0_0, 0, 2;
    %load/vec4 v0x55bbeae409f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.178, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.179, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.180, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.181, 6;
    %jmp T_6.182;
T_6.178 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.182;
T_6.179 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.182;
T_6.180 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.182;
T_6.181 ;
    %load/vec4 v0x55bbeae40ad0_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.183, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.184;
T_6.183 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
T_6.184 ;
    %jmp T_6.182;
T_6.182 ;
    %pop/vec4 1;
    %jmp T_6.177;
T_6.169 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.170 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.171 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.172 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.173 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.174 ;
    %load/vec4 v0x55bbeae40850_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55bbeae409f0_0, 0, 2;
    %load/vec4 v0x55bbeae40850_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55bbeae40ad0_0, 0, 6;
    %load/vec4 v0x55bbeae40ad0_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_6.185, 4;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.186;
T_6.185 ;
    %load/vec4 v0x55bbeae40ad0_0;
    %cmpi/e 0, 0, 6;
    %jmp/0xz  T_6.187, 4;
    %load/vec4 v0x55bbeae409f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.189, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.190, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.191, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.192, 6;
    %jmp T_6.193;
T_6.189 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.193;
T_6.190 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.193;
T_6.191 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.193;
T_6.192 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.193;
T_6.193 ;
    %pop/vec4 1;
    %jmp T_6.188;
T_6.187 ;
    %load/vec4 v0x55bbeae40ad0_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.194, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.195;
T_6.194 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
T_6.195 ;
T_6.188 ;
T_6.186 ;
    %jmp T_6.177;
T_6.175 ;
    %load/vec4 v0x55bbeae40850_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55bbeae40ad0_0, 0, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55bbeae40570;
    %jmp T_6.177;
T_6.177 ;
    %pop/vec4 1;
    %end;
S_0x55bbeae40ce0 .scope autofunction.vec4.s134, "init_collector" "init_collector" 3 117, 3 117 0, S_0x55bbeadcd250;
 .timescale 0 0;
v0x55bbeae40ec0_0 .var "collector", 133 0;
v0x55bbeae40fc0_0 .var "first_word", 31 0;
; Variable init_collector is vec4 return value of scope S_0x55bbeae40ce0
TD_tile_decoder_pkg.init_collector ;
    %pushi/vec4 0, 0, 128;
    %ix/load 4, 6, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae40ec0_0, 4, 128;
    %load/vec4 v0x55bbeae40fc0_0;
    %ix/load 4, 6, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae40ec0_0, 4, 32;
    %pushi/vec4 1, 0, 2;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae40ec0_0, 4, 2;
    %alloc S_0x55bbeae40570;
    %load/vec4 v0x55bbeae40fc0_0;
    %store/vec4 v0x55bbeae40850_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.get_instruction_length, S_0x55bbeae40570;
    %free S_0x55bbeae40570;
    %ix/load 4, 2, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae40ec0_0, 4, 2;
    %alloc S_0x55bbeae41160;
    %load/vec4 v0x55bbeae40fc0_0;
    %store/vec4 v0x55bbeae414d0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55bbeae41160;
    %free S_0x55bbeae41160;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae40ec0_0, 4, 1;
    %load/vec4 v0x55bbeae40ec0_0;
    %parti/u 2, 2, 32;
    %pushi/vec4 0, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55bbeae40ec0_0, 4, 1;
    %load/vec4 v0x55bbeae40ec0_0;
    %ret/vec4 0, 0, 134;  Assign to init_collector (store_vec4_to_lval)
    %disable S_0x55bbeae40ce0;
    %end;
S_0x55bbeae41160 .scope autofunction.vec4.s1, "is_tile_instruction" "is_tile_instruction" 3 33, 3 33 0, S_0x55bbeadcd250;
 .timescale 0 0;
v0x55bbeae413d0_0 .var "ace_op", 6 0;
v0x55bbeae414d0_0 .var "first_word", 31 0;
; Variable is_tile_instruction is vec4 return value of scope S_0x55bbeae41160
TD_tile_decoder_pkg.is_tile_instruction ;
    %alloc S_0x55bbeae3aa10;
    %load/vec4 v0x55bbeae414d0_0;
    %store/vec4 v0x55bbeae3acd0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x55bbeae3aa10;
    %free S_0x55bbeae3aa10;
    %store/vec4 v0x55bbeae413d0_0, 0, 7;
    %load/vec4 v0x55bbeae413d0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %ret/vec4 0, 0, 1;  Assign to is_tile_instruction (store_vec4_to_lval)
    %disable S_0x55bbeae41160;
    %end;
S_0x55bbeadd26d0 .scope module, "original_test" "original_test" 4 4;
 .timescale 0 0;
S_0x55bbeae41680 .scope begin, "$unm_blk_84" "$unm_blk_84" 4 6, 4 6 0, S_0x55bbeadd26d0;
 .timescale 0 0;
v0x55bbeae41830_0 .var "instruction_data", 127 0;
v0x55bbeae41930_0 .var/str "result";
v0x55bbeae419f0_0 .var "test_instruction", 31 0;
S_0x55bbeadd2860 .scope module, "tile_instruction_decoder_example" "tile_instruction_decoder_example" 3 583;
 .timescale 0 0;
S_0x55bbeae41ae0 .scope begin, "$unm_blk_79" "$unm_blk_79" 3 586, 3 586 0, S_0x55bbeadd2860;
 .timescale 0 0;
v0x55bbeae41ce0_0 .var "collector", 133 0;
v0x55bbeae41de0_0 .var/str "disasm_result";
v0x55bbeae41ea0_0 .var "test_word", 31 0;
    .scope S_0x55bbeadd26d0;
T_9 ;
    %fork t_37, S_0x55bbeae41680;
    %jmp t_36;
    .scope S_0x55bbeae41680;
t_37 ;
    %vpi_call/w 4 11 "$display", "=== Original functionality test ===" {0 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x55bbeae419f0_0, 0, 32;
    %pushi/vec4 0, 0, 96;
    %load/vec4 v0x55bbeae419f0_0;
    %concat/vec4; draw_concat_vec4
    %store/vec4 v0x55bbeae41830_0, 0, 128;
    %vpi_call/w 4 17 "$display", "Testing CSR instruction: 0x%08x", v0x55bbeae419f0_0 {0 0 0};
    %alloc S_0x55bbeae41160;
    %load/vec4 v0x55bbeae419f0_0;
    %store/vec4 v0x55bbeae414d0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55bbeae41160;
    %free S_0x55bbeae41160;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.0, 8;
    %vpi_call/w 4 20 "$display", "\342\234\223 Recognized as tile instruction" {0 0 0};
    %alloc S_0x55bbeae3adb0;
    %load/vec4 v0x55bbeae41830_0;
    %pushi/vec4 0, 0, 2;
    %store/vec4 v0x55bbeae3d910_0, 0, 2;
    %store/vec4 v0x55bbeae3d820_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.extract_instruction_name, S_0x55bbeae3adb0;
    %free S_0x55bbeae3adb0;
    %store/str v0x55bbeae41930_0;
    %vpi_call/w 4 23 "$display", "\342\234\223 CSR instruction result: %s", v0x55bbeae41930_0 {0 0 0};
    %jmp T_9.1;
T_9.0 ;
    %vpi_call/w 4 25 "$display", "\342\234\227 Not recognized as tile instruction" {0 0 0};
T_9.1 ;
    %vpi_call/w 4 28 "$display", "=== Test Complete ===" {0 0 0};
    %end;
    .scope S_0x55bbeadd26d0;
t_36 %join;
    %end;
    .thread T_9;
    .scope S_0x55bbeadd2860;
T_10 ;
    %fork t_39, S_0x55bbeae41ae0;
    %jmp t_38;
    .scope S_0x55bbeae41ae0;
t_39 ;
    %vpi_call/w 3 591 "$display", "=== Tile Instruction Decoder Example ===" {0 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x55bbeae41ea0_0, 0, 32;
    %vpi_call/w 3 595 "$display", "\012Testing 32-bit instruction: 0x%08x", v0x55bbeae41ea0_0 {0 0 0};
    %alloc S_0x55bbeae41160;
    %load/vec4 v0x55bbeae41ea0_0;
    %store/vec4 v0x55bbeae414d0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55bbeae41160;
    %free S_0x55bbeae41160;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.0, 8;
    %alloc S_0x55bbeae40ce0;
    %load/vec4 v0x55bbeae41ea0_0;
    %store/vec4 v0x55bbeae40fc0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55bbeae40ce0;
    %free S_0x55bbeae40ce0;
    %store/vec4 v0x55bbeae41ce0_0, 0, 134;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.3, 8;
T_10.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.3, 8;
 ; End of false expr.
    %blend;
T_10.3;
    %vpi_call/w 3 599 "$display", "  Is tile: YES, Length: %0d, Complete: %s", &PV<v0x55bbeae41ce0_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.4, 8;
    %alloc S_0x55bbeae3a320;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55bbeae3a760_0, 0, 2;
    %store/vec4 v0x55bbeae3a6a0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55bbeae3a320;
    %free S_0x55bbeae3a320;
    %store/str v0x55bbeae41de0_0;
    %vpi_call/w 3 606 "$display", "  Disassembly: %s", v0x55bbeae41de0_0 {0 0 0};
T_10.4 ;
T_10.0 ;
    %vpi_call/w 3 611 "$display", "\012Testing 64-bit instruction:" {0 0 0};
    %pushi/vec4 24699, 0, 32;
    %store/vec4 v0x55bbeae41ea0_0, 0, 32;
    %vpi_call/w 3 613 "$display", "  Word 1: 0x%08x", v0x55bbeae41ea0_0 {0 0 0};
    %alloc S_0x55bbeae40ce0;
    %load/vec4 v0x55bbeae41ea0_0;
    %store/vec4 v0x55bbeae40fc0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55bbeae40ce0;
    %free S_0x55bbeae40ce0;
    %store/vec4 v0x55bbeae41ce0_0, 0, 134;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.6, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.7, 8;
T_10.6 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.7, 8;
 ; End of false expr.
    %blend;
T_10.7;
    %vpi_call/w 3 616 "$display", "  Expected length: %0d, Complete: %s", &PV<v0x55bbeae41ce0_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.8, 8;
    %pushi/vec4 2147483771, 0, 32;
    %store/vec4 v0x55bbeae41ea0_0, 0, 32;
    %vpi_call/w 3 622 "$display", "  Word 2: 0x%08x", v0x55bbeae41ea0_0 {0 0 0};
    %alloc S_0x55bbeadd29f0;
    %load/vec4 v0x55bbeae41ce0_0;
    %load/vec4 v0x55bbeae41ea0_0;
    %store/vec4 v0x55bbeae3a240_0, 0, 32;
    %store/vec4 v0x55bbeae3a0a0_0, 0, 134;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55bbeadd29f0;
    %free S_0x55bbeadd29f0;
    %store/vec4 v0x55bbeae41ce0_0, 0, 134;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.11, 8;
T_10.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.11, 8;
 ; End of false expr.
    %blend;
T_10.11;
    %vpi_call/w 3 624 "$display", "  Complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.12, 8;
    %alloc S_0x55bbeae3a320;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55bbeae41ce0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55bbeae3a760_0, 0, 2;
    %store/vec4 v0x55bbeae3a6a0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55bbeae3a320;
    %free S_0x55bbeae3a320;
    %store/str v0x55bbeae41de0_0;
    %vpi_call/w 3 629 "$display", "  Disassembly: %s", v0x55bbeae41de0_0 {0 0 0};
T_10.12 ;
T_10.8 ;
    %vpi_call/w 3 633 "$display", "\012=== Example Complete ===" {0 0 0};
    %end;
    .scope S_0x55bbeadd2860;
t_38 %join;
    %end;
    .thread T_10;
# The file index is used to find the file name in the following table.
:file_names 5;
    "N/A";
    "<interactive>";
    "-";
    "tile_instruction_decoder.sv";
    "original_test.sv";
