#!/usr/bin/env python3
"""
基于tadd.ttr.md文档格式重新分析指令 0x027b1009e4fb
"""

def final_analysis():
    instr_val = 0x027b1009e4fb
    
    print("=== 基于tadd.ttr.md格式的最终分析 ===")
    print(f"指令编码: 0x{instr_val:016x}")
    print(f"期望结果: tadd.ttr.f32.neg2 T0,T0,s3")
    print()
    
    # 根据tadd.ttr.md中的wavedrom格式
    # 第一个32位word (低位):
    # bits 7   'ACE_op'    '1111011'
    # bits 2   ''          '01' 
    # bits 2   'vecuop1'   '10'
    # bits 1   'tmask'     ''
    # bits 3   'tuop'      '110'  
    # bits 5   'rs2'       ''
    # bits 5   ''          '00000'
    # bits 1   'reuse1'    ''
    # bits 1   ''          '0'
    # bits 1   'neg1'      ''
    # bits 1   'neg2'      ''
    # bits 2   'vecuop2[1:0]' ''
    # bits 1   ''          '0'
    
    # 第二个32位word (高位):
    # bits 7   'ACE_op'    '1111011'
    # bits 1   ''          '0' 
    # bits 4   'vecuop2[5:2]' '0000'
    # bits 3   'tuop'      '010'
    # bits 8   'Ts1'       ''
    # bits 8   'Td'        ''
    # bits 1   ''          '0'
    
    # 重新按照这个格式分析
    word1 = instr_val & 0xFFFFFFFF      # 0x1009e4fb
    word2 = (instr_val >> 32) & 0xFFFFFFFF  # 0x0000027b
    
    print(f"第一word: 0x{word1:08x}")
    print(f"第二word: 0x{word2:08x}")
    print()
    
    # 第一word的字段 (按bit位置从低到高)
    print("=== 第一word字段分析 ===")
    ace_op1 = word1 & 0x7F                   # [6:0]
    rsen_immen = (word1 >> 7) & 0x3          # [8:7] - 注意这里可能是'01'
    vecuop1 = (word1 >> 9) & 0x3             # [10:9] - 应该是'10'
    tmask = (word1 >> 11) & 0x1              # [11]
    tuop1 = (word1 >> 12) & 0x7              # [14:12] - 应该是'110'
    rs2 = (word1 >> 15) & 0x1F               # [19:15]
    zero_field = (word1 >> 20) & 0x1F        # [24:20] - 应该是'00000'
    reuse1 = (word1 >> 25) & 0x1            # [25]
    zero_bit = (word1 >> 26) & 0x1          # [26] - 应该是'0' 
    neg1 = (word1 >> 27) & 0x1              # [27]
    neg2 = (word1 >> 28) & 0x1              # [28]
    vecuop2_10 = (word1 >> 29) & 0x3        # [30:29]
    zero_bit_31 = (word1 >> 31) & 0x1       # [31] - 应该是'0'
    
    print(f"ACE_OP[6:0]:      {ace_op1:07b} (0x{ace_op1:02x})")
    print(f"bits[8:7]:        {rsen_immen:02b} ({rsen_immen})")  
    print(f"vecuop1[10:9]:    {vecuop1:02b} ({vecuop1})")
    print(f"tmask[11]:        {tmask}")
    print(f"tuop[14:12]:      {tuop1:03b} ({tuop1})")
    print(f"rs2[19:15]:       {rs2} (r{rs2})")
    print(f"zero[24:20]:      {zero_field}")
    print(f"reuse1[25]:       {reuse1}")
    print(f"zero[26]:         {zero_bit}")
    print(f"neg1[27]:         {neg1}")
    print(f"neg2[28]:         {neg2}")
    print(f"vecuop2[1:0]:     {vecuop2_10:02b} ({vecuop2_10})")
    print(f"zero[31]:         {zero_bit_31}")
    print()
    
    # 第二word的字段
    print("=== 第二word字段分析 ===")
    ace_op2 = word2 & 0x7F                  # [6:0]
    zero2 = (word2 >> 7) & 0x1              # [7] - 应该是'0'
    vecuop2_52 = (word2 >> 8) & 0xF         # [11:8]
    tuop2 = (word2 >> 12) & 0x7             # [14:12] - 应该是'010'
    ts1 = (word2 >> 15) & 0xFF              # [22:15] - 但word2只有16位有效
    td = (word2 >> 23) & 0xFF               # [30:23] - 但word2只有16位有效
    zero3 = (word2 >> 31) & 0x1             # [31]
    
    # 由于word2实际只有16位，我们需要重新解析
    # word2 = 0x027b = 0000 0010 0111 1011
    # [6:0] = 1111011 = 0x7B (ace_op)
    # [7] = 0
    # [11:8] = 0010 = 2 (vecuop2[5:2])  
    # [14:12] = 000 = 0 (tuop)
    # [15] = 0
    
    print(f"ACE_OP[6:0]:      {ace_op2:07b} (0x{ace_op2:02x})")
    print(f"zero[7]:          {zero2}")
    print(f"vecuop2[5:2]:     {vecuop2_52:04b} ({vecuop2_52})")
    print(f"tuop[14:12]:      {tuop2:03b} ({tuop2})")
    print()
    
    # 但这里有个问题 - 第二word的tuop应该是'010'(2), 但我们得到的是'000'(0)
    # 让我重新检查bit位置
    # 也许vecuop2[5:2]的位置不对
    
    # 重新按照文档中64位指令的实际位置分析
    print("=== 重新分析 - 考虑实际的bit位置 ===")
    
    # 我们知道这是一个64位指令，需要正确理解位的分布
    # 第二word中的tuop应该在[46:44]位置（相对于整个64位）
    # 也就是在第二word的[14:12]位置
    
    # 让我直接验证期望的模式
    expected_ace_op = 0x7B
    expected_rsen_bit = 1  # bit 8, 表示第二操作数是register  
    expected_vecuop1 = 2   # '10' 表示2-operand ALU
    expected_tuop1 = 6     # '110' 第一word的tuop
    expected_tuop2 = 2     # '010' 第二word的tuop  
    expected_vecuop2_52 = 0 # '0000' 表示tadd
    expected_neg2 = 1      # neg2修饰符
    
    # 验证我们的解析是否正确
    print("=== 验证期望模式 ===")
    print(f"ACE_OP == 0x7B: {ace_op1 == expected_ace_op}")
    print(f"第一word TUOP == 6: {tuop1 == expected_tuop1}")  
    
    # 重新检查rsen位置 - 可能在bit 8
    actual_rsen = (word1 >> 8) & 0x1
    print(f"rsen[8] == 1: {actual_rsen == 1}")
    print(f"vecuop1 == 2: {vecuop1 == 2}")
    print(f"neg2 == 1: {neg2 == expected_neg2}")
    
    # 对于第二word，我们需要从完整的64位指令中提取
    # 第二word的tuop在整体的[46:44]位置
    actual_tuop2 = (instr_val >> 44) & 0x7
    actual_vecuop2_52 = (instr_val >> 40) & 0xF
    actual_ts1 = (instr_val >> 48) & 0xFF  
    actual_td = (instr_val >> 56) & 0xFF
    
    print()
    print(f"第二word TUOP[46:44] == 2: {actual_tuop2 == expected_tuop2}")
    print(f"vecuop2[5:2] == 0: {actual_vecuop2_52 == expected_vecuop2_52}")
    print(f"Ts1: {actual_ts1}")
    print(f"Td: {actual_td}")
    
    print()
    print("=== 最终结论 ===")
    all_correct = (ace_op1 == expected_ace_op and
                   tuop1 == expected_tuop1 and
                   actual_rsen == 1 and
                   neg2 == expected_neg2 and
                   actual_tuop2 == expected_tuop2 and
                   actual_vecuop2_52 == expected_vecuop2_52)
    
    if all_correct:
        print("✓ 完全匹配tadd.ttr.f32.neg2模式!")
        print(f"✓ 完整指令: tadd.ttr.f32.neg2 T{actual_td}, T{actual_ts1}, r{rs2}")
    else:
        print("✗ 仍有部分字段不匹配")
        print("需要进一步调试指令格式")
    
    return {
        'ace_op1': ace_op1,
        'tuop1': tuop1, 
        'rsen': actual_rsen,
        'vecuop1': vecuop1,
        'neg2': neg2,
        'rs2': rs2,
        'tuop2': actual_tuop2,
        'vecuop2_52': actual_vecuop2_52,
        'ts1': actual_ts1,
        'td': actual_td
    }

if __name__ == "__main__":
    result = final_analysis()
