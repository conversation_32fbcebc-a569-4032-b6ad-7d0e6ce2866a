#!/usr/bin/env python3
"""
正确分析指令 0x027b1009e4fb (tadd.ttr.f32.neg2)
基于对64位tile指令格式的正确理解
"""

def analyze_instruction_correctly():
    # 指令编码: 0x027b1009e4fb
    # 应该识别为: tadd.ttr.f32.neg2 T0,T0,s3
    
    instr_val = 0x027b1009e4fb
    
    print("=== 正确分析指令 0x027b1009e4fb ===")
    print(f"指令编码: 0x{instr_val:016x}")
    print(f"期望结果: tadd.ttr.f32.neg2 T0,T0,s3")
    print()
    
    # 重新理解字段布局
    # 在64位指令中，可能需要从整个64位中提取字段，而不是分两个32位word
    
    # 第一种理解：作为完整的64位数据
    print("=== 作为64位数据分析 ===")
    ace_op = instr_val & 0x7F
    print(f"ACE_OP[6:0]: {ace_op:07b} (0x{ace_op:02x}) = {ace_op}")
    
    # 检查是否是tile指令
    if ace_op == 0x7B:
        print("✓ 确认是Tile指令")
        
        # 根据tadd.ttr的文档格式重新分析
        # 参考encode/tuop_010/vecuop1_10/rsen_1/vecuop2[5_2]_0000/tadd.ttr.md
        
        # 提取关键字段
        rsen = (instr_val >> 8) & 0x1          # bit 8
        immen = (instr_val >> 9) & 0x1         # bit 9  
        vecuop1 = (instr_val >> 10) & 0x3      # bits [11:10]
        tuop_first = (instr_val >> 12) & 0x7   # bits [14:12] 第一word的tuop
        rs2 = (instr_val >> 15) & 0x1F         # bits [19:15]
        rs3 = (instr_val >> 20) & 0x1F         # bits [24:20] - 在ttr中应该是00000
        reuse1 = (instr_val >> 25) & 0x1      # bit 25
        neg1 = (instr_val >> 27) & 0x1        # bit 27
        neg2 = (instr_val >> 28) & 0x1        # bit 28
        vecuop2_10 = (instr_val >> 29) & 0x3  # bits [30:29]
        
        # 第二word字段 (从bit 32开始)
        ace_op2 = (instr_val >> 32) & 0x7F     # bits [38:32]
        vecuop2_52 = (instr_val >> 40) & 0xF   # bits [43:40] - vecuop2[5:2]
        tuop_second = (instr_val >> 44) & 0x7  # bits [46:44] 第二word的tuop
        ts1 = (instr_val >> 48) & 0xFF         # bits [55:48]
        td = (instr_val >> 56) & 0xFF          # bits [63:56]
        
        print()
        print("=== 字段分析结果 ===")
        print(f"rsen[8]:           {rsen}")
        print(f"immen[9]:          {immen}")
        print(f"vecuop1[11:10]:    {vecuop1:02b} ({vecuop1})")
        print(f"tuop_first[14:12]: {tuop_first:03b} ({tuop_first})")
        print(f"rs2[19:15]:        {rs2} (r{rs2})")
        print(f"rs3[24:20]:        {rs3}")
        print(f"reuse1[25]:        {reuse1}")
        print(f"neg1[27]:          {neg1}")
        print(f"neg2[28]:          {neg2}")
        print(f"vecuop2[1:0]:      {vecuop2_10:02b} ({vecuop2_10})")
        print()
        print(f"ace_op2[38:32]:    {ace_op2:07b} (0x{ace_op2:02x})")
        print(f"vecuop2[5:2]:      {vecuop2_52:04b} ({vecuop2_52})")
        print(f"tuop_second[46:44]: {tuop_second:03b} ({tuop_second})")
        print(f"ts1[55:48]:        {ts1} (T{ts1})")
        print(f"td[63:56]:         {td} (T{td})")
        print()
        
        # 检查是否匹配tadd.ttr模式
        print("=== 指令模式匹配检查 ===")
        is_correct_ace = (ace_op == 0x7B and ace_op2 == 0x7B)
        is_correct_tuop = (tuop_first == 6 and tuop_second == 2)  # 110 + 010
        is_correct_vecuop1 = (vecuop1 == 2)  # 10 for 2-operand ALU
        is_correct_vecuop2 = (vecuop2_52 == 0)  # 0000 for tadd
        is_ttr_mode = (rsen == 1 and immen == 0)  # tile-tile-register
        
        print(f"ACE_OP正确 (0x7B+0x7B): {is_correct_ace}")
        print(f"TUOP正确 (110+010):     {is_correct_tuop}")
        print(f"VECUOP1正确 (10):       {is_correct_vecuop1}")
        print(f"VECUOP2[5:2]正确 (0000): {is_correct_vecuop2}")
        print(f"TTR模式 (rsen=1,immen=0): {is_ttr_mode}")
        print(f"有neg2修饰符:           {neg2 == 1}")
        print()
        
        if (is_correct_ace and is_correct_tuop and is_correct_vecuop1 
            and is_correct_vecuop2 and is_ttr_mode):
            print("✓ 完全匹配 tadd.ttr.f32 模式!")
            
            # 构造指令名
            instr_name = "tadd.ttr.f32"
            if neg2:
                instr_name += ".neg2"
            
            print(f"✓ 指令名称: {instr_name}")
            print(f"✓ 操作数: T{td}, T{ts1}, r{rs2}")
            print(f"✓ 完整指令: {instr_name} T{td}, T{ts1}, r{rs2}")
            
        else:
            print("✗ 不完全匹配，需要进一步检查")
        
        return {
            'ace_op': ace_op,
            'ace_op2': ace_op2,
            'tuop_first': tuop_first,
            'tuop_second': tuop_second,
            'vecuop1': vecuop1,
            'vecuop2_52': vecuop2_52,
            'rsen': rsen,
            'immen': immen,
            'neg2': neg2,
            'td': td,
            'ts1': ts1,
            'rs2': rs2
        }
    
    else:
        print(f"✗ ACE_OP = 0x{ace_op:02x}, 不是tile指令")
        return None

if __name__ == "__main__":
    result = analyze_instruction_correctly()
    
    if result:
        print()
        print("=== SystemVerilog修复方案 ===")
        print("需要在extract_instruction_name函数的tuop_110分支中添加:")
        print()
        print("else if (length == INSTR_64BIT) begin")
        print("    // Check for vector ALU operations with tuop_110 + tuop_010 pattern")
        print("    logic [2:0] tuop_second = instruction_data[32+14:32+12];")
        print("    logic [1:0] vecuop1 = instruction_data[11:10];")
        print("    logic [3:0] vecuop2_52 = instruction_data[32+19:32+16];")
        print("    logic rsen = instruction_data[8];")
        print("    logic immen = instruction_data[9];")
        print("    logic neg2 = instruction_data[28];")
        print("    ")
        print("    if (tuop_second == 3'b010 && vecuop1 == 2'b10) begin")
        print("        // Vector ALU operations")
        print("        case (vecuop2_52)")
        print("            4'b0000: begin // tadd operations")
        print("                if (rsen && !immen) begin")
        print("                    if (neg2)")
        print("                        return \"tadd.ttr.f32.neg2\";")
        print("                    else")
        print("                        return \"tadd.ttr.f32\";")
        print("                end")
        print("            end")
        print("            // 可以添加其他vecuop2值的处理")
        print("        endcase")
        print("    end")
        print("end")
