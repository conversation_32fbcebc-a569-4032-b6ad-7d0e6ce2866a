# MMA Operation
**该类指令使用TMAC单元，reuse bit仅能用于后续的MMA指令**
可参考[SIPU 1.0 TMAC 架构设计方案](https://siorigin.feishu.cn/docx/LPaIdDLccomQNBxCrwEcjuNDnpg)
| Syntax | Description |
| - | - |
| tuop | 011时为MMA类指令，110代表低32bit指令|
| mop | 见后续的mmauop encoding |
| micro_num | 表示连续进行几个microop的运算（m1、m2、m4），使用2的幂次方表示，即2^micro_num^。micro_num>0时，A和B矩阵数量都为一次micro_op的2^micro_num^的倍数。micro_op需看下方指令对应的列表。仅GEMV支持m8。 |
| Td | 8192-bit/1024-Byte tile寄存器编号，指定写回的寄存器位置。**不能和TS1/TS2操作的任意一个寄存器id相同** |
| Ts1 | 8192-bit/1024-Byte tile寄存器编号，指定A矩阵使用起始的tile寄存器。 |
| Ts2 | 8192-bit/1024-Byte tile寄存器编号，指定B矩阵使用起始的tile寄存器。 |
| rs1 | 64-bit整型标量寄存器编号，指定A矩阵使用的64bit矩阵描述符。 |
| rs2 | 64-bit整型标量寄存器编号，指定B矩阵使用的64bit矩阵描述符。 |
| ashape | a矩阵的shape<br>00代表32 rows(r32)模式<br>01代表16 rows(r16)模式<br>10代表8 rows(r8)模式<br> |
| neg | 矩阵乘的输入乘(-1)，公式变为D=-(AxB^T^)+D |
| gemv | 该bit为1标识该指令是GEMV指令 |
| reuse | 对下一条指令指示A、B、D三个矩阵会被复用，三个中仅一个会被复用。01代表reusea，10代表reuseb，11代表reused。reuse仅可作用于tile register。reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。**特殊的，reused生效后，后续非MMA指令读被reuse的寄存器，行为也是无法确定的。** |
| noacc | 该bit为1时D矩阵不参与累加。如果K方向多个tile(即n_ext, micro_num>1)，只有第一个不输入acc做累加。后续tile会和前面的tile做acc。|

## MMA运算公式为D = A x B^T^或D = A x B^T^ + D
**B矩阵是NxK排布，矩阵乘仅支持K连续排布的模式**
| Name | Layout |
| - | - |
| A | MxK |
| B | **NxK** |
| D | MxN |

dtype：f32/s32
atype/btype：tf32/bf16/f16/f8_e4m3/f8_e5m2/u8/s8/u4/s4/mxi8/mxi4/mxf8_e4m3/mxf8_e5m2/mxf6_e3m2/mxf6_e2m3/mxf4

#### 指令运算过程
[运算流程见SIPU 1.0 TMAC 架构设计方案 章节5](https://siorigin.feishu.cn/docx/LPaIdDLccomQNBxCrwEcjuNDnpg#LPtAdJkkzouftfxEk5mc26p1nXd)

[运算功能描述见SIPU 1.0 TMAC 架构设计方案 章节5](https://siorigin.feishu.cn/docx/LPaIdDLccomQNBxCrwEcjuNDnpg#Xc9WdcCp1o1PMkxYQDdcWvHRnob)
## 运算模式
### mmauop encoding
@import "table/mma_uop.csv"
下面是32 Rows/16 Rows/8 Rows的不同运算模式，输出仅支持32bit
- A8W4表示MXINT8 x MXINT4模式，A矩阵的输入数量是B的两倍
- MXFP6(A6W6)表示MXFP6 x MXFP6模式，该模式下A、B矩阵输入最少为3个Tile Reg
- Normal表示除了上述两种特殊情况的模式，输入最少时A和B都是只使用1个Tile Reg
## Tile Matrix Multiply Add
### GEMM指令（A、B矩阵从tileReg输入）
`tmma.ttt.dtype.atype.btype.r32/r16/r8.[m2/m4].[noacc].[reusea/reuseb/reused].[neg] Td, Ts1, Ts2`
@import "encode/tuop_011/memmata_0/memmatb_0/vecen_0/tmma.ttt.md"

#### 注意事项 {ignore=true}
- 当 micro_num == 0，reuseA/B/D 能三选一使用
- 当 micro_num > 0，仅能用 reuseD
- reuseD时，下一条指令MMA指令无法使用noacc的标志
- reuseD前后的MMA指令（包括tmma和tmma.mem），m1/m2/m4可以混用。
- tmma和tmma.mem指令若mmauop和Tile Shape**相同**可以使用reuseD
- **使用reuseA/B/D的MMA指令后续的Tile Core指令必须是同类型的MMA指令。MMA指令组合必须以reuse bits全为0的MMA指令作为结尾。**
- **reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。**
举例，下列reuse方式**不能**保证功能行为
- reuse后寄存器id不同
`tmma.ttt.f32.f16.f16.r32.reusea T4, T0, T1`
`tmma.ttt.f32.f16.f16.r32.reusea T4, T2, T1`
- reuse后被reuse寄存器被写人
`tmma.ttt.f32.f16.f16.r32.reusea T4, T1, T2`
`tld.trii.linear.u32.global T1, (a0), 0, 0`
`tmma.ttt.f32.f16.f16.r32.reusea T4, T1, T2`
- 指令中reuse的操作数也是目的操作数
`tmma.ttt.f32.f16.f16.r32.reusea T0, T0, T4`
#### 32 Rows Layout
@import "table/mma_32rows.csv"
##### M32N32 Normal运算模式
@import "graph/M32MMA.svg"
##### M32N32 A8W4运算模式
@import "graph/M32_A8W4_MMA.svg"
##### M32N32 MXFP6运算模式
@import "graph/M32_MXFP6_MMA.svg"
#### 16 Rows Layout
@import "table/mma_16rows.csv"
##### M16N32 Normal运算模式
- 该模式下reuseb不可用
@import "graph/M16MMA.svg"
##### M16N32 A8W4运算模式
- 该模式下reuseb不可用
@import "graph/M16_A8W4_MMA.svg"
##### M16N32 MXFP6运算模式
- 该模式下reuseb不可用
@import "graph/M16_MXFP6_MMA.svg"
#### 8 Rows Layout
@import "table/mma_8rows.csv"
##### M8N32 Normal运算模式
- 该模式下reuseb不可用
@import "graph/M8MMA.svg"
##### M8N32 A8W4运算模式
- 该模式下reuseb不可用
@import "graph/M8_A8W4_MMA.svg"
##### M8N32 MXFP6运算模式
- 该模式下reuseb不可用
@import "graph/M8_MXFP6_MMA.svg"
### GEMV指令（A向量、B矩阵从tileReg输入）
`tmva.ttt.dtype.atype.btype.[m2/m4/m8].[noacc].[reusea/reused].[neg] Td, Ts1, Ts2`
@import "encode/tuop_011/memmata_0/memmatb_0/vecen_1/aspen_0/tmva.ttt.md"
**dtype**：f32/s32
**atype/btype**：tf32/bf16/f16/f8_e4m3/f8_e5m2/u8/s8/u4/s4/mxi8/mxi4/mxf8_e4m3/mxf8_e5m2/mxf6_e3m2/mxf6_e2m3/mxf4
#### 注意事项 {ignore=true}
- reuseA/D 能二选一使用，reuseB不可用
- reuseD时，下一条指令MMA指令无法使用noacc的标志
- tmva和tmva.mem指令若mmauop和Tile Shape**相同**可以使用reuseD
- tmva和tmva.mem指令若mmauop和Tile Shape以及micro_num_exp**相同**可以使用reuseA
- reuseD前后的MMA指令（包括tmva和tmva.mem），m1/m2/m4可以混用。
- **该指令的结果只会写到Tile Reg的前1/8。该指令执行完成，Tile Reg中其余7/8的数据是不确定的。**
- **使用reuseA/B/D的MMA指令后续的Tile Core指令必须是同类型的MMA指令。MMA指令组合必须以reuse bits全为0的MMA指令作为结尾。**
- **reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。**
#### GEMV运算模式
@import "table/mma_gemv.csv"
##### M1N32 Normal运算模式
@import "graph/GEMV.svg"
##### M1N32 A8W4运算模式
@import "graph/GEMV_A8W4.svg"
##### M1N32 MXFP6运算模式
@import "graph/GEMV_MXFP6.svg"
## Tile Matrix Multiply Add from Memory
从memory读数据的矩阵乘累加指令。
**这类指令都被视为一条share memory load指令，tsld_cnt信号在TFE发射时cnt会+1**
[运算流程见SIPU 1.0 TMAC 架构设计方案 章节5.6](https://siorigin.feishu.cn/docx/LPaIdDLccomQNBxCrwEcjuNDnpg#ByDFdsVYOoU9fHx0Mm1cT1hBnbc)
| Field | Start bit | End bit | # bits	|Definition | Notes |
| - | - | - | - | - | - |
| startTileIdx | 0 |15 | 16 | Start tile index offset of Operand A/B from the base address of Tensor chunk A/B. Require: Align to 4 tiles (supertile size) range: 0~65535 | Will be changed dynamically. |
| Reserved | 16 | 31 | 16 | | These bits must be zero. Otherwise,report illegal instruction. |
| startAddr	| 32 | 55 | 24 | The base address in 1B of tensor chunk A/B on local PE L2B. Representing 0~1MB. Support mode0/1 address mode. | HW needs to add L2B offset of current thread block only in mode 0.  |
| Reserved	| 56 | 63 | 8 | | These bits must be zero. Otherwise,report illegal instruction. |
| Total | | | 64 |

**startAddr地址的表达如下：**

| 位范围 | 字段名称 | 描述 | 大小（位）| 备注 |
| - | - | - | - | - |
| 23-22 | **MODE** | **模式选择字段** | 2 | 支持2种模式（00,01），分别代表模式0和模式1。|
| 21-20 | （保留） | 未使用 | 2 | 未来扩展使用。|
| 19-0 | SMEM Address | Local PE的Share Memory地址 | 2 | 可寻址范围：0x0–0xFFFFF（1MB）|

| 模式 | 描述 |
| - | - |
| 模式0 | 访问的地址的MODE字段为00时，访问的local L2B空间从smembase起始。 |
| 模式1 | 访问的地址的MODE字段为01时，访存的local L2B空间从0起始。|

描述符如上，详情见见[******* Matrix Descriptor](https://siorigin.feishu.cn/docx/BhzVd25X8ox5nWx8C5Nc7mJCnWb#FSrVdKyD8oYIfmxZNBgcSElBn2b)
### GEMM.mem指令
#### 注意事项 {ignore=true}
- 仅能使用 reuseD
- reuseD时，下一条指令MMA指令无法使用noacc的标志
- **使用reuseD的MMA指令后续的Tile Core指令必须是同类型的MMA指令。MMA指令组合必须以reuse bits全为0的MMA指令作为结尾。**
- **reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。**
#### GEMM.mem指令（A矩阵从shareMemory输入，B矩阵从tileReg输入）
`tmma.trt.dtype.atype.btype.r32/r16/r8.[noacc].[reused].[neg] Td, rs1, Ts2`
@import "encode/tuop_011/memmata_1/memmatb_0/tmma.trt.md"
#### GEMM.mem指令（A矩阵从tileReg输入，B矩阵从shareMemory输入）
`tmma.ttr.dtype.atype.btype.r32/r16/r8.[noacc].[reused].[neg] Td, Ts1, rs2`
@import "encode/tuop_011/memmata_0/memmatb_1/vecen_0/tmma.ttr.md"
#### GEMM.mem指令（A矩阵、B矩阵从shareMemory输入）
`tmma.trr.dtype.atype.btype.r32/r16/r8.[noacc].[reused].[neg] Td, rs1, rs2`
@import "encode/tuop_011/memmata_1/memmatb_1/tmma.trr.md"

dtype：f32/s32
atype/btype：tf32/bf16/f16/f8_e4m3/f8_e5m2/u8/s8/u4/s4/mxi8/mxi4/mxf8_e4m3/mxf8_e5m2/mxf6_e3m2/mxf6_e2m3/mxf4
#### 32 Rows Layout
@import "table/mma_mem_32rows.csv"
#### 16 Rows Layout
@import "table/mma_mem_16rows.csv"
#### 8 Rows Layout
@import "table/mma_mem_8rows.csv"
### GEMV.mem指令
#### 注意事项 {ignore=true}
- 仅能使用 reuseA/reuseD
- reuseD时，下一条指令MMA指令无法使用noacc的标志
- **该指令的结果只会写到Tile Reg的前1/8。该指令执行完成，Tile Reg中7/8的数据是不确定的。**
- **使用reuseD的MMA指令后续的Tile Core指令必须是同类型的MMA指令。MMA指令组合必须以reuse bits全为0的MMA指令作为结尾。**
- **reuse生效后，寄存器中的数据将会搬运到接近运算单元的位置，此时后续指令写入了被reuse的寄存器或者后续使用其他寄存器作为src，两次数据的一致性被破坏。行为是无法确定的。**
#### GEMV.mem指令（A矩阵从tileReg输入，B矩阵从shareMemory输入）
`tmva.ttr.dtype.atype.btype.[m2/m4/m8].[noacc].[reusea/reused].[neg] Td, Ts1, rs2`
@import "encode/tuop_011/memmata_0/memmatb_1/vecen_1/aspen_0/tmva.ttr.md"
@import "table/mma_mem_gemv.csv"