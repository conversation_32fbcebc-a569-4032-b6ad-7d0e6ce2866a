module verify_bits;
    reg [31:0] word1, word2;
    reg [63:0] instruction;
    
    initial begin
        instruction = 64'h027b1009e4fb;
        word1 = instruction[31:0];   // 0x1009e4fb
        word2 = instruction[63:32];  // 0x0000027b
        
        $display("Manual bit analysis:");
        $display("Word 1: 0x%08x = %032b", word1, word1);
        $display("Word 2: 0x%08x = %032b", word2, word2);
        $display("");
        
        // From word 1: 0x1009e4fb = 0001 0000 0000 1001 1110 0100 1111 1011
        $display("Word 1 bit fields:");
        $display("  [6:0]   ACE_OP: %07b = 0x%02x", word1[6:0], word1[6:0]);
        $display("  [11:10] vecuop1: %02b = %0d", word1[11:10], word1[11:10]);
        $display("  [14:12] tuop: %03b = %0d", word1[14:12], word1[14:12]);
        $display("  [8]     rsen: %b", word1[8]);
        $display("  [9]     immen: %b", word1[9]);
        $display("  [19:15] rs2: %05b = %0d", word1[19:15], word1[19:15]);
        $display("  [24:20] ???: %05b = %0d", word1[24:20], word1[24:20]);
        $display("  [27]    neg1: %b", word1[27]);
        $display("  [28]    neg2: %b", word1[28]);
        $display("");
        
        // From word 2: 0x027b = 0000 0010 0111 1011
        $display("Word 2 bit fields:");
        $display("  [6:0]   ACE_OP: %07b = 0x%02x", word2[6:0], word2[6:0]);
        $display("  [14:12] tuop: %03b = %0d", word2[14:12], word2[14:12]);
        $display("  [19:16] vecuop2[5:2]: %04b = %0d", word2[19:16], word2[19:16]);
        $display("");
        
        // Let me try different bit indexing approaches
        $display("Alternative bit field readings:");
        $display("  Instruction bits [8]: %b", instruction[8]);
        $display("  Instruction bits [9]: %b", instruction[9]);
        $display("  Instruction bits [28]: %b", instruction[28]);
        $display("  Instruction bits [11:10]: %02b", instruction[11:10]);
        $display("  Instruction bits [51:48]: %04b", instruction[51:48]);  // This should be vecuop2[5:2]
        
        $finish;
    end
endmodule
