import sys
import os
import re

def process_hex_file(input_path):
    """
    处理包含地址信息的文本文件，提取16进制地址并统计不同值
    
    参数:
    input_path -- 输入文件路径
    
    返回:
    original_unique -- 原始地址的不同值集合
    results_unique -- 计算结果的不同值集合
    total_values -- 有效行总数
    """
    hex_values = []
    line_count = 0
    valid_lines = 0
    
    # 用于匹配地址字段的正则表达式
    addr_pattern = re.compile(r'addr:([0-9a-fA-F]+)')
    
    try:
        with open(input_path, 'r') as file:
            for line_num, line in enumerate(file, 1):
                line_count += 1
                stripped_line = line.strip()
                if not stripped_line:
                    continue
                
                # 使用正则表达式提取地址
                match = addr_pattern.search(stripped_line)
                if match:
                    addr_str = match.group(1)
                    try:
                        # 将提取的地址字符串转换为整数
                        hex_value = int(addr_str, 16)
                        hex_values.append(hex_value)
                        valid_lines += 1
                    except ValueError:
                        print(f"警告：第{line_num}行的地址 '{addr_str}' 无法转换为16进制数")
                else:
                    print(f"警告：第{line_num}行未找到地址字段")
    
    except FileNotFoundError:
        print(f"错误：文件 '{input_path}' 不存在")
        return set(), set(), 0
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return set(), set(), 0
    
    # 计算原始数据的不同值
    original_unique = set(hex_values)
    # 计算除以0x200000的结果
    divisor = 0x200000
    results_unique = {value // divisor for value in hex_values}
    
    print(f"处理完成：共 {line_count} 行，其中有效行 {valid_lines} 行")
    return original_unique, results_unique, valid_lines

def write_to_files(input_path, original_unique, results_unique):
    """将不同值写入文件"""
    try:
        # 生成输出文件名
        base_name, ext = os.path.splitext(input_path)
        original_file = f"{base_name}.original.unique"
        results_file = f"{base_name}.results.unique"
        
        # 写入原始数据的不同值
        with open(original_file, 'w') as f:
            for value in sorted(original_unique):
                f.write(f"0x{value:x}\n")
        
        # 写入计算结果的不同值
        with open(results_file, 'w') as f:
            for value in sorted(results_unique):
                f.write(f"0x{value:x}\n")
        
        print(f"结果已写入:")
        print(f"  原始地址不同值: {original_file}")
        print(f"  计算结果不同值: {results_file}")
    
    except Exception as e:
        print(f"写入文件时发生错误：{e}")

def main():
    if len(sys.argv) < 2:
        print("用法: python hex_counter.py <文件路径>")
        print("示例: python hex_counter.py trace.log")
        return
    
    input_path = sys.argv[1]
    original_unique, results_unique, total_values = process_hex_file(input_path)
    
    print(f"\n文件中共有 {total_values} 个有效地址")
    print(f"原始地址不同值数量: {len(original_unique)}")
    print(f"除以0x200000后不同结果数量: {len(results_unique)}")
    
    # 写入文件
    write_to_files(input_path, original_unique, results_unique)

if __name__ == "__main__":
    main()