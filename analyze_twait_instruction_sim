#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_0000021316d90cf0 .scope module, "analyze_twait_instruction" "analyze_twait_instruction" 2 2;
 .timescale 0 0;
v0000021316d8f790_0 .var "ace_op", 6 0;
v0000021316d8a3f0_0 .var "ctrluop", 2 0;
v0000021316d8d310_0 .var "is_sync_wait", 0 0;
v0000021316c379f0_0 .var "is_tile", 0 0;
v0000021316c37a90_0 .var "mem_bit", 0 0;
v0000021316d8ee70_0 .var "rs1", 4 0;
v0000021316d8ef10_0 .var "rw", 1 0;
v0000021316c3a3e0_0 .var "test_instruction", 31 0;
v0000021316c3a480_0 .var "tuop", 2 0;
v0000021316d94dc0_0 .var "waitop", 2 0;
    .scope S_0000021316d90cf0;
T_0 ;
    %pushi/vec4 545673339, 0, 32;
    %store/vec4 v0000021316c3a3e0_0, 0, 32;
    %vpi_call 2 17 "$display", "=== Analyzing Instruction 0x%08x ===", v0000021316c3a3e0_0 {0 0 0};
    %vpi_call 2 18 "$display", "Binary: %b", v0000021316c3a3e0_0 {0 0 0};
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0000021316d8f790_0, 0, 7;
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0000021316c3a480_0, 0, 3;
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0000021316d8a3f0_0, 0, 3;
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0000021316d94dc0_0, 0, 3;
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0000021316d8ee70_0, 0, 5;
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0000021316c37a90_0, 0, 1;
    %load/vec4 v0000021316c3a3e0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0000021316d8ef10_0, 0, 2;
    %vpi_call 2 29 "$display", "\000" {0 0 0};
    %vpi_call 2 30 "$display", "=== Bit Field Analysis ===" {0 0 0};
    %load/vec4 v0000021316d8f790_0;
    %cmpi/e 123, 0, 7;
    %flag_mov 8, 4;
    %jmp/0 T_0.0, 8;
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %pushi/vec4 541675091, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1414681923, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1414090574, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.1, 8;
T_0.0 ; End of true expr.
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1313821728, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.1, 8;
 ; End of false expr.
    %blend;
T_0.1;
    %vpi_call 2 31 "$display", "ACE_OP[6:0]:    %b (%h) - %s", v0000021316d8f790_0, v0000021316d8f790_0, S<0,vec4,u128> {1 0 0};
    %vpi_call 2 33 "$display", "RW[31:30]:      %b (%d)", v0000021316d8ef10_0, v0000021316d8ef10_0 {0 0 0};
    %vpi_call 2 34 "$display", "WAITOP[30:28]:  %b (%d)", v0000021316d94dc0_0, v0000021316d94dc0_0 {0 0 0};
    %vpi_call 2 35 "$display", "CTRLUOP[25:23]: %b (%d)", v0000021316d8a3f0_0, v0000021316d8a3f0_0 {0 0 0};
    %vpi_call 2 36 "$display", "MEM_BIT[26]:    %b (%d)", v0000021316c37a90_0, v0000021316c37a90_0 {0 0 0};
    %vpi_call 2 37 "$display", "RS1[19:15]:     %b (%d) - register a%d/x%d", v0000021316d8ee70_0, v0000021316d8ee70_0, v0000021316d8ee70_0, v0000021316d8ee70_0 {0 0 0};
    %vpi_call 2 38 "$display", "TUOP[14:12]:    %b (%d)", v0000021316c3a480_0, v0000021316c3a480_0 {0 0 0};
    %load/vec4 v0000021316d8f790_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v0000021316c379f0_0, 0, 1;
    %load/vec4 v0000021316c379f0_0;
    %flag_set/vec4 8;
    %flag_get/vec4 8;
    %jmp/0 T_0.2, 8;
    %load/vec4 v0000021316c3a480_0;
    %pushi/vec4 5, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.2;
    %store/vec4 v0000021316d8d310_0, 0, 1;
    %vpi_call 2 44 "$display", "\000" {0 0 0};
    %vpi_call 2 45 "$display", "=== Instruction Classification ===" {0 0 0};
    %load/vec4 v0000021316c379f0_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.3, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.4, 8;
T_0.3 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.4, 8;
 ; End of false expr.
    %blend;
T_0.4;
    %vpi_call 2 46 "$display", "Is tile instruction:     %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000021316c3a480_0;
    %cmpi/e 5, 0, 3;
    %flag_mov 8, 4;
    %jmp/0 T_0.5, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.6, 8;
T_0.5 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.6, 8;
 ; End of false expr.
    %blend;
T_0.6;
    %vpi_call 2 47 "$display", "Is tuop_101 (sync):      %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000021316d8d310_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.7, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.8, 8;
T_0.7 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.8, 8;
 ; End of false expr.
    %blend;
T_0.8;
    %vpi_call 2 48 "$display", "Should be sync/wait:     %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0000021316c379f0_0;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.11, 9;
    %load/vec4 v0000021316c3a480_0;
    %pushi/vec4 5, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.11;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.9, 8;
    %vpi_call 2 51 "$display", "\000" {0 0 0};
    %vpi_call 2 52 "$display", "=== TUOP_101 Analysis ===" {0 0 0};
    %vpi_call 2 53 "$display", "CTRLUOP = %d, WAITOP = %d", v0000021316d8a3f0_0, v0000021316d94dc0_0 {0 0 0};
    %load/vec4 v0000021316d8a3f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_0.12, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_0.13, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_0.14, 6;
    %vpi_call 2 70 "$display", "CTRLUOP %d: unknown", v0000021316d8a3f0_0 {0 0 0};
    %jmp T_0.16;
T_0.12 ;
    %vpi_call 2 56 "$display", "CTRLUOP 000: tsync operations" {0 0 0};
    %jmp T_0.16;
T_0.13 ;
    %vpi_call 2 58 "$display", "CTRLUOP 001: twait operations" {0 0 0};
    %load/vec4 v0000021316d94dc0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_0.17, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_0.18, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_0.19, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_0.20, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_0.21, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_0.22, 6;
    %vpi_call 2 66 "$display", "  WAITOP %d: unknown", v0000021316d94dc0_0 {0 0 0};
    %jmp T_0.24;
T_0.17 ;
    %vpi_call 2 60 "$display", "  WAITOP 000: twait.i.ls (load/store immediate)" {0 0 0};
    %jmp T_0.24;
T_0.18 ;
    %vpi_call 2 61 "$display", "  WAITOP 001: twait.r.ls (load/store register)" {0 0 0};
    %jmp T_0.24;
T_0.19 ;
    %vpi_call 2 62 "$display", "  WAITOP 010: twait.r.tacp_cg (tacp commit group)" {0 0 0};
    %jmp T_0.24;
T_0.20 ;
    %vpi_call 2 63 "$display", "  WAITOP 011: twait.r.rmtfence (remote fence)" {0 0 0};
    %jmp T_0.24;
T_0.21 ;
    %vpi_call 2 64 "$display", "  WAITOP 100: tkill.r" {0 0 0};
    %jmp T_0.24;
T_0.22 ;
    %vpi_call 2 65 "$display", "  WAITOP 111: twait (basic)" {0 0 0};
    %jmp T_0.24;
T_0.24 ;
    %pop/vec4 1;
    %jmp T_0.16;
T_0.14 ;
    %vpi_call 2 69 "$display", "CTRLUOP 010: trmtfence operations" {0 0 0};
    %jmp T_0.16;
T_0.16 ;
    %pop/vec4 1;
T_0.9 ;
    %vpi_call 2 74 "$display", "\000" {0 0 0};
    %vpi_call 2 75 "$display", "=== Expected vs Current Decoding ===" {0 0 0};
    %vpi_call 2 76 "$display", "Expected: twait.r.tacp_cg a2" {0 0 0};
    %vpi_call 2 77 "$display", "Current:  unknown_sync" {0 0 0};
    %vpi_call 2 78 "$display", "\000" {0 0 0};
    %load/vec4 v0000021316c379f0_0;
    %flag_set/vec4 11;
    %flag_get/vec4 11;
    %jmp/0 T_0.29, 11;
    %load/vec4 v0000021316c3a480_0;
    %pushi/vec4 5, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.29;
    %flag_set/vec4 10;
    %flag_get/vec4 10;
    %jmp/0 T_0.28, 10;
    %load/vec4 v0000021316d8a3f0_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.28;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.27, 9;
    %load/vec4 v0000021316d94dc0_0;
    %pushi/vec4 2, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.27;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.25, 8;
    %vpi_call 2 82 "$display", "\342\234\223 This should be: twait.r.tacp_cg x%d (a%d)", v0000021316d8ee70_0, v0000021316d8ee70_0 {0 0 0};
    %vpi_call 2 83 "$display", "\342\234\223 Matches expected pattern for twait.r.tacp_cg" {0 0 0};
    %jmp T_0.26;
T_0.25 ;
    %vpi_call 2 85 "$display", "\342\234\227 Does not match expected pattern" {0 0 0};
T_0.26 ;
    %vpi_call 2 88 "$display", "\000" {0 0 0};
    %vpi_call 2 89 "$display", "=== Problem Identification ===" {0 0 0};
    %vpi_call 2 90 "$display", "The issue is likely in extract_instruction_name() function:" {0 0 0};
    %vpi_call 2 91 "$display", "1. Current logic only handles ctrluop=001 && waitop=111" {0 0 0};
    %vpi_call 2 92 "$display", "2. Missing support for other waitop values like 010 (tacp_cg)" {0 0 0};
    %vpi_call 2 93 "$display", "3. Need to add comprehensive waitop decoding" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "analyze_twait_instruction.sv";
