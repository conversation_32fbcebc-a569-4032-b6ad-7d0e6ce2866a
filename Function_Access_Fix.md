# SystemVerilog函数访问问题修复

## 问题描述

在SystemVerilog中，module内定义的function默认情况下**不能**被module外部直接调用，包括在initial块、always块或其他module中。这是SystemVerilog的作用域规则。

## 解决方案

将所有函数移动到**package**中，这样就可以在任何地方调用这些函数。

## 修复后的架构

### 1. Package定义 (`tile_decoder_pkg`)

```systemverilog
package tile_decoder_pkg;
    // 类型定义
    typedef enum logic [1:0] {
        INSTR_32BIT  = 2'b00,
        INSTR_64BIT  = 2'b01,
        INSTR_96BIT  = 2'b10,
        INSTR_128BIT = 2'b11
    } instr_length_e;

    // 所有函数都在package中
    function automatic logic is_tile_instruction(input logic [31:0] first_word);
        // 函数实现
    endfunction

    function automatic instr_length_e get_instruction_length(input logic [31:0] first_word);
        // 函数实现
    endfunction

    // ... 其他所有函数
endpackage
```

### 2. 正确的使用方式

#### 方式1：导入package后直接调用
```systemverilog
import tile_decoder_pkg::*;

module my_test;
    initial begin
        logic [31:0] word = 32'h0000607b;
        
        // 直接调用函数
        logic is_tile = is_tile_instruction(word);
        instr_length_e length = get_instruction_length(word);
        instr_collector_t collector = init_collector(word);
        
        $display("Is tile: %s, Length: %s", 
                is_tile ? "YES" : "NO", length.name());
    end
endmodule
```

#### 方式2：使用完整路径调用
```systemverilog
module my_test;
    initial begin
        logic [31:0] word = 32'h0000607b;
        
        // 使用完整路径调用
        logic is_tile = tile_decoder_pkg::is_tile_instruction(word);
        tile_decoder_pkg::instr_length_e length = tile_decoder_pkg::get_instruction_length(word);
        
        $display("Is tile: %s", is_tile ? "YES" : "NO");
    end
endmodule
```

## 支持的调用上下文

修复后，函数可以在以下所有上下文中调用：

### 1. Initial块
```systemverilog
import tile_decoder_pkg::*;

module test;
    initial begin
        logic [31:0] word = 32'h0000607b;
        logic is_tile = is_tile_instruction(word);  // ✓ 可以调用
        $display("Result: %s", is_tile ? "TILE" : "NON-TILE");
    end
endmodule
```

### 2. Always块（组合逻辑）
```systemverilog
import tile_decoder_pkg::*;

module decoder_logic(
    input logic [31:0] instruction_word,
    output logic is_tile_instr,
    output instr_length_e instr_length
);
    always_comb begin
        is_tile_instr = is_tile_instruction(instruction_word);  // ✓ 可以调用
        instr_length = get_instruction_length(instruction_word); // ✓ 可以调用
    end
endmodule
```

### 3. Always块（时序逻辑）
```systemverilog
import tile_decoder_pkg::*;

module sequential_decoder(
    input logic clk,
    input logic [31:0] instruction_word,
    output logic is_tile_instr
);
    always_ff @(posedge clk) begin
        is_tile_instr <= is_tile_instruction(instruction_word);  // ✓ 可以调用
    end
endmodule
```

### 4. Task和Function中
```systemverilog
import tile_decoder_pkg::*;

module processor;
    task automatic process_instruction(input logic [31:0] word);
        instr_collector_t collector;
        string disasm_result;
        
        if (is_tile_instruction(word)) begin  // ✓ 可以调用
            collector = init_collector(word);  // ✓ 可以调用
            
            if (collector.is_complete) begin
                disasm_result = disassemble_instruction(  // ✓ 可以调用
                    collector.instruction_data, 
                    collector.expected_length
                );
                $display("Decoded: %s", disasm_result);
            end
        end
    endtask
endmodule
```

## 文件结构更新

### 主要文件
1. **`tile_instruction_decoder.sv`** - 包含package定义和示例module
2. **`simple_decoder_test.sv`** - 演示在initial块中使用函数
3. **`tile_decoder_testbench.sv`** - 完整测试套件
4. **`instruction_fetch_unit.sv`** - 流水线集成示例

### 编译和运行
```bash
# 编译所有文件
make compile

# 运行简单测试（演示initial块用法）
make simple_test

# 运行完整测试套件
make simulate

# 查看帮助
make help
```

## 关键改进

### 1. 函数可访问性
- ✅ **修复前**: 函数在module内，外部无法调用
- ✅ **修复后**: 函数在package中，任何地方都可以调用

### 2. 使用便利性
- ✅ 支持在initial块中直接调用
- ✅ 支持在always块中调用
- ✅ 支持在task/function中调用
- ✅ 支持跨module调用

### 3. 代码组织
- ✅ 所有相关类型和函数集中在一个package中
- ✅ 清晰的命名空间管理
- ✅ 易于维护和扩展

## 示例：完整的使用流程

```systemverilog
// 文件: my_tile_processor.sv
import tile_decoder_pkg::*;

module my_tile_processor;
    // 在initial块中演示完整流程
    initial begin
        logic [31:0] instruction_stream[] = '{
            32'h0000407b,  // 32-bit CSR
            32'h0000607b, 32'h8000007b,  // 64-bit memory load
            32'h12345678   // Non-tile instruction
        };
        
        instr_collector_t collector;
        logic collector_active = 1'b0;
        string disasm_result;
        
        $display("Processing instruction stream...");
        
        foreach (instruction_stream[i]) begin
            logic [31:0] word = instruction_stream[i];
            $display("Word %0d: 0x%08x", i, word);
            
            if (!collector_active) begin
                // 检查是否为tile指令
                if (is_tile_instruction(word)) begin
                    // 初始化收集器
                    collector = init_collector(word);
                    collector_active = 1'b1;
                    
                    if (collector.is_complete) begin
                        // 32位指令立即完成
                        disasm_result = disassemble_instruction(
                            collector.instruction_data, 
                            collector.expected_length
                        );
                        $display("  -> %s", disasm_result);
                        collector_active = 1'b0;
                    end
                end else begin
                    $display("  -> Non-tile instruction");
                end
            end else begin
                // 添加到现有指令
                collector = add_word_to_collector(collector, word);
                
                if (collector.is_complete) begin
                    disasm_result = disassemble_instruction(
                        collector.instruction_data, 
                        collector.expected_length
                    );
                    $display("  -> %s", disasm_result);
                    collector_active = 1'b0;
                end
            end
        end
        
        $display("Processing complete!");
    end
endmodule
```

这个修复确保了所有函数都可以在任何SystemVerilog上下文中正常使用，包括initial块、always块、task和function中。
