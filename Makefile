# Makefile for Tile Extension ISA Decoder SystemVerilog Implementation

# Tool configuration
SIMULATOR ?= questa
VLOG_OPTS = +acc -sv
VSIM_OPTS = -c -do "run -all; quit"

# Source files
SV_SOURCES = tile_instruction_decoder.sv \
             tile_decoder_testbench.sv \
             instruction_fetch_unit.sv \
             simple_decoder_test.sv \
             test_specific_instruction.sv \
             test_all_bitwidths.sv \
             test_csr_instructions.sv

# Default target
all: compile simulate

# Compile SystemVerilog sources
compile:
	@echo "Compiling SystemVerilog sources..."
	vlib work
	vlog $(VLOG_OPTS) $(SV_SOURCES)

# Run basic testbench
simulate: compile
	@echo "Running tile decoder testbench..."
	vsim $(VSIM_OPTS) tile_decoder_testbench

# Run with debug output
debug: compile
	@echo "Running with debug output..."
	vsim $(VSIM_OPTS) +define+DEBUG_TILE_FETCH tile_decoder_testbench

# Run instruction fetch unit test
fetch_test: compile
	@echo "Running instruction fetch unit test..."
	vsim $(VSIM_OPTS) instruction_fetch_unit

# Run simple decoder test
simple_test: compile
	@echo "Running simple decoder test..."
	vsim $(VSIM_OPTS) simple_decoder_test

# Run specific instruction test
specific_test: compile
	@echo "Running specific instruction test..."
	vsim $(VSIM_OPTS) test_specific_instruction

# Run all bitwidths test
bitwidth_test: compile
	@echo "Running all bitwidths test..."
	vsim $(VSIM_OPTS) test_all_bitwidths

# Run CSR instructions test
csr_test: compile
	@echo "Running CSR instructions test..."
	vsim $(VSIM_OPTS) test_csr_instructions

# Generate documentation
docs:
	@echo "Generating documentation..."
	@echo "API documentation available in tile_decoder_api.md"
	@echo "Source files:"
	@for file in $(SV_SOURCES); do echo "  - $$file"; done

# Clean generated files
clean:
	@echo "Cleaning generated files..."
	rm -rf work
	rm -f transcript
	rm -f vsim.wlf
	rm -f *.log

# Lint check (if available)
lint: compile
	@echo "Running lint checks..."
	# Add your preferred linter here, e.g.:
	# verilator --lint-only $(SV_SOURCES)

# Help target
help:
	@echo "Available targets:"
	@echo "  all           - Compile and run basic simulation"
	@echo "  compile       - Compile SystemVerilog sources"
	@echo "  simulate      - Run tile decoder testbench"
	@echo "  debug         - Run with debug output enabled"
	@echo "  fetch_test    - Run instruction fetch unit test"
	@echo "  simple_test   - Run simple decoder test (demonstrates initial block usage)"
	@echo "  specific_test - Run test for specific instruction 0x8003907b8200647b"
	@echo "  bitwidth_test - Run comprehensive test for all bit widths (32/64/96/128)"
	@echo "  csr_test      - Run CSR instructions test (32-bit only)"
	@echo "  docs          - Show documentation info"
	@echo "  clean         - Remove generated files"
	@echo "  lint          - Run lint checks"
	@echo "  help          - Show this help"

.PHONY: all compile simulate debug fetch_test simple_test specific_test bitwidth_test csr_test docs clean lint help
