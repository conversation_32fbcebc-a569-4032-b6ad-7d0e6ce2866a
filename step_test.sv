// Step by step test for twait instruction
import tile_decoder_pkg::*;

module step_test;

    initial begin
        logic [31:0] test_instruction;
        logic [127:0] instruction_data;
        string result;

        $display("=== Step by step test ===");
        
        // Test the specific instruction: 0x7080507B
        test_instruction = 32'h7080507B;
        instruction_data = {96'h0, test_instruction};
        
        $display("Testing instruction: 0x%08x", test_instruction);
        
        // Step 1: Test if it's a tile instruction
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("✓ Step 1: Recognized as tile instruction");
        end else begin
            $display("✗ Step 1: Not recognized as tile instruction");
            $finish;
        end
        
        // Step 2: Test length detection
        $display("✓ Step 2: Length detection completed");

        // Step 3: Test instruction name extraction (this is where the crash happens)
        $display("Step 3: About to call extract_instruction_name...");
        result = tile_decoder_pkg::extract_instruction_name(instruction_data, tile_decoder_pkg::INSTR_32BIT);
        $display("✓ Step 3: extract_instruction_name completed, result = %s", result);
        
        $display("=== Test Complete ===");
    end

endmodule
