#!/usr/bin/env python3

import re
import glob
import os
import yaml
import sys
import json


def process_tiledesc(ifile, odict):
    idict = {}
    instr_name = "null"
    with open(ifile, "r") as f:
        yaml_string = ""
        for line in f.read().splitlines():
            if line.startswith("```"):
                continue
            elif line.startswith("//"):
                instr_name = line.strip().replace("//","")
            else:
                yaml_string += line

        try:
            idict = yaml.safe_load(yaml_string)
            #print(f"Get instr <{instr_name}> from file: {ifile})")
        except Exception as exc:
            print(f"Error: an unexpected error when loading yaml: {exc}")
            return 1


        if instr_name in odict:
            print(f"Error: {instr_name} already defined")
            return 1

        odict[instr_name] = {}
        odict[instr_name]["Instr"] = instr_name
        odict[instr_name]["Encode"] = ""
        odict[instr_name]["Match"] = ""
        odict[instr_name]["File"] = ifile

        # Calculate encodings
        pos = 0
        encoding = ""
        match = ""
        mask = ""
        variable = ""

        for field in idict["reg"]:
            bits = field.get("bits", 999)
            attr = field.get("attr", "")
            fname = field.get("name", "")

            if attr != "":
                if len(attr) != bits:
                    print(f"Error: {instr_name}: length of attr doesn\'t match bits in {field}")
                    return 1
                else: 
                    encoding = attr + encoding;

            elif fname != "":
                if variable == "":
                    variable = fname
                else:
                    variable = fname + "-" + variable

                encoding = "-"*bits + encoding
            else:
                # this is hole
                encoding = "^"*bits + encoding

            pos += bits

        odict[instr_name]["Encode"] = encoding
        imatch = "".join(encoding).replace('-','0').replace('^','0').replace('x','0')
        odict[instr_name]["Match"] = hex(int(imatch,2))

    #finish normally
    return 0



#------------------------------------------------------
if __name__ == "__main__":

    all_files = glob.glob("./encode/**/*.md", recursive = True)
    ace_all_files = glob.glob("./ace_encode/**/*.md", recursive = True)

    tile_dict = {}
    err_num = 0
    instr_num = 0

    for md_file in all_files:
        if not "encode.md" in md_file:
            # Skip encode.md and get the leaf md
            err_num += process_tiledesc(md_file, tile_dict)

    for md_file in ace_all_files:
        if not "encode.md" in md_file:
            # Skip encode.md and get the leaf md
            err_num += process_tiledesc(md_file, tile_dict)

    imatch_dict = {}
    for item in tile_dict:
        key = str(tile_dict[item]["Match"])
        if imatch_dict.get(key, "") != "":
            imatch_dict[key].append(tile_dict[item]['Instr'])
        else:
            imatch_dict[key] = [tile_dict[item]['Instr']]

        
    for key in imatch_dict:
        if len(imatch_dict[key]) > 1:
            print("Error: Following instructions have same encoding:")
            err_num += 1
            for instr in imatch_dict[key]:
                print("%30s, %s, %s" % (instr, tile_dict[instr]['Encode'], tile_dict[instr]['File']))


    #write to log file 
    with open ("isa_out.log", "w") as fo:
        for item in tile_dict:
            instr_num += 1
            fo.write("%-30s, %64s, %s\n" % (item, tile_dict[item]['Encode'], tile_dict[item]['File']))


    print(f"=======\nParse Done. {instr_num} instructions, {err_num} errors.")
    print(f"Check isa_out.log for details.")
