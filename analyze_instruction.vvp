#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2009.vpi";
S_0000020b34c19de0 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0000020b34bd9230 .scope module, "analyze_instruction" "analyze_instruction" 3 1;
 .timescale 0 0;
v0000020b34c176d0_0 .var "instruction", 63 0;
v0000020b34bf33a0_0 .var "word1", 31 0;
v0000020b34bd8290_0 .var "word2", 31 0;
    .scope S_0000020b34bd9230;
T_0 ;
    %pushi/vec4 2179743783, 0, 50;
    %concati/vec4 9467, 0, 14;
    %store/vec4 v0000020b34c176d0_0, 0, 64;
    %load/vec4 v0000020b34c176d0_0;
    %parti/s 32, 0, 2;
    %store/vec4 v0000020b34bf33a0_0, 0, 32;
    %load/vec4 v0000020b34c176d0_0;
    %parti/s 32, 32, 7;
    %store/vec4 v0000020b34bd8290_0, 0, 32;
    %vpi_call/w 3 10 "$display", "=== \345\210\206\346\236\220\346\214\207\344\273\244 0x%016x ===", v0000020b34c176d0_0 {0 0 0};
    %vpi_call/w 3 11 "$display", "Word 1: 0x%08x = %032b", v0000020b34bf33a0_0, v0000020b34bf33a0_0 {0 0 0};
    %vpi_call/w 3 12 "$display", "Word 2: 0x%08x = %032b", v0000020b34bd8290_0, v0000020b34bd8290_0 {0 0 0};
    %vpi_call/w 3 13 "$display", "\000" {0 0 0};
    %vpi_call/w 3 16 "$display", "Word 1 \344\275\215\345\237\237\345\210\206\346\236\220:" {0 0 0};
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 7, 0, 2;
    %cmpi/e 123, 0, 7;
    %flag_mov 8, 4;
    %jmp/0 T_0.0, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.1, 8;
T_0.0 ; End of true expr.
    %pushi/vec4 1313821728, 0, 32; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.1, 8;
 ; End of false expr.
    %blend;
T_0.1;
    %vpi_call/w 3 17 "$display", "  [6:0]   ACE_OP: %07b = 0x%02x (%s)", &PV<v0000020b34bf33a0_0, 0, 7>, &PV<v0000020b34bf33a0_0, 0, 7>, S<0,vec4,u64> {1 0 0};
    %vpi_call/w 3 19 "$display", "  [14:12] tuop: %03b = %0d", &PV<v0000020b34bf33a0_0, 12, 3>, &PV<v0000020b34bf33a0_0, 12, 3> {0 0 0};
    %vpi_call/w 3 20 "$display", "  [11:10] vecuop1: %02b = %0d", &PV<v0000020b34bf33a0_0, 10, 2>, &PV<v0000020b34bf33a0_0, 10, 2> {0 0 0};
    %vpi_call/w 3 21 "$display", "  [9]     immen: %b", &PV<v0000020b34bf33a0_0, 9, 1> {0 0 0};
    %vpi_call/w 3 22 "$display", "  [8]     rsen: %b", &PV<v0000020b34bf33a0_0, 8, 1> {0 0 0};
    %vpi_call/w 3 23 "$display", "  [24:20] rs2: %05b = %0d", &PV<v0000020b34bf33a0_0, 20, 5>, &PV<v0000020b34bf33a0_0, 20, 5> {0 0 0};
    %vpi_call/w 3 24 "$display", "  [30:25] memuop: %06b = %0d", &PV<v0000020b34bf33a0_0, 25, 6>, &PV<v0000020b34bf33a0_0, 25, 6> {0 0 0};
    %vpi_call/w 3 25 "$display", "  [28]    neg2: %b", &PV<v0000020b34bf33a0_0, 28, 1> {0 0 0};
    %vpi_call/w 3 26 "$display", "  [27]    neg1: %b", &PV<v0000020b34bf33a0_0, 27, 1> {0 0 0};
    %vpi_call/w 3 27 "$display", "\000" {0 0 0};
    %vpi_call/w 3 30 "$display", "Word 2 \344\275\215\345\237\237\345\210\206\346\236\220:" {0 0 0};
    %vpi_call/w 3 31 "$display", "  [6:0]   ACE_OP: %07b = 0x%02x", &PV<v0000020b34bd8290_0, 0, 7>, &PV<v0000020b34bd8290_0, 0, 7> {0 0 0};
    %vpi_call/w 3 32 "$display", "  [14:12] tuop: %03b = %0d", &PV<v0000020b34bd8290_0, 12, 3>, &PV<v0000020b34bd8290_0, 12, 3> {0 0 0};
    %vpi_call/w 3 33 "$display", "  [19:16] vecuop2[5:2]: %04b = %0d", &PV<v0000020b34bd8290_0, 16, 4>, &PV<v0000020b34bd8290_0, 16, 4> {0 0 0};
    %vpi_call/w 3 34 "$display", "\000" {0 0 0};
    %vpi_call/w 3 37 "$display", "\346\214\207\344\273\244\346\250\241\345\274\217\345\210\206\346\236\220:" {0 0 0};
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 7, 0, 2;
    %cmpi/e 123, 0, 7;
    %jmp/0xz  T_0.2, 4;
    %vpi_call/w 3 39 "$display", "  \342\234\223 \350\277\231\346\230\257\344\270\200\344\270\252TILE\346\214\207\344\273\244" {0 0 0};
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 3, 12, 5;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_0.4, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_0.5, 6;
    %vpi_call/w 3 51 "$display", "  ? tuop_%03b", &PV<v0000020b34bf33a0_0, 12, 3> {0 0 0};
    %jmp T_0.7;
T_0.4 ;
    %vpi_call/w 3 43 "$display", "  \342\234\223 tuop_110 (\345\244\232\350\275\246\351\201\223\345\206\205\345\255\230\346\214\207\344\273\244)" {0 0 0};
    %load/vec4 v0000020b34bd8290_0;
    %parti/s 3, 12, 5;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_0.9, 6;
    %vpi_call/w 3 47 "$display", "  ? \347\254\254\344\272\214\345\255\227tuop_%03b", &PV<v0000020b34bd8290_0, 12, 3> {0 0 0};
    %jmp T_0.11;
T_0.8 ;
    %vpi_call/w 3 45 "$display", "  \342\234\223 \347\254\254\344\272\214\345\255\227tuop_010 (\345\220\221\351\207\217ALU)" {0 0 0};
    %jmp T_0.11;
T_0.9 ;
    %vpi_call/w 3 46 "$display", "  \342\234\223 \347\254\254\344\272\214\345\255\227tuop_000 (\345\206\205\345\255\230\346\223\215\344\275\234)" {0 0 0};
    %jmp T_0.11;
T_0.11 ;
    %pop/vec4 1;
    %jmp T_0.7;
T_0.5 ;
    %vpi_call/w 3 50 "$display", "  \342\234\223 tuop_010 (\345\220\221\351\207\217\346\223\215\344\275\234)" {0 0 0};
    %jmp T_0.7;
T_0.7 ;
    %pop/vec4 1;
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 2, 10, 5;
    %cmpi/e 2, 0, 2;
    %flag_get/vec4 4;
    %jmp/0 T_0.14, 4;
    %load/vec4 v0000020b34bd8290_0;
    %parti/s 4, 16, 6;
    %pushi/vec4 0, 0, 4;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
T_0.14;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.12, 8;
    %vpi_call/w 3 56 "$display", "  \342\234\223 vecuop1=10 + vecuop2[5:2]=0000 => tadd\346\223\215\344\275\234" {0 0 0};
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 1, 8, 5;
    %flag_set/vec4 9;
    %flag_get/vec4 9;
    %jmp/0 T_0.17, 9;
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 1, 9, 5;
    %nor/r;
    %and;
T_0.17;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.15, 8;
    %vpi_call/w 3 58 "$display", "  \342\234\223 rsen=1, immen=0 => ttr\345\217\230\344\275\223" {0 0 0};
    %load/vec4 v0000020b34bf33a0_0;
    %parti/s 1, 28, 6;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.18, 8;
    %vpi_call/w 3 60 "$display", "  \342\234\223 neg2=1 => \345\270\246neg2\344\277\256\351\245\260\347\254\246" {0 0 0};
    %vpi_call/w 3 61 "$display", "  \347\273\223\350\256\272: \350\277\231\345\272\224\350\257\245\346\230\257 tadd.ttr.f32.neg2" {0 0 0};
T_0.18 ;
T_0.15 ;
T_0.12 ;
T_0.2 ;
    %vpi_call/w 3 67 "$finish" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "-";
    "analyze_instruction.sv";
