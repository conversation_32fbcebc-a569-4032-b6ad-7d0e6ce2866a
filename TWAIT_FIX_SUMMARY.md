# Twait Instruction Decompilation Fix

## Problem Description

The instruction `0x7080507B` was being incorrectly decompiled as `unknown_tile` instead of the correct `twait` instruction.

## Root Cause Analysis

### 1. Missing tuop_101 Support
The original `extract_instruction_name` function in `tile_instruction_decoder.sv` was missing support for `tuop_101` (sync operations). The function only handled:
- `tuop_100` (CSR operations)
- `tuop_111` (ACE operations)

But did not handle `tuop_101` (sync operations), which includes `twait`, `tsync`, and `tkill` instructions.

### 2. Incorrect Field Position
Initially, there was confusion about the `waitop` field position. The correct position is `[30:28]`, not `[28:26]` as initially assumed.

## Instruction Analysis

### Binary Breakdown of 0x7080507B
```
0x7080507B = 01110000100000000101000001111011

Field breakdown:
- ACE_OP [6:0]   = 1111011 (0x7B) ✓ Tile instruction
- tuop [14:12]   = 101 (5)        ✓ Sync operations  
- ctrluop [25:23] = 001 (1)       ✓ twait operations
- waitop [30:28] = 111 (7)        ✓ Basic twait
- isMem [26]     = 0              ✓ Core wait (not memory wait)
```

### Instruction Encoding Reference
According to the wavedrom definition in `ace_encode/tuop_101/ctrluop_001/waitop_111/twait.md`:
- `tuop = 101` (sync operations)
- `ctrluop = 001` (twait operations) 
- `waitop = 111` (basic twait)
- `isMem` bit determines if it's `twait` (0) or `twait.mem` (1)

## Solution Implemented

### 1. Added tuop_101 Support
Added a new case in the `extract_instruction_name` function to handle sync operations:

```systemverilog
3'b101: begin // Sync operations (tuop_101)
    logic [2:0] ctrluop;
    logic [2:0] waitop;
    
    ctrluop = instruction_data[25:23];
    waitop = instruction_data[30:28];
    
    if (ctrluop == 3'b001 && waitop == 3'b111) begin
        // Basic twait instruction
        if (instruction_data[26] == 1'b1)
            return "twait.mem";
        else
            return "twait";
    end else begin
        return "unknown_sync";
    end
end
```

### 2. Corrected Field Positions
- `waitop` field: `[30:28]` (not `[28:26]`)
- `ctrluop` field: `[25:23]` 
- `isMem` field: `[26]`

## Verification Results

### Before Fix
```
Instruction: 0x7080507B
Result: unknown_tile
```

### After Fix
```
Instruction: 0x7080507B  
Result: twait
```

### Additional Test Cases
- `0x7080507B` → `twait` ✓
- `0x7480507B` → `twait.mem` ✓ (isMem=1 variant)
- `0x0000407B` → `tcsrw.i 0` ✓ (regression test)

## Files Modified

1. **tile_instruction_decoder.sv**
   - Added `tuop_101` case in `extract_instruction_name` function
   - Fixed field position references
   - Added support for basic `twait` and `twait.mem` instructions

## Future Enhancements

The current fix implements basic `twait` support. For complete sync operation support, the following could be added:

1. **Extended twait variants:**
   - `twait.i.load.global/share` (waitop=000)
   - `twait.r.load.global/share` (waitop=001) 
   - `twait.r.tacp_cg` (waitop=010)
   - `twait.r.rmtfence` (waitop=011)

2. **Other sync operations:**
   - `tsync.i` (ctrluop=000)
   - `tkill.r` (waitop=100)

3. **Operand formatting:**
   - Immediate values for `twait.i.*` instructions
   - Register operands for `twait.r.*` instructions

## Testing

Run the verification test:
```bash
iverilog -g2012 -o final_test tile_instruction_decoder.sv final_test.sv && ./final_test
```

This confirms that:
- ✓ Instruction 0x7080507B is correctly identified as 'twait'
- ✓ No regression in existing functionality
- ✓ twait.mem variant also works correctly
