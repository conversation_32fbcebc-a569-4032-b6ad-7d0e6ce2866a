#! /c/Source/iverilog-install/bin/vvp
:ivl_version "12.0 (devel)" "(s20150603-1539-g2693dd32b)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "C:\iverilog\lib\ivl\system.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_sys.vpi";
:vpi_module "C:\iverilog\lib\ivl\vhdl_textio.vpi";
:vpi_module "C:\iverilog\lib\ivl\v2005_math.vpi";
:vpi_module "C:\iverilog\lib\ivl\va_math.vpi";
S_00000178ae25aa90 .scope module, "simple_tacp_test" "simple_tacp_test" 2 2;
 .timescale 0 0;
v00000178ae45d670_0 .var "ace_op", 6 0;
v00000178ae259b30_0 .var "detected_length", 1 0;
v00000178ae4572a0_0 .var "first_word", 31 0;
v00000178ae433380_0 .var "is_tacp_64bit", 0 0;
v00000178ae259780_0 .var "is_tile", 0 0;
v00000178ae45ab00_0 .var "length_correct", 0 0;
v00000178ae45aba0_0 .var "lsuop", 1 0;
v00000178ae461450_0 .var "memuop", 5 0;
v00000178ae4614f0_0 .var "second_word", 31 0;
v00000178ae461590_0 .var "test_instruction", 63 0;
v00000178ae456ec0_0 .var "tuop", 2 0;
    .scope S_00000178ae25aa90;
T_0 ;
    %pushi/vec4 2382692731, 0, 32;
    %concati/vec4 940499195, 0, 32;
    %store/vec4 v00000178ae461590_0, 0, 64;
    %load/vec4 v00000178ae461590_0;
    %parti/s 32, 0, 2;
    %store/vec4 v00000178ae4572a0_0, 0, 32;
    %load/vec4 v00000178ae461590_0;
    %parti/s 32, 32, 7;
    %store/vec4 v00000178ae4614f0_0, 0, 32;
    %vpi_call 2 18 "$display", "=== Testing tacp.rrr.linear Instruction Fix ===" {0 0 0};
    %vpi_call 2 19 "$display", "Instruction: 0x%016x", v00000178ae461590_0 {0 0 0};
    %vpi_call 2 20 "$display", "First word:  0x%08x", v00000178ae4572a0_0 {0 0 0};
    %vpi_call 2 21 "$display", "Second word: 0x%08x", v00000178ae4614f0_0 {0 0 0};
    %vpi_call 2 22 "$display", "\000" {0 0 0};
    %load/vec4 v00000178ae4572a0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v00000178ae45d670_0, 0, 7;
    %load/vec4 v00000178ae4572a0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v00000178ae456ec0_0, 0, 3;
    %load/vec4 v00000178ae4572a0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v00000178ae45aba0_0, 0, 2;
    %load/vec4 v00000178ae4572a0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v00000178ae461450_0, 0, 6;
    %vpi_call 2 30 "$display", "Field Analysis:" {0 0 0};
    %vpi_call 2 31 "$display", "  ace_op [6:0]   = %07b = %d", v00000178ae45d670_0, v00000178ae45d670_0 {0 0 0};
    %vpi_call 2 32 "$display", "  tuop [14:12]   = %03b = %d", v00000178ae456ec0_0, v00000178ae456ec0_0 {0 0 0};
    %vpi_call 2 33 "$display", "  lsuop [11:10]  = %02b = %d", v00000178ae45aba0_0, v00000178ae45aba0_0 {0 0 0};
    %vpi_call 2 34 "$display", "  memuop [30:25] = %06b = %d", v00000178ae461450_0, v00000178ae461450_0 {0 0 0};
    %vpi_call 2 35 "$display", "\000" {0 0 0};
    %load/vec4 v00000178ae45d670_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v00000178ae259780_0, 0, 1;
    %load/vec4 v00000178ae259780_0;
    %flag_set/vec4 8;
    %jmp/0 T_0.0, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_0.1, 8;
T_0.0 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_0.1, 8;
 ; End of false expr.
    %blend;
T_0.1;
    %vpi_call 2 39 "$display", "Is tile instruction: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v00000178ae259780_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.2, 8;
    %vpi_call 2 42 "$display", "TUOP Analysis:" {0 0 0};
    %vpi_call 2 43 "$display", "  tuop = %d", v00000178ae456ec0_0 {0 0 0};
    %load/vec4 v00000178ae456ec0_0;
    %cmpi/e 6, 0, 3;
    %jmp/0xz  T_0.4, 4;
    %vpi_call 2 46 "$display", "  This is tuop_110 (multi-lane memory instructions)" {0 0 0};
    %vpi_call 2 47 "$display", "  memuop = %d (0x%02x)", v00000178ae461450_0, v00000178ae461450_0 {0 0 0};
    %vpi_call 2 48 "$display", "  lsuop = %d", v00000178ae45aba0_0 {0 0 0};
    %load/vec4 v00000178ae461450_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_0.6, 4;
    %vpi_call 2 51 "$display", "  \342\234\223 This is a tacp operation (memuop = 0x1C)" {0 0 0};
    %load/vec4 v00000178ae45aba0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.9, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.10, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.11, 6;
    %jmp T_0.12;
T_0.8 ;
    %pushi/vec4 1, 0, 2;
    %store/vec4 v00000178ae259b30_0, 0, 2;
    %vpi_call 2 57 "$display", "  \342\234\223 lsuop=00: Linear operations -> Should be 64-bit" {0 0 0};
    %jmp T_0.12;
T_0.9 ;
    %pushi/vec4 1, 0, 2;
    %store/vec4 v00000178ae259b30_0, 0, 2;
    %vpi_call 2 61 "$display", "  \342\234\223 lsuop=01: Stride operations -> Should be 64-bit" {0 0 0};
    %jmp T_0.12;
T_0.10 ;
    %pushi/vec4 2, 0, 2;
    %store/vec4 v00000178ae259b30_0, 0, 2;
    %vpi_call 2 65 "$display", "  \342\234\223 lsuop=10: Index operations -> Should be 96-bit" {0 0 0};
    %jmp T_0.12;
T_0.11 ;
    %pushi/vec4 3, 0, 2;
    %store/vec4 v00000178ae259b30_0, 0, 2;
    %vpi_call 2 69 "$display", "  \342\234\223 lsuop=11: 4-lane operations -> Should be 128-bit" {0 0 0};
    %jmp T_0.12;
T_0.12 ;
    %pop/vec4 1;
    %load/vec4 v00000178ae45aba0_0;
    %cmpi/e 0, 0, 2;
    %flag_get/vec4 4;
    %jmp/1 T_0.13, 4;
    %load/vec4 v00000178ae45aba0_0;
    %pushi/vec4 1, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %or;
T_0.13;
    %store/vec4 v00000178ae433380_0, 0, 1;
    %vpi_call 2 75 "$display", "\000" {0 0 0};
    %vpi_call 2 76 "$display", "=== RESULTS ===" {0 0 0};
    %load/vec4 v00000178ae259b30_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_0.14, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.15, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.16, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.17, 6;
    %jmp T_0.18;
T_0.14 ;
    %vpi_call 2 79 "$display", "Detected length: 32-bit" {0 0 0};
    %jmp T_0.18;
T_0.15 ;
    %vpi_call 2 80 "$display", "Detected length: 64-bit" {0 0 0};
    %jmp T_0.18;
T_0.16 ;
    %vpi_call 2 81 "$display", "Detected length: 96-bit" {0 0 0};
    %jmp T_0.18;
T_0.17 ;
    %vpi_call 2 82 "$display", "Detected length: 128-bit" {0 0 0};
    %jmp T_0.18;
T_0.18 ;
    %pop/vec4 1;
    %load/vec4 v00000178ae259b30_0;
    %pushi/vec4 1, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %store/vec4 v00000178ae45ab00_0, 0, 1;
    %load/vec4 v00000178ae45ab00_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.19, 8;
    %vpi_call 2 89 "$display", "\342\234\223 SUCCESS: Length correctly detected as 64-bit" {0 0 0};
    %jmp T_0.20;
T_0.19 ;
    %vpi_call 2 91 "$display", "\342\234\227 FAILED: Length incorrectly detected" {0 0 0};
T_0.20 ;
    %load/vec4 v00000178ae45aba0_0;
    %cmpi/e 0, 0, 2;
    %jmp/0xz  T_0.21, 4;
    %vpi_call 2 95 "$display", "\342\234\223 SUCCESS: This should be tacp.rrr.linear instruction" {0 0 0};
    %jmp T_0.22;
T_0.21 ;
    %vpi_call 2 97 "$display", "! NOTE: This is a different tacp variant (not linear)" {0 0 0};
T_0.22 ;
    %vpi_call 2 100 "$display", "\000" {0 0 0};
    %vpi_call 2 101 "$display", "=== CONCLUSION ===" {0 0 0};
    %vpi_call 2 102 "$display", "Before fix: This would have been incorrectly detected as 128-bit" {0 0 0};
    %load/vec4 v00000178ae259b30_0;
    %cmpi/e 1, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_0.23, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 54, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 875389545, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1948284001, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1668296306, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1920085612, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1768842593, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 114, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.24, 8;
T_0.23 ; End of true expr.
    %load/vec4 v00000178ae259b30_0;
    %cmpi/e 2, 0, 2;
    %flag_mov 9, 4;
    %jmp/0 T_0.25, 9;
    %pushi/vec4 14646, 0, 32; draw_string_vec4
    %pushi/vec4 761424244, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 544498019, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1881172334, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1684371488, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1869636978, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1635019119, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 110, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.26, 9;
T_0.25 ; End of true expr.
    %load/vec4 v00000178ae259b30_0;
    %cmpi/e 3, 0, 2;
    %flag_mov 10, 4;
    %jmp/0 T_0.27, 10;
    %pushi/vec4 825374765, 0, 32; draw_string_vec4
    %pushi/vec4 1651078176, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1952539504, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 540290412, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1634624800, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1869636978, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1635019119, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 110, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.28, 10;
T_0.27 ; End of true expr.
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 13106, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 761424244, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 539522414, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1702391909, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1668572516, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 41, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.28, 10;
 ; End of false expr.
    %blend;
T_0.28;
    %jmp/0 T_0.26, 9;
 ; End of false expr.
    %blend;
T_0.26;
    %jmp/0 T_0.24, 8;
 ; End of false expr.
    %blend;
T_0.24;
    %vpi_call 2 103 "$display", "After fix:  Now correctly detected as %s", S<0,vec4,u232> {1 0 0};
    %jmp T_0.7;
T_0.6 ;
    %vpi_call 2 110 "$display", "  This is not a tacp operation (memuop != 0x1C)" {0 0 0};
T_0.7 ;
    %jmp T_0.5;
T_0.4 ;
    %vpi_call 2 114 "$display", "  This is not tuop_110" {0 0 0};
T_0.5 ;
    %jmp T_0.3;
T_0.2 ;
    %vpi_call 2 117 "$display", "\342\234\227 Not a tile instruction" {0 0 0};
T_0.3 ;
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "simple_tacp_test.sv";
